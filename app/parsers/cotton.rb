#encoding: utf-8

require 'downloader'
#require 'unicode_utils/downcase'

class Cotton < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}

    super
  end


  def get_cat_id(cat)
    cat_id='850'

    cat_id='870' if cat.include? 'МУЖЧИН'

    cat_id='872' if cat.include? 'МУЖЧИН' and cat.include? 'Трусы'
    cat_id='31' if cat.include? 'МУЖЧИН' and cat.include? 'Брюки'

    cat_id='544' if cat.include? 'ДЕВОЧЕК'
    cat_id='545' if cat.include? 'МАЛЬЧИКОВ'

    cat_id='20' if cat.include? 'ЖЕНЩИН' and cat.include? 'Платья'
    cat_id='19' if cat.include? 'ЖЕНЩИН' and cat.include? 'Юбки'
    cat_id='307' if cat.include? 'ЖЕНЩИН' and cat.include? 'Туники'
    cat_id='23' if cat.include? 'ЖЕНЩИН' and cat.include? 'Белье'
    cat_id='139' if cat.include? 'ЖЕНЩИН' and cat.include? 'Берем'
    cat_id='61' if cat.include? 'ЖЕНЩИН' and cat.include? 'Больших'

    cat_id
  end

  def process_page(url)
    @log.info(url)
    page = @agent.get(url)

    /show.([0-9]+)/=~url
    art=$1

    art2=page.search('.item-code').text.strip

    cat=page.search('.crumps a')[2..3].map(&:text).join ' - '

    cat=page.search('.crumps a')[3..4].map(&:text).join ' - ' if cat.include? 'ДЕТЕЙ'
    cat=page.search('.crumps a')[3..4].map(&:text).join ' - ' if cat.include? 'НОВИНКИ'

    cat_id=get_cat_id(cat)

    colors=Hash.new
    pics=Hash.new
    page.search('#select-colors option').each {|o|
      color_id=o.attr('data-id')
      color=o.text.strip
      colors[color_id]=color
      pics[color_id]=o.attr('data-imagesrc').to_s.gsub('/small','') if o.attr('data-imageind').to_s!=''
    }

    /colorGrowthSizeData = (.*?);/=~page.body
    size_data=JSON.parse($1)

    desc=page.search('.mixture').text.gsub(/\s+/,' ').strip+'. '
    desc+=page.search('.description').text.gsub('Описание:','').gsub(/ +/,' ').strip

    size_data.each {|color_id, d|
      stocks=Hash.new
      prices=Hash.new { |h, k| h[k] = [] }

      t=[]
      d['growths'].each {|grow_id, d2|
        a=d2['available'].to_i
        r=d2['reserved'].to_i
        stock=a-r
        t<<stock if stock>0
      }

      d['sizes'].values.each_with_index {|s,i|
        ts=page.search("#item-size-#{s} .size-size").text.gsub('Размер','').strip
        begin
          ts=ts.to_i
        rescue
        end
        price=page.search("#item-size-#{s} span.on-active-hide").text.strip
        stocks[ts]=t[i]
        prices[price]<<ts
      }

      next if not pics.has_key? color_id

      savePic(pics[color_id],"#{art}~#{color_id}",false)

      col=addCollection(cat,cat_id)
      prices.each {|price,sizes|
        addProduct(col,art,art2+' '+colors[color_id],price,desc,sizes.sort,["#{art}~#{color_id}"])
      }
    }


  end

  def process_cat(url)
    @log.info(url)
    page = @agent.get(url)

    page.search('.catalog-item-block-name a').each {|a|
      process_page(a.attr('href').to_s)
    }


  end

  def run
    page = @agent.get('http://kotton45-shop.ru/personal/profile/')

    login_form=page.form_with(name:'form_auth')
    login_form['USER_LOGIN']=@login
    login_form['USER_PASSWORD']=@password
    page = @agent.submit(login_form, login_form.buttons[0])

    page=@agent.get 'http://kotton45-shop.ru/catalog/'


    cats.each {|url|
      process_cat(url)
    }

  end
  #handle_asynchronously :run

end