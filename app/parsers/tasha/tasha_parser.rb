#encoding: utf-8

require 'downloader'
require 'csv'
#require 'logger'
#require 'unicode_utils/downcase'
include Magick

module Tasha
  class TashaParser < Downloader
    include InvoiceFullname

    attr_accessor :login
    attr_accessor :password
    attr_accessor :stock_file

    def initialize
      @dlparams = { 'login' => :text, 'password' => :password, 'stock_file' => :file }
      @pics = Hash.new

      @stock = []

      super
    end

    def word_wrap(text, columns = 80)
      text.split("\n").collect do |line|
        line.length > columns ? line.gsub(/(.{1,#{columns}})(\s+|$)/, "\\1\n").strip : line
      end * "\n"
    end

    def create_image(text)
      position = 80
      drawing = Draw.new
      ret = []
      word_wrap(text, 90).split("\n").each_slice(35).with_index do |rows, i|
        img = Image.new(1000, 3200)
        rows.each do |row|
          next if row.empty?
          drawing.annotate(img, 0, 0, 200, position += 20, row)
        end
        img.trim!
        img.write("/tmp/tasha~desc~#{i}.png")
        ret << "/tmp/tasha~desc~#{i}.png"
      end
      ret
    end

    def process_page_site(url)
      puts url
      p = @agent.get url
      d = JSON.parse p.body

      html = d['html']['product_quick_view']
      h = Nokogiri::HTML(html)

      pic = h.search('a.cm-image-previewer')
      url = pic.attr('href').to_s

      art = h.search('span[id^=product_code_ajax]').text.gsub('-00', '')
      a = art.strip
      return if a == ''
      @pics[a] = url
      puts a
    end

    def process_cat_site(url)
      p = @agent.get url
      p.search('input[type=hidden][name^=product_data]').each do |inp|
        pid = inp.attr('value').to_s
        process_page_site "http://tashabeauty.ru/index.php?dispatch=products.quick_view&product_id=#{pid}&result_ids=product_quick_view&skip_result_ids_check=true&is_ajax=1"
      end
    end

    def processPage(url)
      @log.info url
      puts url

      #https://www.b2b.tashabeauty.ru/front_api/cart.json
      #lang:
      # _method: patch
      # variant_ids[211454737]: -90
      #
      page = @agent.get(url)

      if page.search('a.breadcrumb-link').count > 4
        cat = page.search('a.breadcrumb-link')[3].text.to_s.strip
      else
        cat = page.search('a.breadcrumb-link').last.text.to_s.strip
      end

      name = page.search('h1.page-headding').text.strip
      #puts name

      art = page.search('span.js-product-sku').text.strip

      min_order = 1
      if page.at('.js-variant-counter')
        min_order = page.at('.js-variant-counter').attr('data-min').to_s.to_i
      end

      desc = page.search('#product-description').text.strip

      price = page.at('.js-product-price').text.gsub(/[^0-9.,]/, '').strip.to_i

      rrp=(price.to_f*2.1).round(-1)

      sizes = ["Заказ кратно #{min_order}"]

      if name.downcase.include? 'мыло' and not name.include? 'тортик' and price > 700
        price = (price / 10 * 1.1).ceil
        rrp = (rrp / 10 * 1.1).ceil
        sizes = ['Цена за 100 г.']
      end

      if /(\d) ?кг/i =~ name
        kg = $1.to_i
        price *= kg
        rrp *= kg
      end

      if name.downcase.include? 'тортик' and price > 700
        sizes = ['-']
      end
      #puts price

      pics = []
      if page.at('a[data-image-large]')
        page.search('a[data-image-large]').each_with_index { |img, i|
          pic = img.attr('data-image-large')
          pics << savePic(pic, "#{art}~#{i}", true)
        }
      else
        page.search('.gallery-main-wrapper a').each_with_index { |img, i|
          pic = img.attr('href')
          pics << savePic(pic, "#{art}~#{i}", true)
        }
      end

      col = addCollection(cat, '2325')
      desc.gsub!('Описание:', '')
      desc.gsub!(/Объем:.*? мл/, '')
      desc.gsub!(/Вес: .*? гр/, '')
      desc.gsub!(/Масса: .*? гр/, '')
      desc.gsub!(/Артикул:\s*[A-Z0-9]+/, '')

      desc = "#{desc}. #{@extra_desc[name.downcase].to_s}"
      desc = "#{@vol[name.downcase]}. #{desc}" if @vol[name.downcase]
      desc.gsub!(/\s+/, ' ')
      desc.gsub!(/\.+/, '.')

      p = @purchase.find_product_by_art(art)

      pics = nil if p&.pictures && p.pictures.count > 0

      p = addProduct(col, art, name, price, desc, sizes, pics, nil, 0, nil, rrp: rrp, source: url, brand_name: 'Tasha')
      unless p.weight
        p.weight = ChatGpt.new.get_product_weight(p.name+'. '+p.desc.to_s[0..100])
        p.save
      end

    end

    def processCat(href)
      @log.info href

      page = @agent.get(href + '?page_size=96')

      page.search(".product-card-wrapper a.product-link").each do |a|
        processPage(a.attr('href').to_s)
      end
    end

    def read_stock(f)
      CSV.foreach(f, :col_sep => ';', :headers => true, :encoding => 'Windows-1251') do |fields|
        art = fields[1]
        q = fields[3]
        @stock << art if q.to_i > 0
      end

    end

    def process_retail_page(href)
      page = @agent.get href
      page.body.force_encoding 'utf-8'

      return if page.at('script[type="application/ld+json"]').nil?

      script = page.at('script[type="application/ld+json"]').text
      data = JSON.parse(script)

      art = data['sku']
      return if art.to_s.strip == ''

      name = data['name'].strip.downcase

      desc = data['description']
      desc.gsub!(/Арт.*?:.*?[A-Z0-9]+/, '')

      if /Объем:.*?([0-9 а-я.]+)/ =~ page.body
        vol = "Объем #{$1}"
      elsif /Вес:.*?([0-9 а-я.]+)/ =~ page.body
        vol = "Вес #{$1}"
      elsif /Масса:.*?([0-9 а-я.]+)/ =~ page.body
        vol = "Вес #{$1}"
      end

      if art and vol
        @vol[name.downcase] = vol.strip
      end

      desc.gsub!(/[\n\r]/, ' ')
      desc.gsub!(/ +/, ' ')
      @extra_desc[name.downcase] = desc

    end

    def process_retail_cat(href)
      page = @agent.get href
      page.search('.product-preview__title a').each do |a|
        process_retail_page a.attr('href').to_s
      end
      if page.at('a.pagination-next')
        process_retail_cat(page.at('a.pagination-next').attr('href').to_s)
      end
    end

    def load_info_from_retail_site
      page = @agent.get 'https://tashabeauty.ru/collection/tasha-new'
      page.search('ul.sidebar-collections__submenu a').each do |a|
        process_retail_cat a.attr('href').to_s
      end
    end

    def run

      #read_stock(@stock_file) unless @stock_file.nil?

      @vol = {}
      @extra_desc = {}
      load_info_from_retail_site

      page = @agent.get 'https://www.b2b.tashabeauty.ru/'

      page.search('ul[data-menu-id=sidebar-menu] li.level-1 a.level-1').each do |a|
        processCat(a.attr('href'))
      end

      #page=@agent.get 'http://tashabeauty.ru/ru/'

      #page.search('a.drop').each do |a|
      #      process_cat_site a.attr('href').to_s+'?items_per_page=200'
      #    end

      #page = @agent.get('http://b2b.tashabeauty.ru/login/')

      #login_form=page.forms[0]
      #login_form.username=@login
      #login_form.password=@password

      #page = @agent.submit(login_form, login_form.buttons.first)

      #page=@agent.get 'http://b2b.tashabeauty.ru/catalog/categorylist?format=json'
      #data=JSON.parse(page.body)

      #data['data'].each {|cat|

      # pages=processCat(cat['href'],true)
      # puts pages
      # if pages>1
      #(2..pages).each {|i|
      #     processCat("#{cat['href']}?page=#{i}",false)
      #   }
      #      end
      #}

    end
  end
end