class UpdateAqsiProducts
  API_KEY = 'TV1eCByij178b7SHYth7ORr2nXuLKU1g9fZBX9PyG6J4VTpPFavGJcbPky1MGN09'

  def update_folders
    folders = []
    InventoryFolder.all.each_with_index do |f,idx|
      next if f.inventory_products.count==0

      f2 = {
        id: f.id.to_s,
        name: f.name,
        number: idx,
        isDefault: false,
        defaultSubject: 1,
        defaultTax: 6,
        defaultUnit: 'шт',
        defaultPaymentMethodType: 1
      }
      folders << f2
    end

    data = {
      removeObsolete: false,
      nonAtomic: false,
      payload: folders
    }
    fname='/tmp/file.gz'
    fname='e:/file.gz' if Rails.env.development?

    Zlib::GzipWriter.open(fname) do |gz|
      gz.write data.to_json
    end

    agent = Mechanize.new
    #agent.log = Logger.new('/tmp/log.txt')

    res = agent.post "https://api.aqsi.ru/pub/v2/ListGoodsCategories", { file: File.new(fname) }, { 'x-client-key' => 'Application ' + API_KEY }
    puts res.body
  end

  def load_folders
    agent = Mechanize.new
    res = agent.get "https://api.aqsi.ru/pub/v2/GoodsCategory/list", [], nil, { 'x-client-key' => 'Application ' + API_KEY }
    puts res.body
    data = JSON.parse(res.body)
    @folders = {}
    data.each do |f|
      @folders[f['name']] = f['id']
    end

  end

  def update_products
    @goods = []
    InventoryProduct.all.each do |p|
      next unless p.stock
      p.stock.each do |d|
        s = d['size']
        next if d['retail_price'].to_f <= 0

        if s == '-'
          name = p.name
        else
          name = "#{p.name}, #{s}"
        end

        name = name[0..127]

        group_id = '704a4315-2bc6-41c7-8716-7490793af7fc'
        group_id = @folders[p.inventory_folder.name] if @folders[p.inventory_folder.name]

        p2 = {
          id: "#{p.id}_#{s}",
          group_id: group_id,
          type: 'simple',
          tax: 6,
          unit: 'шт',
          subject: 1,
          name: name,
          sku: p.sku,
          price: d['retail_price'].to_f,
          barcodes: [d['barcode']],
          paymentMethodType: 1
        }
        @goods << p2
      end
    end

    data = {
      removeObsolete: false,
      nonAtomic: false,
      payload: @goods
    }

    fname='/tmp/file.gz'
    fname='e:/file.gz' if Rails.env.development?

    Zlib::GzipWriter.open(fname) do |gz|
      gz.write data.to_json
    end

    agent = Mechanize.new
    #    agent.log = Logger.new('/tmp/log.txt')

    res = agent.post "https://api.aqsi.ru/pub/v2/ListGoods", { file: File.new(fname) }, { 'x-client-key' => 'Application ' + API_KEY}
    puts res.body
  end

  def run
    update_folders
    sleep(30)
    load_folders
    update_products
  end
end