module Alisa
  class AlisaParser < Downloader
    def initialize
      @dlparams = {'price_file1'=>:file,'price_file2'=>:file,'price_file3'=>:file}

      super
    end

    def process_page(url)
      tries ||= 2

      @log.info(url)
      page = @agent.get(url)
      puts url

      category_on_web = page.search('ol.breadcrumb li')[2].text
      name = page.search('div.product-detail h1').text.gsub(/\s+/,' ').strip
      vendor_code = ''
      description = ''
      h=page.search('h4').select{|h| h.text.strip=='Описание товара' and h.next_element.name=='p'}.first
      description=h.next_element.text.strip if h

      characteristics = page.search('table.table-striped td').each {|row|
        if row.text.include? 'Артикул:'
          vendor_code = row.text.sub('Артикул:', '').strip
        else
          #description += row.text.gsub(/\s+/,' ').strip+' ' unless row.text.include? '
        end
      }

      puts vendor_code

      price = @reader.get_price(vendor_code)
      puts price

      return if price.nil?

      category = @reader.get_category(vendor_code).gsub(/\s+/,' ').strip

      col_type=guess_cat_type(category,name,'894')

      col = addCollection(category,col_type)

      pics=[]
      page.search('div.product-detail > div').each_with_index {|div,i|
        unless div.at('a').nil?
          image = div.at('a').attr('href').to_s
          fn="#{vendor_code}~#{i}"
          savePic(image,fn,true)
          pics<<fn
        end
      }

      addProduct(col, vendor_code, name, price, description,[], pics)

    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_toys_list(href)
      page = @agent.get(href)

      page.search('div.fine-list-products > div.media > div.media-body > h4.media-heading > a').each {|a|
        process_page(a.attr('href'))
      }
    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href)
      page.search('section.fine-list-catalog > article > ul.nav > li a').each_with_index {|a, index|
        process_toys_list(a.attr('href').to_s)
      }
    end

    def run
      @reader = ExcelReader.new
      @reader.init_worksheet(@price_file1, 0)
      @reader.init_categories_prices

      @reader.init_worksheet(@price_file2, 0)
      @reader.init_categories_prices

      @reader.init_worksheet(@price_file3, 0)
      @reader.init_categories_prices

      page = @agent.get 'http://alisatoys.ru/catalog'

      page.search("div.tab-content ul.nav-stacked > li > a").each_with_index {|a, index|
        process_cat(a.attr('href').to_s)
      }
    end

  end
end