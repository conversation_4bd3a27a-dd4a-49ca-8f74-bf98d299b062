#encoding: utf-8
module Belsavon
  class BelsavonOrderSender< Framework::OrderSender

    def order(articul, quantity)
      debug_func "#{articul}, #{quantity}"

      begin
        #@agent.log=Logger.new('/tmp/belsavorder.txt')
        @agent.redirect_ok = false
        page=@agent.get("https://sochi-rose.ru/product/view/#{articul}")
      rescue Mechanize::ResponseCodeError => e
        if e.response_code == '404'
          add_error_csv_data('Page url open error', articul)
          return
        else
          raise
        end

      end

      stock=page.at('span#product_qty').text.to_s
      stock=1000000 if stock=='Неограничено'
      stock=stock.to_i
      quantity=stock if quantity>stock

      return if quantity==0

      desc=page.search('.jshop_prod_description').text.strip
      if /Цена указана за (\d+) штук/=~desc
        quantity=quantity/$1.to_i+1
      end

      (c_id,p_id)=articul.split('/')

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

      params = {quantity: quantity,to: 'cart',product_id: p_id, category_id: c_id}
      puts params
      page=@agent.post('https://sochi-rose.ru/cart/add', params, ajax_headers)

      #sleep(0.5+rand(0.7))
    end


  end
end
