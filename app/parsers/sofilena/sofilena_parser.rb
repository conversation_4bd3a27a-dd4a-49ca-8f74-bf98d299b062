#encoding: utf-8

module <PERSON><PERSON><PERSON>
  class SofilenaParser < Downloader
    attr_accessor :price_file

    def initialize
      @price_file=''
      @dlparams={'price_file'=>:file}

      @prices={}
      @sizes={}
      super
    end


    def process_page(url)
      @log.info(url)
      page = @agent.get(url)

      script = page.body.match(/JCCatalogElement\((.+)\);/).captures[0].to_s
                   .gsub('\'', '"').gsub(' ', ' ')#.gsub(/\s/, '')
      # puts product_page.body
      data = JSON.parse(script)
      pics={}
      data['OFFERS'].each {|o|
        #puts o
        next unless o['DETAIL_PICTURE']
        pics[o['DETAIL_PICTURE']['TITLE']]=o['DETAIL_PICTURE']['SRC']
      }
      pic_urls=pics.values

      code=data['PRODUCT']['ID']
      name=data['PRODUCT']['NAME']

      return unless @prices[name]

      price=@prices[name]
      sizes=@sizes[name]
      return if sizes.nil?

      cat=name.gsub(/ .*/,'')

      desc=page.search('.detail_text').text
      desc.gsub!('Внимание!','')
      desc.gsub!('Модель в ограниченном количестве.','')
      desc.gsub!(/[\r\n]+/,'.')
      desc.gsub!(/\.+ */,'. ')
      desc.gsub!(/ +/,' ')

      cat_type='850'
      cat_type='102165' if cat.include? 'Блуз'
      cat_type='17' if cat.include? 'Брюки'
      cat_type='102179' if cat.include? 'Жакет'
      cat_type='102180' if cat.include? 'Жилет'
      cat_type='26' if cat.include? 'Комбинезон'
      cat_type='2105' if cat.include? 'Комплект'
      cat_type='102181' if cat.include? 'Кофта'
      cat_type='102186' if cat.include? 'Куртка'
      cat_type='286' if cat.include? 'Накидка'
      cat_type='129' if cat.include? 'Пальто'
      cat_type='1101' if cat.include? 'Платье'
      cat_type='102164' if cat.include? 'Рубашка'
      cat_type='1104' if cat.include? 'Сарафан'
      cat_type='102185' if cat.include? 'Свитшот'
      cat_type='307' if cat.include? 'Туника'
      cat_type='1373' if cat.include? 'Футболка'
      cat_type='19' if cat.include? 'Юбка'

      pics=[]
      pic_urls.each_with_index {|url,i|
        fn="#{code}~#{i}"
        savePic(url,fn,false)
        pics<<fn
      }

      col=addCollection(cat,cat_type)
      addProduct(col,code,name,price,desc,sizes,pics)
    end

    def process_good_list(page)
      page.search('.items .item-titles a').each {|a|
        href=a.attr('href').to_s
        name=a.search('span').text.strip

        process_page(href) if @prices.has_key? name
      }

    end

    def process_cat(href)
      puts "Cat #{href}"
      page=@agent.get href

      process_good_list(page)

      a=page.search('.module-pagination .nums a')[-1]
      if a
        h=a.attr('href').to_s
        if /(\d+)$/=~h
          last_page=$1.to_i
          (2..last_page).each {|p|
            puts "#{href}?PAGEN_1=#{p}"
            page2=@agent.get "#{href}?PAGEN_1=#{p}"
            process_good_list(page2)
          }
        end
      end
    end

    def process_file(f)
      book = Spreadsheet.open f

      sheet1 = book.worksheet 0

      sheet1.rows[9..-1].each do |row|
        next if row[3].nil?
        name=row[0]
        (size,color,code)=row[3].split(', ')
        price=row[7].to_f
        price=row[9].to_f*1.2 if row[9] and row[9].to_f*1.2<price
        @prices[name]=price
        @sizes[name]=[] unless @sizes[name]
        @sizes[name]<<"#{color}-#{size}"
      end
    end


    def run
      if @agent==nil then before end

      process_file(@price_file)

      page=@agent.get 'http://sofilena.ru/catalog/'
      page.search('.container .items .item li.name a').each {|a|
        process_cat(a.attr('href').to_s)
      }

    end
  end
end
