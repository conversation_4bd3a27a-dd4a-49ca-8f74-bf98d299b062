#encoding: utf-8

module InvoiceSima
  extend ActiveSupport::Concern

  def unprocessed_invoice?
    true
  end

  def read_invoice(file)
    ret=[]
    workbook = Spreadsheet.open(file)
    sheet = workbook.worksheet(0)

    code_num=get_col_number(sheet,'код')
    name_num=get_col_number(sheet,'товары (работы, услуги)')
    q_num=get_col_number(sheet,'Кол-во')
    price_num=get_col_number(sheet,"Цена\n(со скидкой)")

    raise "Bad file" if not code_num or not name_num or not q_num

    invoice_name=nil
    in_data=false
    sheet.each do |row|
      next if row.nil?

      invoice_name=row[1].gsub(/Договор.*/,'').strip if row[1].to_s.include? 'Счет на оплату' or row[1].to_s.include? 'Заказ '

      next if row[code_num].blank?


      if row[code_num]=='Код'
        in_data=true
        next
      end

      next unless in_data

      art=row[code_num].to_s.strip.gsub(/\.0$/,'')
      name=row[name_num]
      q=row[q_num].to_i

      price='0'
      price=row[price_num].to_s if price_num

      ret<<{'Артикул'=>art,'Название'=>name,'Количество'=>q, 'Цена закуп'=>price,'#invoice_name'=>invoice_name,'Размер'=>'-'}
    end
    ret
  end

  def transform_invoice(fields)
    @log = Logger.new('log/sima-confirm.log')
    ret = []

    purchase_ids=Purchase.where(dlclass:'Simaland::SimalandParser').map {|p| p.id}

    fields.each do |f|
      @log.info(f['Артикул'].strip)
      #products = Product.where(purchase_id:purchase_ids).where("art like 's%' and extra like '%#{f['Артикул'].strip}%'")
      products = Product.where("art like 's%' and extra like '%#{f['Артикул'].strip}%'")
      transforms = 0
      if products.count > 0
        added = Set.new
        products.each do |p|
          next unless purchase_ids.include? p.purchase_id

          begin
            @log.info(p.extra)
            extra = JSON.parse(p.extra)
            extra['size_data'].each do |s, real_art|
              next if added.include? real_art
              a = f['Артикул'].strip
              if real_art.to_s == a
                f['Артикул'] = p.art
                s = '-' if s.blank?
                f['Размер'] = s
                ret << f
                added << real_art
                transforms += 1
              end
            end
          rescue
          end
        end

      end
      ret << f if transforms == 0
    end

    filename = Rails.root.join("tmp", "#{DateTime.now.strftime('%Y-%m-%d %h-%s')}.csv")

    CSV.open(filename, "wb") do |csv|
      # Add header row
      csv << ret.first.keys

      # Add data rows
      ret.each do |hash|
        csv << hash.values
      end
    end
    ret


  end

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line, fields)
    site_art = fields['Артикул'].upcase.strip.gsub(/--\d+$/,'')
    art = line['Артикул'].upcase.strip

    if line['Размер'] && line['Размер']!='-' && fields['Размер'] && art.start_with?('s')
      art == site_art && line['Размер'] == fields['Размер'].split('@')[0]
    else
      art == site_art
    end
  end

end