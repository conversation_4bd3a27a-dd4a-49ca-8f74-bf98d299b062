#encoding:utf-8

module Lukumrf
  class LukumrfOrderSender < Framework::OrderSender

    def clean
      # @agent.get('https://kristaller.pro/personal/cart/index.php?BasketClear=Y')
    end

    def order(articul, quantity)
      puts articul, quantity

      begin
      page=@agent.get "http://opt.lukumrf.ru/products/#{articul}"
      rescue Mechanize::ResponseCodeError => exception
        if exception.response_code == '404'
          add_error_csv_data('Page url open error', articul)
          return
        end
      end

        #return if page.search('link[itemprop=availability]').count==0

      token=page.search('input[name=__RequestVerificationToken]').attr('value').to_s
      ne
      offer_id=page.search('div[data-start-offer-id]').first.attr('data-start-offer-id').to_s.to_i
      product_id=page.search('div[data-product-id]').first.attr('data-product-id').to_s.to_i

      json = {
          :amount => quantity,
          :offerId => offer_id,
          :productId => product_id
      }
      ajax_headers = { 'Content-Type'=> 'application/json;charset=UTF-8','X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*',
                       '__RequestVerificationToken'=> token}
      res=@agent.post('http://opt.lukumrf.ru/cart/addToCart',json.to_json,ajax_headers)
      sleep(rand(0.3)+0.3)
    end
  end
end