import { _ as _export_sfc, d as defineComponent, i as ref, l as onMounted, L as onUnmounted, $ as api, o as openBlock, R as createBlock, S as withCtx, W as createBaseVNode, T as createVNode, m as QBtn, a as createTextVNode, t as toDisplayString, V as QInput, Q as QIcon } from "./index.f4b41209.js";
import { Q as QTable } from "./QTable.dd43f75f.js";
import { Q as QPage } from "./QPage.c348e993.js";
import "./QSelect.33e940a7.js";
import "./QField.b3da084d.js";
function translit(word) {
  var converter = {
    \u0430: "a",
    \u0431: "b",
    \u0432: "v",
    \u0433: "g",
    \u0434: "d",
    \u0435: "e",
    \u0451: "e",
    \u0436: "zh",
    \u0437: "z",
    \u0438: "i",
    \u0439: "y",
    \u043A: "k",
    \u043B: "l",
    \u043C: "m",
    \u043D: "n",
    \u043E: "o",
    \u043F: "p",
    \u0440: "r",
    \u0441: "s",
    \u0442: "t",
    \u0443: "u",
    \u0444: "f",
    \u0445: "h",
    \u0446: "c",
    \u0447: "ch",
    \u0448: "sh",
    \u0449: "sch",
    \u044C: "",
    \u044B: "y",
    \u044A: "",
    \u044D: "e",
    \u044E: "yu",
    \u044F: "ya"
  };
  word = word.toLowerCase();
  var answer = "";
  for (var i = 0; i < word.length; ++i) {
    if (converter[word[i]] == void 0) {
      answer += word[i];
    } else {
      answer += converter[word[i]];
    }
  }
  return answer;
}
function translitToR(word) {
  const converter = {
    sch: "\u0449",
    yo: "\u0451",
    zh: "\u0436",
    ch: "\u0447",
    sh: "\u0448",
    yu: "\u044E",
    ya: "\u044F",
    a: "\u0430",
    b: "\u0431",
    v: "\u0432",
    g: "\u0433",
    d: "\u0434",
    e: "\u0435",
    z: "\u0437",
    i: "\u0438",
    y: "\u0439",
    k: "\u043A",
    l: "\u043B",
    m: "\u043C",
    n: "\u043D",
    o: "\u043E",
    p: "\u043F",
    r: "\u0440",
    s: "\u0441",
    t: "\u0442",
    u: "\u0443",
    f: "\u0444",
    h: "\u0445",
    c: "\u0446"
  };
  for (const [key, value] of Object.entries(converter)) {
    word = word.replaceAll(key, value);
  }
  return word;
}
var IndexPage_vue_vue_type_style_index_0_scoped_true_lang = "";
const _sfc_main = defineComponent({
  name: "IndexPage",
  async setup() {
    const purchaseList = ref([]);
    const columns = [
      { name: "id", field: "id", sortable: true },
      { name: "name", field: "name", label: "\u0417\u0430\u043A\u0443\u043F\u043A\u0430", align: "left" },
      { name: "is_pp", field: "is_pp", label: "\u042D\u0442\u043E \u041F\u041F \u043F\u043E\u043A\u0443\u043F\u043A\u0430", align: "left", sortable: true, format: (val, row) => val ? "\u0414\u0430" : "\u041D\u0435\u0442" },
      { name: "pid", field: "sp_pid", label: "pid", align: "left", sortable: false, format: (val, row) => val.join(",") },
      { name: "priority", field: "priority", label: "\u041F\u0440\u0438\u043E\u0440\u0438\u0442\u0435\u0442", align: "left" },
      {
        label: "\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u0437\u0430\u0432\u0435\u0440\u0435\u0448\u0435\u043D\u0430",
        name: "downloaded_at",
        field: "downloaded_at",
        sortable: true,
        sort: (a, b, rowA, rowB) => Date.parse(a) - Date.parse(b),
        format: (val, row) => new Date(val).toLocaleString()
      },
      {
        label: "\u041E\u0448\u0438\u0431\u043A\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0438",
        name: "error",
        field: "error",
        format: (val, row) => val ? "\u0414\u0430" : "\u041D\u0435\u0442"
      }
    ];
    const serarchField = ref(null);
    onMounted(() => {
      window.onkeydown = function(event) {
        if (event.ctrlKey && event.keyCode == 70) {
          event.preventDefault();
          serarchField.value.focus();
          window.scrollTo(0, serarchField.value.offsetTop);
        }
      };
    });
    onUnmounted(() => {
      window.onkeydown = null;
    });
    let res = await api.get("/api3/purchases");
    purchaseList.value = res.data;
    console.log(res.data);
    function filterPurchases(rows, filter) {
      filter.toLowerCase();
      let frte = translit(filter.toLowerCase());
      translitToR(filter.toLowerCase());
      return rows.filter((p) => {
        let n = p.name.toLowerCase();
        return n.includes(filter) || translit(n).includes(frte) || translitToR(n).includes(frte) || p.sp_pid?.toString().includes(filter);
      });
    }
    return {
      purchaseList,
      columns,
      search: ref(""),
      filterPurchases,
      serarchField,
      pagination: ref({
        sortBy: "downloaded_at",
        descending: true,
        rowsPerPage: 0
      })
    };
  },
  methods: {}
});
const _hoisted_1 = {
  style: { "width": "100%" },
  class: "row"
};
const _hoisted_2 = { class: "col-12" };
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createBlock(QPage, { padding: "" }, {
    default: withCtx(() => [
      _cache[1] || (_cache[1] = createBaseVNode("h2", null, "\u041C\u043E\u0438 \u043F\u043E\u043A\u0443\u043F\u043A\u0438", -1)),
      createVNode(QTable, {
        rows: _ctx.purchaseList.purchases,
        columns: _ctx.columns,
        "hide-pagination": "",
        "binary-state-sort": "",
        filter: _ctx.search,
        pagination: _ctx.pagination,
        "filter-method": _ctx.filterPurchases
      }, {
        "body-cell-name": withCtx((props) => [
          createBaseVNode("td", null, [
            createVNode(QBtn, {
              flat: "",
              "no-caps": "",
              to: "/purchase/" + props.row.id
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(props.row.name), 1)
              ]),
              _: 2
            }, 1032, ["to"])
          ])
        ]),
        top: withCtx(() => [
          createBaseVNode("div", _hoisted_1, [
            createBaseVNode("div", _hoisted_2, [
              createVNode(QInput, {
                ref: "serarchField",
                label: "\u041F\u043E\u0438\u0441\u043A",
                outlined: "",
                dense: "",
                debounce: "400",
                color: "primary",
                clearable: "",
                modelValue: _ctx.search,
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.search = $event)
              }, {
                append: withCtx(() => [
                  createVNode(QIcon, { name: "search" })
                ]),
                _: 1
              }, 8, ["modelValue"])
            ])
          ])
        ]),
        _: 1
      }, 8, ["rows", "columns", "filter", "pagination", "filter-method"])
    ]),
    _: 1
  });
}
var IndexPage = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a71f1054"]]);
export { IndexPage as default };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
