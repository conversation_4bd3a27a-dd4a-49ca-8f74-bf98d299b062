#encoding:utf-8
require_relative '../framework/parser'
require 'htmlentities'
module <PERSON><PERSON>cci
  class VelicciParser < Framework::<PERSON><PERSON><PERSON>
    def initialize_settings
      with_auth_data(->(ad) {
        ad.with_auth_url('http://www.velicci.ru/partners/')
            .with_login('<EMAIL>')
            .with_password('6288826')
            .with_login_field_name('email')
            .with_password_field_name('password')
            .with_form(->(page) {
              page.form_with(:id => 'wholesaleLogin')
            })
      })
      with_url('http://www.velicci.ru/shop/31/')
          .with_category_links('.menu-main a')
          .with_category_links('http://www.velicci.ru/shop/31/', true)
          .with_category(->(category_page) {
            prepare_string category_page.search('h1').first.text
          })
          .with_product_config(->(pc) {
            pc.with_product_selector('.catalogue-list > .item > a')
                .with_articul(->(product_page) {
                  parts = product_page.search('#codeline > span').first.text.split('-')
                  prepare_string parts.take(parts.size-1).join '-'
                })
                .with_name(->(product_page) {
                  prepare_string product_page.search('h1').first.text
                })
                .with_sizes(->(product_page) {
                  product_page.search('.sub-size').map { |div| prepare_string div.text }
                })
                .with_price(->(product_page) {
                  product_page.search('[name="price"]').first.attr('value').to_s.to_f.round(2)
                })
                .with_description(->(product_page) {
                  result = product_page.search('.description p').map { |p|
                    prepare_string HTMLEntities.new.decode(p.text)
                  }.join '. '
                  prepare_string result.gsub('..', '.')
                })
                .with_images(->(product_page) {
                  product_page.search('.mini .cloud-zoom-gallery').map { |img| img.attr('href').to_s }
                })
                .with_image_config(->(ic){ic.with_default_url('http://www.velicci.ru')})
                .with_unique(->(product){
                  return false if product.price == 0
                  not ProductNew.exists?(purchase_id: @purchase.id, sku: product.articul)
                })
                .with_category_type('850')
                .with_category_type('15', 'блузы')
                .with_category_type('19', 'юбки')
                .with_category_type('20', 'платья')
                .with_category_type('1104', 'сарафаны')
          })
    end
  end
end