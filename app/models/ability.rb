# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    user ||= User.new # guest user (not logged in)
    #can :manage, User, id:user.id

    can :manage, SpPurchase, company:user.company
    can :manage, SpPurchase, user:user

    can :manage, Purchase, company:user.company
    can :manage, Purchase, user:user
    can :manage, Collection, purchase:{ company:user.company}
    can :manage, Product, purchase:{ company:user.company}

    can :manage, InventoryDocument, user:user
    can :manage, InventoryProduct, user:user
    can :manage, InventoryFolder, user:user
    can :manage, InventoryDocumentLine, user:user
    can :manage, InventoryPicture, user:user

    can :manage, SimaCategory

    if user and user.role.to_s=='dev'
      can :read, Purchase, developer: user
      can :read, Purchase, developer: nil, company: user.company, dev_state:Purchase.dev_states[:new_task]
      can :read, Purchase, developer: nil, company: user.company, dev_state:Purchase.dev_states[:need_fix]

    end
    # Define abilities for the passed in user here. For example:
    #
    #   user ||= User.new # guest user (not logged in)
    #   if user.admin?
    #     can :manage, :all
    #   else
    #     can :read, :all
    #   end
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/wiki/Defining-Abilities
  end
end
