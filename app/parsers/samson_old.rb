#encoding: utf-8
#encoding: utf-8

require 'downloader'

class SamsonOld < Downloader
  include TableUtils
  include Invoice<PERSON>amson

  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams = { 'login' => :text, 'password' => :password, 'price_file' => :file }
    @real_price = {}
    @retail_price = {}
    @base_price = {}
    @barcode = {}
    @purchase_tags = ['Лайма', 'Брауберг канц', 'Хатбер', 'Рюкзаки', 'Дети', 'Хозы', 'Офис']
    super
  end

  def sale_collection_name
    'РАСПРОДАЖА'
  end

  def get_sku(product)
    "SAMS#{product.art}"
  end

  def read_sale(book)
    sheet = book['Снижение цен']

    sheet.sheet_data.rows[3..-1].each do |row|
      price = row[4].value
      rate = row[5].value.to_i
      next if rate.nil?
      art = row[0].value.to_s
      #puts art
      next if rate < 25

      q = 1.15
      q = 1.25 if rate > 40

      @sale_price[art] = (price * q).ceil
    end
  end

  def read_json url
    @log.info url
    page = @agent.get url

    js = JSON.parse(page.body)
    #js=JSON.parse(File.open(file).read)

    js['data'].each { |prod|
      @pool.post do
        process_item prod
      end
    }

    #puts js['meta']['pagination']['next']
    read_json js['meta']['pagination']['next'] if js['meta']['pagination']['next']
  end

  def use_local_pics
    path = '/home/<USER>/samson_pics/x'

    pics = Hash.new
    Dir.glob(path + '/**/*.jpg').each do |f|

      t = f.gsub(path + '/', '')
      #puts t
      s = t.split '_'
      art = s[0]
      #puts art

      if pics.has_key? art
        pics[art] << f
        pics[art].sort!
      else
        pics[art] = [f]
      end
    end

    #puts pics

    @pics[art] = files
  end

  def process_item(item)

    art = item['sku'].to_s

    barcode = item['barcode']

    brand = item['brand']
    @used_brands << brand unless @used_brands.include? brand

    desc = item['description']
    desc += ' ' + item['characteristic_list'].reject { |el| el.include? 'Количество в наборе' }.map { |el| el.gsub(/[. ]+$/, '') }.join('. ') unless item['characteristic_list'] == ''

    pic_urls = item['photo_list']

    package_list = item['package_list'].map { |el| [el['type'], el['value']] }.to_h
    price_list = item['price_list'].map { |el| [el['type'], el['value']] }.to_h
    stock_list = item['stock_list'].map { |el| [el['type'], el['value']] }.to_h

    minorder = package_list['min_opt'].to_i

    #desc += ". Упаковка #{package_list['min_opt']} шт"
    desc.gsub(/\.+/, '.')

    price = price_list['contract'].to_f.ceil
    real_price = price

    return if art.nil?

    @buy_prices[art] = price

    @log.info art

    name = item['name']

    return if price.nil?

    return if @skipBrands.any? { |br| name.upcase.include? br }

    price = price.ceil
    @log.info "Price #{price}"
    stock = stock_list['idp'].to_i

    @log.info "Stock #{stock}"

    return if stock < 1

    @log.info "Min order #{minorder}"

    cat = @cat_names[item['category_list'].first.to_i]

    if @skipCats.include? cat
      @log.info 'Skip cat'
      return
    end

    cat_id = 0

    sale = false

    if @sale_price.has_key? art
      sale = true
      price = @sale_price[art]
      @log.info "Price sale #{price}"
    end

    @log.info "Cat name #{cat}"

    size = ''
    #size = "Упаковка #{minorder} шт. Цена за 1" if minorder > 1
    size = "Упаковка #{minorder} шт. Цена за целую упаковку" if minorder > 1

    pics = []
    if pic_urls
      pic_urls.each_with_index { |url, i|
        pic = savePic(url, "#{art}~#{i}", true, false, false, :active, nil, show_order: i) unless Rails.env.development?
        pics << pic
      }
    end

    if minorder > 1
      #add_price = (price / 20.0).ceil
      #add_price = 2 if add_price > 2

      price = price * minorder * 1.1
      #price = price * 1.1
      #price += add_price
    end

    @log.info "Calculated price #{price}"
    @log.info "Real price #{real_price}"

    #      if price.to_i==@real_price[art].to_i
    #        price=price*1.1
    #end

    @log.info "minorder #{minorder.inspect}"
    @log.info "price #{price.inspect}"
    @log.info " minorder==1 and price>400 #{ minorder == 1 and price > 400}"

    if minorder == 1
      price = (real_price * 1.14).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder == 1 and price > 400
      price = (real_price * 1.12).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder == 1 and price > 1000
      price = (real_price * 1.07).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder == 1 and price > 2000
      price = (real_price * 1.02).ceil
    end

    if minorder == 1 and price > 3000
      price = real_price.ceil
    end

    orig_art = art

=begin
    if real_price < 6 and minorder > 1
      price = (real_price * 1.15 * 10).ceil
      art = "#{art}-10"
      size = "Цена за 10 шт" if minorder > 1
      name[0] = name[0].downcase
      name = "10 шт. #{name}"
      real_price = real_price.to_f * 10
      #@buy_price[art]=@buy_price[orig_art].to_f*10
    elsif real_price < 10 and minorder > 1
      price = (real_price * 1.14 * 5).ceil
      art = "#{art}-5"
      real_price = real_price.to_f * 5
      #@buy_price[art]=@buy_price[orig_art].to_f*5
      size = "Цена за 5 шт" if minorder > 1
      name[0] = name[0].downcase
      name = "5 шт. #{name}"
    end
=end

    desc = ActionView::Base.full_sanitizer.sanitize(desc)
    desc.gsub!(/[\r\n]+/, '. ')
    desc.gsub!(/ +/, ' ')
    desc.gsub!(/\.+/, '.')
    desc.strip!
    desc.gsub!(/[.,]+$/, '.')

    #desc="#{size}. #{desc}" if size!=''

    rrp = @retail_price[art]
    rrp *= minorder if minorder > 1

    rrp = (price * 1.5).round if rrp.nil? or rrp < price * 1.4

    col = addCollection(cat, cat_id)

    if col.tags2 == ['От поставщика']
      cat_tree = item['category_list'].map { |c| get_cat_tree(c) }.flatten
      if cat_tree.intersection(@children_cats).count > 0
        col.add_tag("Дети")
      elsif cat_tree.intersection(@hoz_cats).count > 0
        col.add_tag("Хозы")
      else
        col.add_tag("Офис")
      end
    end

    tree = (get_cat_tree(item['category_list'].first.to_i).reverse + [item['category_list'].first.to_i]).map { |c| @cat_names[c.to_i] }

    weight = item["weight"].to_f*1050
    height = item["package_size"].find { |size| size["type"] == "height" }["value"].to_f
    width = item["package_size"].find { |size| size["type"] == "width" }["value"].to_f
    depth = item["package_size"].find { |size| size["type"] == "depth" }["value"].to_f

    height = (height*10).ceil
    width = (width*10).ceil
    depth = (depth*10).ceil

    p = addProduct(col, art, name, price, desc, [size], pics, { 'cat_tree' => tree }, cat_id, nil, barcode: barcode, buy_price: real_price, rrp: rrp, sale: sale, brand_name: brand,
                   weight: weight, box_width: width, box_height: height, box_depth: depth)

    if col.coltype.to_i == 0
      col.coltype = p.category
      col.save
    end

  end

  def read_file(book)
    sheet = book['Прайс-заказ']

    start_row = 0
    sheet.sheet_data.rows.each_with_index do |row, i|
      if row[0] and row[0].value == 'Код'
        start_row = i + 3
        break
      end
    end

    #puts start_row

    cat = ''
    sheet.sheet_data.rows[start_row..-1].each do |row|
      if row[13].nil? or row[13].value.nil? or row[13].value.to_s == ''
        cat = row[0].value
        next
      end

      @pool.post do
        process_row(row, cat)
      end
    end

  end

  def dl_price(href, dest_file)
    @log.info href

    url = nil
    if href.include? 'pricelist'
      file = 'PRICE_LIST_ORDER'
    else
      file = 'ASSORTMENT_CSV'
    end

    page=@agent.get 'https://www.samsonopt.ru/zakaz/services/prices/'
    if /window.obBitrix.sessid = "(.*?)"/=~page.body
      uid=$1
    end

    d={ FILE: file, ajax: 'true', progress_bar_uid: uid }

    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest','Referer'=>'https://www.samsonopt.ru/zakaz/services/prices/' }
    @agent.post href, d, ajax_headers

    progress = 0
    i=0
    while progress < 100 do
      res = @agent.get "https://www.samsonopt.ru/ajax/progressbar.php?progress_bar_uid=#{uid}&_=#{Time.now.to_i}"
      @log.info res.body
      r = JSON.parse(res.body)
      progress = r['progress'].to_i
      url = r['message']
      sleep(1)
      i+=1
      raise "File dl timeout" if i==120
    end

    ext = 'zip'
    ext = '7z' if url.end_with? '7z'
    @agent.download(url, "/tmp/samson.#{ext}")

    File.unlink dest_file if File.exist? dest_file

    if ext == '7z'
      system("7z e -o/tmp/samson/ /tmp/samson.7z")
      system("mv /tmp/samson/*.xlsx #{dest_file}")
    else
      Zip::File.open("/tmp/samson.zip") do |zipfile|
        zipfile.each do |entry|
          entry.extract(dest_file)
          break
        end
      end
    end

  end

  def create_revalue_document
    doc = InventoryDocument.create(
      user_id: 1,
      doc_type: InventoryDocument.doc_types[:revalue],
      name: "Обновление цен Samson",
      posted: false
    )

    @buy_prices.each do | art, buy_price |
      p = InventoryProduct.find_by_sku("SAMS#{art}")
      if p
        p.stock.each do |st|
          size = st['size']
          next if st['buy_price'].to_f == buy_price.to_f

          retail_price = st['retail_price'].to_f
          sp_price = st['sp_price'].to_f
          rrp = st['rrp'].to_f

          new_rrp = (buy_price * 2).round
          rrp = new_rrp if new_rrp > rrp

          new_sp_price = buy_price * 1.57
          new_sp_price = new_sp_price.ceil if new_sp_price > 5
          sp_price = new_sp_price #if new_sp_price > sp_price

          new_retail_price = buy_price * 1.6
          new_retail_price = new_retail_price.ceil #if new_retail_price > 5
          retail_price = new_retail_price #if new_retail_price > retail_price

          rrp = sp_price * 1.1 if rrp < sp_price * 1.1

          InventoryDocumentLine.create(
            inventory_product_id: p.id,
            size: size,
            amount_change: 0,
            buy_price: buy_price,
            retail_price: retail_price,
            sp_price: sp_price,
            rrp: rrp,
            comment: "Переоценка",
            inventory_document: doc,
            line_type: :relative)
        end
      end
    end
  end

  def read_cats
    @cat_names = {}
    @cat_parents = {}
    page = @agent.get 'https://api.samsonopt.ru/v1/category/?api_key=b41407614a3372cb4584dbffeadd9aae'
    js = JSON.parse(page.body)

    js['data'].each { |cat|
      @cat_names[cat['id'].to_i] = cat['name']
      @cat_parents[cat['id'].to_i] = cat['parent_id'].to_i
    }
  end

  def get_cat_tree(cat_id)
    ret = []
    i = 0
    while true do
      cat_id = @cat_parents[cat_id]
      break if cat_id == 0
      ret << cat_id
      i += 1
      break if i == 50
    end
    ret
  end

  def run
    if @agent == nil then
      before
    end

    @sale_price = Hash.new
    @cat_ids = Hash.new
    @min_opt = Hash.new(0)

    #@agent.set_proxy("**************", 8080) unless Rails.env.development?

    read_cats

    @pool = Concurrent::FixedThreadPool.new(5)

    @children_cats = [26536, 142170, 125006, 48740, 218457, 25041, 25022, 24991, 218437, 212197, 25867, 217366, 217904, 144862, 26164]
    @hoz_cats = [217535, 218377, 48699, 461604, 268586, 217623, 217562, 218234, 216107, 404837, 25891]

    @skipCats = []

    @skipBrands = ['PASABAHCE', 'PATERRA', 'ROUBLOFF', 'VICTORINOX', 'GILLETTE', 'ФЕЯ', 'ALWAYS', 'NATURELLA', 'DISCREET', 'TAMPAX', 'ORAL-B', 'CALGON', 'IDEA',
                   'УТЕНОК', 'МИФ', 'АРИЕЛЬ', 'УШАСТЫЙ', 'НЕВСКАЯ ', 'TIDE', 'ARIEL', 'LENOR', 'МИФ', 'FINISH', 'GLADE ', 'AIRWICK', 'PERSIL', 'LOSK', 'ЛАСКА', 'DETTOL',
                   'DURACELL', 'PACLAN', 'AURA', 'ЧИСТАЯ', 'АЛЬТ', 'FABULA', 'BEFLER', 'FAIRY', 'MR. PROPER', 'GRIZZLY', 'GRASS'
    ]

    @used_brands = []

    #
    #use_local_pics
    #read_xml(@xml_file) unless @xml_file.nil?

    page = @agent.get 'https://www.samsonopt.ru/zakaz/'

    login_form = page.form_with(id:'authForm')
    login_form.USER_LOGIN = @login
    login_form.USER_PASSWORD = @password
    @agent.submit(login_form, login_form.buttons.first)

    if @price_file.nil?
      if Rails.env.development?
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_pricelist.php', 'e:/samson.xlsx')
        book = RubyXL::Parser.parse('e:/samson.xlsx')
      else
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_pricelist.php', '/tmp/samson.xlsx')
        book = RubyXL::Parser.parse('/tmp/samson.xlsx')
      end
    else
      book = RubyXL::Parser.parse(@price_file)
    end

    read_sale book

    @pics = {}
    @descs = {}
    @brands = {}

    @buy_prices = {}

    read_json 'https://api.samsonopt.ru/v1/assortment/?api_key=b41407614a3372cb4584dbffeadd9aae'

    @pool.shutdown
    @pool.wait_for_termination

    merge_small_collections(20, true)

    create_revalue_document

    File.open('/tmp/samson_brands', 'w') { |file| file.write(@used_brands.join("\n")) }
  end

end
