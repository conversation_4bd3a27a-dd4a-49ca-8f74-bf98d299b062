#encoding:utf-8
require_relative '../framework/parser'
require_relative 'nice_point_product_config'


module NicePoint
  class NicePointParser < Framework::<PERSON><PERSON><PERSON>
    def initialize_settings
      with_url('http://nice-point.ru/')
          .with_category_links('.categories_list__link')
          .with_pagination(->(category_page) {
            href = category_page.search('.pagination a').find {|a| a.text == '>'}
            return nil if href.nil?
            href.attr('href')
          })
          .with_product_config(->(pc) {
            pc.with_product_selector('.item-inner .box-images a')
                .with_category(->(product_page) {
                  product_page.search('.breadcrumbs li > a').drop(1)
                      .map {|t| prepare_string t.text.gsub('/ ', '')}.take(2).join ', '
                })
                .with_name(->(product_page) {
                  prepare_string product_page.search('h1').first.text
                })
                .with_articul(->(product_page) {
                  prepare_string product_page.search('.prt-code').first.text
                })
                .with_sizes(->(product_page) {
                  product_page.search('.product-options select option').drop(1).map{|opt| prepare_string opt.text}
                })
                .with_description(->(product_page) {
                  p = product_page.search('#product > p').find{|p| not p.text.blank? }
                  return '' if p.nil?
                  prepare_string p.text
                })
                .with_images(->(product_page) {
                  images = product_page.search('.thumbnail-item > img').map{|img| img.attr('src')}.uniq
                  [images[1], images[0]]
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://nice-point.ru/')
                })
                .with_category_type('850')
                .with_category_type('1373', 'женские', 'футболки')
                .with_category_type('30', 'мужские', 'футболки')
                .with_category_type('304', 'женские', 'свитшоты')
                .with_category_type('1444', 'мужские', 'свитшоты')
          }, NicePoint::NicePointProductConfig)
    end
  end
end