#encoding:utf-8
require_relative '../framework/excel/worksheet_config'
require_relative '../framework/sizeable_parsed_product'

module Intexxo
  class IntexxoWorksheetConfig < Framework::Excel::WorksheetConfig
    def initialize

    end

    def with_regex(regex)
      @regex = regex
      self
    end

    def with_articul_regex(articul_regex)
      @articul_regex = articul_regex
      self
    end
    def with_articul_with_size_regex(articul_with_size_regex)
      @articul_with_size_regex = articul_with_size_regex
      self
    end
    def with_nomenclature_index(nomenclature_index)
      @nomenclature_index = nomenclature_index
      self
    end
    def with_code_index(code_index)
      @code_index = code_index
      self
    end
    def with_price_index(price_index)
      @price_index = price_index
      self
    end

    def process_worksheet(worksheet)
      current_articul = ''
      products = Array.new

      worksheet.sheet_data.rows.each_with_index do |row, k|
        # Если в номенклатуре есть арт., то это следующий товар
        nomenclature = (row.nil? or row[@nomenclature_index].nil?) ? nil : row[@nomenclature_index].value.to_s

        # Если нет наименования, артикула или цены
        next if nomenclature.nil? or nomenclature.match(@regex).nil? or row[@price_index].nil? or row[@price_index].value.nil?

        name, articul = nomenclature.match(@articul_regex).captures
        size = nil
        unless nomenclature.match(@articul_with_size_regex).nil?
          captures = nomenclature.match(@articul_with_size_regex).captures
          size = captures[-1]
          # Имя не меняем, потому что информация будет из описания на сайте
          #name = "#{name} #{captures[-3]}"
        end

        code = row[@code_index].value.to_i

        # 4 с конца, а не 5 с начала
        price = row[@price_index].value.round

        products.push Framework::SizeableParsedProduct.new('', "#{code} #{articul}", [], name, '', price, []) if articul != current_articul

        current_articul = articul

        current_product = products[-1]

        # потому что она не всегда есть.
        # Как следствие, запоминаться будет последняя
        current_product.price = price
        current_product.sizes.push size unless size.nil?
        current_product.name = name if current_product.name != name
      end

      products
    end
  end
end
