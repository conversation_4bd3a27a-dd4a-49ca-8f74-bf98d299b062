# frozen_string_literal: true

class ChatGptBatchService
  def self.update_result
    ChatGptBatch.where.not(status: ['failed', 'completed', 'expired', 'cancelled']).each do |b|
      gpt = ChatGpt.new
      batch = gpt.client.batches.retrieve(id: b.batch_id)
      if batch
        b.status = batch['status']
        b.save
        service = Object.const_get(b.batch_type).new
        service.process_result(batch) if b.status == 'completed'
      end
    end

  end
end
