<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>

<% if @purchase.dlclass=='ApiParser::ApiParser'  %>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.12/ace.min.js" integrity="sha512-GoORoNnxst42zE3rYPj4bNBm0Q6ZRXKNH2D9nEmNvVF/z24ywVnijAWVi/09iBiVDQVf3UlZHpzhAJIdd9BXqw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.12/mode-javascript.min.js" integrity="sha512-ZxMbXDxB0Whct+zt+DeW/RZaBv33N5D7myNFtBGiqpDSFRLxn2CNp6An0A1zUAJU/+bl8CMVrwxwnFcpFi3yTQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<% end %>
<script>
    $.fn.selectpicker.Constructor.BootstrapVersion = '4';
    const purchaseId=<%=@purchase.id %>;
</script>

<div class="card m-3 p-2 ">
  <div class="card-heading">
    <h3 class="card-title">Информация о закупке <%=@purchase.name %></h3>
  </div>
  <div class="card-body">
    <h1 ><a data-toggle="collapse" data-target="#purchase_info_card" href="#"><%= @purchase.name %></a></h1>

    <div class="card-body collapse" id="purchase_info_card">
      <div class="row">
        <div class="col mb-3">
          <%= link_to 'Редактировать инструкцию по поставщику', info_purchase_path(@purchase)%>

        </div>
      </div>

      <%= form_for :purchase, :url => {:action => :'edit'}, :html => {:class => 'form-horizontal', :multipart => true} do |f| %>
          <div class="form-group row">
            <label class="control-label col-lg-2"> Name:</label>
            <div class="col-lg-10">
              <input type="text" name="name" value="<%= @purchase.name %>" class="form-control"/>
            </div>
          </div>

          <div class="form-group row">
            <label class="control-label col-lg-2"> DlClass:</label>
            <div class="col-lg-10">
              <input type="text" name="dlclass" value="<%= @purchase.dlclass %>" class="form-control"/>
            </div>
          </div>
          <div class="form-group row">
            <label class="control-label col-lg-2"> OrderClass:</label>
            <div class="col-lg-10">
              <input type="text" name="orderclass" value="<%= @purchase.orderclass %>" class="form-control"/>
            </div>
          </div>
        <div class="form-group row">
          <label class="control-label col-lg-2"> Название бренда:</label>
          <div class="col-lg-10">
            <input type="text" name="sp_brand_name" value="<%= @purchase.sp_brand_name %>" class="form-control"/>
          </div>
        </div>

        <div class="form-group row">
          <label class="control-label col-lg-2"> Номер мегазакупки 100sp:</label>
          <div class="col-lg-10">
            <input type="text" name="mega_id" value="<%= @purchase.mega_id %>" class="form-control"/>
          </div>
        </div>

        <div class="form-group row">
          <label class="control-label col-lg-2">Исключить бренды:</label>
          <div class="col-lg-10">
            <textarea name="skip_brands" class="form-control" rows="2"><%= @purchase.skip_brands %></textarea>
          </div>
        </div>

        <%if @purchase.dlclass=='ApiParser::ApiParser' %>
          <div class="form-group row">
            <label class="control-label col-lg-2"> Код:</label>
            <div class="col-lg-10">
              <div id="code_editor"><%=@purchase.code %></div>
              <textarea  id="code" name="code" class="form-control" style="display: none"><%=@purchase.code %></textarea>
            </div>
          </div>
        <% end %>

          <div class="form-group row">
            <div class="offset-lg-2 col-lg-10">
              <%= f.submit 'Изменить', class: 'btn btn-primary' %>
            </div>
          </div>
      <% end %>


      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Импорт коллекций со 100sp</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_col_pics_from_sp'}, :html => {:class => 'form-horizontal'}) do |f| %>

            <div class="form-group row">
              <label class="control-label col-lg-2"> ID закупки:</label>
              <div class="col-lg-10">
                <input type="text" name="pid" class="form-control"/>
              </div>

            </div>
            <div class="form-group row">
              <label class="control-label col-lg-2"> Перенести товары по коллекциям как на 100сп:</label>
              <div class="col-lg-5">
                <input type="checkbox" name="move_goods" class="form-control"/>
              </div>
              <div class="col-lg-5">
                <% if @purchase_variants and @purchase_variants.count>0 %>
                  Добавить метку к коллекциям:
                  <%= select_tag 'use_tag', options_for_select(['']+@purchase_variants) %>
                <% end %>
              </div>
            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2"> Загрузить категории товаров со 100сп:</label>
              <div class="col-lg-5">
                <input type="checkbox" name="change_good_type" class="form-control"/>
              </div>
            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2"> Перенести выключенные товары в коллекцию НЕ ЗАГРУЖАТЬ:</label>
              <div class="col-lg-5">
                <input type="checkbox" name="move_disabled_goods_to_not_load" class="form-control"/>
              </div>
            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2">Загрузить картинки коллекций:</label>
              <div class="col-lg-5">
                <input type="checkbox" name="load_col_pics" class="form-control"/>
              </div>
            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2">Загрузить группы коллекций:</label>
              <div class="col-lg-5">
                <input type="checkbox" name="load_groups" class="form-control"/>
              </div>
            </div>

              <div class="form-group row">
                <div class="offset-lg-2 col-lg-10">
                  <%= f.submit 'Загрузить', class: 'btn btn-primary' %>
                </div>
          <% end %>
              </div>
        </div>
      </div>


      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Импорт коллекций из файла</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_collection_csv'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>

            <div class="form-group row">
              <label class="control-label col-lg-2"> CSV файл:</label>
              <div class="col-lg-10">
                <input type="file" name="file" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Загрузить', class: 'btn btn-primary' %>
              </div>
          <% end %>
          </div>
        </div>
      </div>

      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Загрузка данных о закупке</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_purchase_data'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>

            <% if @purchase_variants and @purchase_variants.count>1 %>
              <div class="form-group row">
                <label class="control-label col-lg-2">Вариант закупки:</label>
                <div class="col-lg-10">
                  <%=select_tag "purchase_variant", options_for_select(@purchase_variants, @purchase_variants.first)%>
                </div>

              </div>
            <% end %>

            <div class="form-group row">
              <label class="control-label col-lg-2">ID закупки на 100sp:</label>
              <div class="col-lg-10">
                <input type="text" name="pid" class="form-control"/>
              </div>

            </div>
            <div class="form-group row">
              <div class="col-lg-10">
              <% if @purchase.purchase_data %>
                <div>Загружена информация о закупке:</div>

                <% if @purchase_variants and @purchase_variants.count>1 %>
                  <% @purchase_variants.each do |t| %>
                    <% next unless @purchase.purchase_data[t] %>
                    <div>
                      <strong><%=t %>:</strong>
                      <input name="purchase_num[<%= t %>]" value="<%=@purchase.purchase_data[t]['num'] %>"/>


                      - <%=@purchase.purchase_data[t]['name'] %>
                      <br/>
                      Мега - <%= @purchase.purchase_data[t]['megapurchase_id'] %><br/><br/>
                      Аннотация:  <%= @purchase.purchase_data[t]['annotation'][0..100]%>...
                      <a href="#" class="edit_purchase_data_button" data-text-type="annotation" data-tag="<%=t %>" data-text="<%=@purchase.purchase_data[t]['annotation'] %>">Редактировать</a><br/><br/>
                      Описание: <%= @purchase.purchase_data[t]['desc'][0..100]%>...
                      <a href="#" class="edit_purchase_data_button" data-text-type="desc" data-tag="<%=t %>" data-text="<%=@purchase.purchase_data[t]['desc'] %>">Редактировать</a><br/><br/>
                      Правила: <%= @purchase.purchase_data[t]['rules'][0..100]%>...
                      <a href="#" class="edit_purchase_data_button" data-text-type="rules" data-tag="<%=t %>" data-text="<%=@purchase.purchase_data[t]['rules'] %>">Редактировать</a><br/><br/>

                    </div>
                  <% end %>
                <% else %>
                    <input name="purchase_num" value="<%=@purchase.purchase_data['num'] %>"/> - <%= @purchase.purchase_data['name'] %>
                    <br/>
                    Мега - <%= @purchase.purchase_data['megapurchase_id'] %> <br/><br/>
                    Аннотация:  <%= @purchase.purchase_data['annotation'][0..100] %>...
                    <a href="#" class="edit_purchase_data_button" data-text-type="annotation" data-tag="" data-text="<%=@purchase.purchase_data['annotation'] %>">Редактировать</a><br/><br/>
                    Описание: <%= @purchase.purchase_data['desc'][0..100] %>...
                    <a href="#" class="edit_purchase_data_button" data-text-type="desc" data-tag="" data-text="<%=@purchase.purchase_data['desc'] %>">Редактировать</a><br/><br/>
                    Правила: <%= @purchase.purchase_data['rules'][0..100] %>...
                    <a href="#" class="edit_purchase_data_button" data-text-type="rules" data-tag="" data-text="<%=@purchase.purchase_data['rules'] %>">Редактировать</a><br/><br/>
                <% end %>

              <% end %>
            </div>
            </div>

            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Загрузить со 100sp', name:'download', class: 'btn btn-primary' %>
                <%= f.submit 'Загрузить НА 100sp', name:'upload', class: 'btn btn-primary' %>
                <%= f.submit 'Загрузить ТОЛЬКО ОБЛОЖКИ НА 100sp', name:'upload_pics', class: 'btn btn-primary' %>
              </div>
          <% end %>
          </div>
        </div>
      </div>


      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Импорт картинок для выбора</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_candidate_pics'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>

            <div class="form-group row">
              <label class="control-label col-lg-2"> Загрузить картинки как основные:</label>
              <div class="col-lg-1">
                <input type="checkbox" name="active_pics" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2"> CSV файл:</label>
              <div class="col-lg-10">
                <input type="file" name="file" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Загрузить', class: 'btn btn-primary' %>
              </div>
          <% end %>
          </div>
          <a href="/purchases/<%= @purchase.id %>/image_chooser">Выбор картинок</a><br/>
          <a href="/purchases/<%= @purchase.id %>/get_goods_without_pics">Товары без картинок</a>
        </div>
      </div>

      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Импорт остатков товара со 100sp</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_stock_from_sp'}, :html => {:class => 'form-horizontal'}) do |f| %>

            <div class="form-group row">
              <label class="control-label col-lg-2"> ID закупки для импорта остатков из закупки:</label>
              <div class="col-lg-10">
                <input type="text" name="pid" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2"> ID мегазаказа для импорта остатков из мегазаказа:</label>
              <div class="col-lg-10">
                <input type="text" name="megaorder_id" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Загрузить', class: 'btn btn-primary' %>
              </div>
          <% end %>
          </div>
        </div>
      </div>

      <div class="card ">
        <div class="card-heading">
          <h3 class="card-title">Импорт картинок товара со 100sp</h3>
        </div>
        <div class="card-body">
          <%= form_for(@purchase, :url => {:action => :'load_from_sp'}, :html => {:class => 'form-horizontal'}) do |f| %>

            <div class="form-group row">
              <label class="control-label col-lg-2"> ID закупки:</label>
              <div class="col-lg-10">
                <input type="text" name="pid" class="form-control"/>
              </div>

            </div>

            <div class="form-group row">
              <label class="control-label col-lg-2"> Заменить картинки:</label>
              <div class="col-lg-10">
            <%= check_box_tag 'replace' %>
              </div>
            </div>

            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Загрузить', class: 'btn btn-primary' %>
              </div>
          <% end %>
          </div>
        </div>
      </div>



    </div>
    <div>
      <%= link_to 'Поставки', purchase_shipments_path(@purchase) %>
    </div>
    <p id="statusText">Статус: <%= @purchase.status %></p>
    <p id="errorInfo">Загрузка завершена успешно: <%= @purchase.error ? 'Нет' : 'Да' %></p>
    <p id="messagesText"> <%= @purchase.message %><br/></p>
    <% if @purchase.products.length > 0  %>
        <p>Дата загрузки данных:
          <% if  @purchase.downloaded_at %>
            <%= @purchase.downloaded_at.localtime.strftime('%Y-%m-%d %H:%M:%S') %>
            (прошло часов: <%=  ((Time.now-@purchase.downloaded_at)/60/60).to_i %>)
            <% else %>
            нет данных
            <% end %>
          </p>
    <% end %>

    Обновлено товаров: <%= @products_updated %>

    <% if @dl.supplier_site %>
      <p>
        Сайт поставщика: <a href="<%= @dl.supplier_site %>"><%= @dl.supplier_site %></a>
      </p>
    <% end %>

    <% if @dl.price_multiplier %>
      <p>
        Наценка: <%= @dl.price_multiplier %>
      </p>
    <% end %>

    <% if @purchase.status==3 %>
        <%= render 'purchases/progress' %>
    <% end %>
    <% if @purchase.status==3 or @purchase.status==1 %>
        <%= form_for(@purchase, :url => {:action => :'stop'}) do |f| %>
            <%= f.submit 'Остановить', class: 'btn btn-primary' %>
        <% end %>
    <% end %>
  </div>
</div>

<div class="card m-3 p-2">
  <div class="card-heading">
    <h3 class="card-title">
      <% if @purchase.is_tool
           button_title='Запустить'
      %>
        Форма для запуска
        <% else
             button_title='Загрузить из источника'
        %>
        Форма для загрузки данных из источника
        <% end %>

    </h3>
  </div>
  <div class="card-body">
    <% if @dl.nil? %>
        <h3 class="card-title">Указан неверный класс парсера</h3>
    <% else %>
        <%= form_for(@purchase, :url => {:action => :'startdownload'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>
            <% if @purchase.errors.any? %>
                <div id="error_explanation">
                  <h2><%= pluralize(@purchase.errors.count, "error") %> prohibited this response from being saved:</h2>

                  <ul>
                    <% @purchase.errors.full_messages.each do |msg| %>
                        <li><%= msg %></li>
                    <% end %>
                  </ul>
                </div>
            <% end %>


        <% @dl.dlparams.each do |name, t| %>
          <%
            if t.is_a?(Array)
              label=t[1]
              t=t[0]
            else
              label=name
            end
          %>
                <div class="form-group row">
                  <label class="control-label col-lg-2"> <%= label %>:</label>
                  <% if t==:checkbox %>
                      <div class="col-lg-1">
                        <input type="<%= t %>" name="<%= name %>" <%= @purchase.data[name]=='on' ? :'checked' : '' %> class="form-control"/>
                      </div>
                  <% elsif t==:select %>
                      <div class="col-lg-10">
                        <select name="<%= name %>" class="form-control">
                          <% @dl.selects[name].each do |val| %>
                              <option value="<%= val %>"><%= val %></option>
                          <% end %>
                        </select>
                      </div>
                  <% elsif t==:textarea %>
                      <div class="col-lg-10">
                        <textarea name="<%= name %>" class="form-control"><%= @purchase.data[name] %></textarea>
                      </div>
                  <% else %>
                      <div class="col-lg-10">
                        <input type="<%= t %>" name="<%= name %>" value="<%= @purchase.data[name] %>" class="form-control"/>
                      </div>
                  <% end %>

                </div>
            <% end %>
            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit button_title, class: 'btn btn-primary' %>
              </div>
            </div>
        <% if @purchase.data and @purchase.data['dl_success_file'] %>
          <div>
            <a href="<%=show_dl_success_file_purchase_url%>">Файл с результатом</a>
          </div>
        <% end %>

        <% end %>
    <% end %>
  </div>
</div>

<% unless  @purchase.is_tool %>

<div class="card m-3 p-2">
  <div class="card-heading">
    <h3 class="card-title">Форма отправки заказа</h3>
  </div>
  <div class="card-body">
    <% if @order.nil? %>
        <p>Нет возможности отослать заказ поставщику</p>

    <% else %>
      <% if @purchase.data and @purchase.data['error_file'] %>
        <div>
          <a href="<%=show_error_file_purchase_url%>">Посмотреть последние ошибки отправки</a>
        </div>
      <% end %>

      <% if @purchase.data and @purchase.data['success_file'] %>
        <div>
          <a href="<%=show_success_file_purchase_url%>">Файл с результатом</a>
        </div>
      <% end %>

      <p>Последние cookies:
          <pre><%= @purchase.cookies %></pre>
      </p>
        <%= form_for(@purchase, :url => {:action => :'process_order'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>
            <% if @purchase.errors.any? %>
                <div id="error_explanation">
                  <h2><%= pluralize(@purchase.errors.count, "error") %> prohibited this response from being saved:</h2>

                  <ul>
                    <% @purchase.errors.full_messages.each do |msg| %>
                        <li><%= msg %></li>
                    <% end %>
                  </ul>
                </div>
            <% end %>


            <% @order.dlparams.each do |name, t| %>
            <%
              if t.is_a?(Array)
                label=t[1]
                t=t[0]
              else
                label=name
              end
            %>

                <div class="form-group row">
                  <label class="control-label col-lg-2"> <%= label %>:</label>
                  <% if t==:checkbox %>
                      <div class="col-lg-1">
                        <input type="<%= t %>" name="<%= name %>" <%= @purchase.data[name]=='on' ? :'checked' : '' %> class="form-control"/>
                      </div>
                  <% elsif t==:select %>
                      <div class="col-lg-10">
                        <select name="<%= name %>" class="form-control">
                          <% @dl.selects[name].each do |val| %>
                              <option value="<%= val %>"><%= val %></option>
                          <% end %>
                        </select>
                      </div>
                  <% elsif t==:textarea %>
                      <div class="col-lg-10">
                        <textarea name="<%= name %>" class="form-control"><%= @purchase.data[name] %></textarea>
                      </div>
                  <% else %>
                      <div class="col-lg-10">
                        <input type="<%= t %>" name="<%= name %>" value="<%= @purchase.data[name] %>" class="form-control"/>
                      </div>
                  <% end %>
                </div>
            <% end %>
            <div class="form-group row">
              <div class="offset-lg-2 col-lg-10">
                <%= f.submit 'Обработать заказ', class: 'btn btn-primary' %>
              </div>
            </div>
        <% end %>
    <% end %>
  </div>
</div>



<div class="card m-3 p-2">
  <div class="card-heading">
    <h3 class="card-title">Форма подтверждения счета заказа</h3>
  </div>
  <div class="card-body">
    <% unless defined? @dl.invoice_comparator %>
      <h3 class="card-title">Нет возможности обработать счет</h3>

    <% else %>
      <%= form_for(@purchase, :url => {:action => :'process_invoice'}, :html => {:class => 'form-horizontal', :multipart => true}) do |f| %>

        <div class="form-group row">
          <label class="control-label col-lg-2">Использовать списание:</label>
          <div class="col-lg-10">
            <select name="use_inventory_document" >
              <option value=""></option>
              <% @inventory_documents.each do |doc| %>
                <option value="<%=doc.id %>"><%=doc.id %> <%=doc.name %></option>
              <% end %>
            </select>
          </div>
        </div>

        <div class="form-group row"  id="invoice_file_tpl">
            <label class="control-label col-lg-2">CSV файл счета:</label>
              <div class="col-lg-10">
                <input type="file" name="invoice_csv[]" class="form-control"/>
              </div>
          </div>

        <div class="form-group row" id="add_invoice_file_block">
          <div class="offset-lg-2 col-lg-2">
            <input type="button" id="add_invoice_file" value="Еще файл счета" class="form-control"/>
          </div>
        </div>

      <div class="form-group row" id="file_tpl">
        <label class="control-label col-lg-2">CSV со 100sp:</label>
        <div class="col-lg-10">
          <input type="file" name="spfile_csv[]" class="form-control"/>
        </div>
      </div>

        <div class="form-group row" id="add_file_block">
          <div class="offset-lg-2 col-lg-2">
            <input type="button" id="add_file" value="Еще файл" class="form-control"/>
          </div>
        </div>

        <div class="form-group row">
          <div class="offset-lg-2 col-lg-10">
            <%= check_box_tag 'price_check' %> Сравнивать цены
          </div>
        </div>

        <div class="form-group row">
          <div class="offset-lg-2 col-lg-10">
            <%= check_box_tag 'clean_confirm' %> Не учитывать подтвержденное
          </div>
        </div>

        <div class="form-group row">
          <div class="offset-lg-2 col-lg-10">
            <%= f.submit 'Обработать cчет', class: 'btn btn-primary' %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>





<div class="card m-3 p-2">
  <div class="card-heading">
    <h3 class="card-title">Форма для загрузки данных на 100sp</h3>
  </div>
  <div class="card-body">
    <% if @new_brands.count>0 %>
    <div class="bg-warning mb-3">
      <p>В закупке есть новые бренды, надо добавить на 100сп!</p>
      <div> <%=@new_brands.join(', ') %></div>
    </div>

    <% end %>

    <%= form_for(@purchase, :url => {:action => :'startupload'}, :html => {:class => 'form-horizontal'}) do |f| %>

      <% unless @dl.no_direct_upload %>

        <% @uploader.dlparams.each do |name, t| %>
            <div class="form-group row">
              <label class="control-label col-lg-2"> <%= name %>:</label>
              <div class="col-lg-10">
                <input type="<%= t %>" name="<%= name %>" class="form-control"/>
              </div>

            </div>
        <% end %>
        <% end %>
      <% if @purchase_variants and @purchase_variants.count>1 %>
        <div class="form-group row">
          <label class="control-label col-lg-2">Вариант закупки:</label>
          <div class="col-lg-10">
            <%=select_tag "purchase_variant", options_for_select(@purchase_variants, @purchase_variants.first)%>
          </div>

        </div>
      <% end %>

      <div class="form-group row">
        <div class="offset-lg-2 col-lg-10">
          <%= f.submit 'Загрузить картинки коллекций', class: 'btn btn-primary', 'data-disable-with':false%>
        </div>
      </div>
      <div class="form-group row">
        <div class="offset-lg-2 col-lg-10">
          <%= f.submit 'Скачать CSV', class: 'btn btn-primary', download:true, 'data-disable-with':false  %>
          <%= f.submit 'Скачать XLSX', class: 'btn btn-secondary', download:true, 'data-disable-with':false  %>
          <%=
            #f.submit 'Скачать CSV для МС', class: 'btn', download:true
          %>
          <%= check_box_tag 'all_goods' %> все товары (вместе с неактивными)
          <%= check_box_tag 'use_one_sale_collection' %> все товары со скидкой в одну коллекцию
        </div>
      </div>
      <div class="form-group row">
        <label class="control-label col-lg-2">Исключить артикулы:</label>
        <div class="col-lg-10">
          <%= text_area_tag 'exclude_arts' %>
        </div>
      </div>


        <% if @max_products_in_collection>1000 %>
          <div class="form-group row">
            <div class="offset-lg-2 col-lg-10">
              Выбрать случайно <input name="max_products_per_collection" value="0"/> товаров из коллекции
            </div>
          </div>

        <% end %>
        <hr/>
        <div class="col-lg-12">
          <div class="row">
            <div class="col-lg-2">
              <h4>Список коллекций</h4>
            </div>
            <div class="col-lg-10">
              <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#create_collection_modal">Создать коллекцию</button>
            </div>
          </div>

          <div class="container-fluid" id="accordion">
            <%= render @purchase.ordered_collections %>
          </div>
        </div>


    <% end %>
  </div>
</div>

<% end %>

<div class="footer fixed-bottom bg-light">
  <div class="navbar-inner">
    <div class="container-fluid">
      <div class="row">
      <div class="col-3">
        <select id="col_select" class="selectpicker"  data-live-search="true" data-width="fit">
          <option value="-1">Перенести товары</option>
          <% @purchase.ordered_collections.each do |col| %>
            <%
              img=col.pics
              begin
                img=JSON.parse(col.pics)[0].gsub('/mnt/spup/images/', '/img/100/')
              rescue
              end

              img=img.gsub('/mnt/spup/images/', '/img/100/') if img

              img="<img src='#{img}'/>" unless img.blank?
            %>
            <option data-content="<span><%=img %> <%= col.name %></span>" value="<%= col.id %>"><%= col.name %></option>
          <% end %>
        </select>
      </div>

        <div class="col-2">
          Всего товаров <%= @active_product_count %> (<%= @total_product_count %>)
        </div>

        <div class="col-3">
          <select id="select_collections" class="selectpicker"  data-width="fit" data-pid="<%= @purchase.id %>">
            <option value="-1">Выбрать коллекции</option>
            <option value="-2">Выбрать все</option>
            <option value="-3">Снять все</option>
            <%= @purchase.owned_tags.each do |tag| %>
              <option><%= tag %></option>
            <% end %>
          </select>
          Выбрано: <span id="checked_collections">0</span>, товаров: <span id="checked_products">0</span>
        </div>

        <div class="col-4">
          <select id="tag_select" class="selectpicker"  data-width="fit" data-pid="<%= @purchase.id %>">
            <option value="0">Добавить метку</option>
            <%= @owned_tags.each do |tag| %>
              <option><%= tag %></option>
            <% end %>
          </select>
        </div>
    </div>
  </div>
</div>
</div>

<div id="fullsize"></div>

<div class="modal fade" id="col_move_confirm" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Перемещение в коллекцию</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>вопрос</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="move_products">Переместить</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="rename_collection_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Переименовать коллекцию</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Старое имя: <span id="old_name"></span></p>
        <p>Новое имя: <input class="input-block-level" type="text" id="new_collection_name" autofocus></input></p>
        <p>Создать правило: <input class="input-block-level" type="checkbox" id="create_rename_rule" checked></input></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="rename_collection">Переименовать</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="create_collection_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Создать коллекцию</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Имя: <input class="input-block-level" type="text" id="collection_name" autofocus></input></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="do_create_collection">Создать</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="delete_collection_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Удалить коллекцию</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        Подтвердите удаление коллекции
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="do_delete_collection">Удалить</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>
<%if @purchase.dlclass=='ApiParser::ApiParser' %>
  <script>
    var editor = ace.edit('code_editor',{maxLines: 50,minLines: 10,autoScrollEditorIntoView: true});
    editor.session.setMode("ace/mode/javascript");
    var textarea = $('textarea[name="code"]');
    editor.getSession().on("change", function () {
      textarea.val(editor.getSession().getValue());
    });
    </script>
<% end %>


<div class="modal fade" id="text_editor" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Редактирование текста</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <textarea autofocus rows="20" cols="100" class="form-control" id="text_editor_text"></textarea>
        </div>

      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="do_save_text">Сохранить</button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>

