# frozen_string_literal: true

class PlusminusInvoiceAdapter < SkuInvoiceAdapter
  def initialize
    super
    @name_col_name = 'Товары (работы, услуги)'
    @code_col_name = nil
  end

  def invoice_comparator(line,fields)
    n=line['Название'].clone
    if /\(([\-+].*?)\)/=~n
      file_size=$1
      file_size=file_size.to_f if file_size.to_f!=0
      n.gsub!(/\(.*?\)/,'')
    end

    site_size=fields['Размер']
    site_size=site_size.to_f if site_size.to_f!=0

    site_art=fields['Наименование'].gsub('&amp;','&').gsub(/[ .«»№&"'”“]/,'').downcase
    file_art=n.gsub('&amp;','&').gsub(/[ .«»№&"'”“]/,'').downcase


    if site_size!='-'
      (site_art.start_with? file_art or file_art.start_with? site_art) and (site_size==file_size)
    else
      site_art.start_with? file_art or file_art.start_with? site_art
    end
  end
end
