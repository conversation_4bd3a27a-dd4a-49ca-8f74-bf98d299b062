#encoding:utf-8
require_relative '../framework/site/product_config'

module Setty
  class SettyProductConfig < Framework::Site::ProductConfig

    def parse_product(product_page, link)
      result = super(product_page, link)

      excel_product = @get_excel.call.find{|p|p.articul == result.articul}

      return nil if excel_product.nil?

      result.category = prepare_string("#{result.category}, #{excel_product.name}")
      result.name = excel_product.name
      result.price = excel_product.price
      #result.category = "#{excel_product.category}, #{result.category}"

      result.category = prepare_string(result.category + ' - Скидки') if excel_product.additional['is_sale']
      result.category = prepare_string(result.category + ' - Новинки') if excel_product.additional['is_new']

      result
    end

    def with_excel_products(get_excel)
      @get_excel = get_excel
      self
    end
  end
end
