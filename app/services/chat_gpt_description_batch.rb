# frozen_string_literal: true
#
class ChatGptDescriptionBatch < ChatGptBatchService
  def run(purchase_id, prompt: nil)
    b = ChatGptBatch.where(purchase_id: purchase_id, batch_type: self.class.name).first
    return if b && (b.status == 'cancelling' || b.status == 'in_progress' || b.status == 'validating')

    gpt = ChatGpt.new
    lines = []

    purchase = Purchase.find(purchase_id)
    purchase.products.active.where(edited_desc: nil).each do |product|
      p = nil
      p = "#{prompt}. Товар: `#{product.name}`. Описание товара: `#{product.desc}`" if prompt
      lines << gpt.create_new_description(product.name, product.desc, nil, batch_id: "prod_desc_#{product.id}", prompt: p)
      break if Rails.env.development? # limit for development
    end

    return if lines.length == 0

    fn=Rails.root.join('files', "chatgpt_desc_#{purchase_id}.jsonl").to_s
    File.open(fn, 'w') { |f| f.write(lines.join("\n")) }
    r = gpt.upload_file(fn, 'batch')
    puts r
    r = gpt.create_batch(r['id'])
    puts r
    ChatGptBatch.create(batch_id: r['id'], status: 'validating', purchase_id: purchase_id, filename: fn, expires_at: r['expires_at'], batch_type: self.class.name )

    gpt.client.batches.list
  end

  def process_result(batch)
    gpt = ChatGpt.new

    content = gpt.client.files.content(id: batch['output_file_id'])
    File.write(Rails.root.join('files', "chatgpt_desc_#{batch['id']}.jsonl").to_s, content)
    #puts content[0..100]

    content.each do |res|
      if res['custom_id'] && ( res['custom_id'].start_with?('prod_dect_') || res['custom_id'].start_with?('prod_desc_'))
        product_id = res['custom_id'].split('_')[2].to_i
        product = Product.find(product_id)

        puts res.dig('response','body', "choices", 0, "message",'content')
        new_desc = JSON.parse(res.dig('response','body', "choices", 0, "message",'content'))['description']

        product.edited_desc = new_desc.gsub('**','')
        product.save
      end
    end

  end

end