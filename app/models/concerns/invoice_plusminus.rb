module InvoicePlusminus
  extend ActiveSupport::Concern

  def unprocessed_invoice?
    true
  end

  def read_invoice(file)
    ret=[]
    workbook = Spreadsheet.open(file)
    sheet = workbook.worksheet(0)

    name_num=get_col_number(sheet,'Товары (работы, услуги)')
    q_num=get_col_number(sheet,'Количество')
    price_num=get_col_number(sheet,"Цена")

    raise "Bad file" if !name_num || !q_num

    in_data=false
    sheet.each do |row|
      next if row.nil?

      next if row[q_num].blank?


      if row[q_num]=='Количество'
        in_data=true
        next
      end

      next unless in_data

      name=row[name_num]
      q=row[q_num].to_i

      price='0'
      price=row[price_num].to_s if price_num

      ret<<{'Название'=>name,'Количество'=>q, 'Цена закуп'=>price}
    end
    ret
  end

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    n=line['Название'].clone
    if /\(([\-+].*?)\)/=~n
      file_size=$1
      file_size=file_size.to_f if file_size.to_f!=0
      n.gsub!(/\(.*?\)/,'')
    end

    site_size=fields['Размер']
    site_size=site_size.to_f if site_size.to_f!=0

    site_art=fields['Наименование'].gsub('&amp;','&').gsub(/[ .«»№&"'”“]/,'').downcase
    file_art=n.gsub('&amp;','&').gsub(/[ .«»№&"'”“]/,'').downcase


    if site_size!='-'
      (site_art.start_with? file_art or file_art.start_with? site_art) and (site_size==file_size)
    else
      site_art.start_with? file_art or file_art.start_with? site_art
    end
  end
end