module Swan
  class SwanOrderSender< Framework::OrderSender

    def process_page(href,code,sizes)
      puts href

      page=@agent.get href

      json=nil

      if /new msDetailProduct\((.*?),\s*"discountPrice"/m=~page.body
        json=$1
      end

      json+='}'
      json.gsub!("'",'"')

      data=JSON.parse(json)

      return if data['OffersProps']=='' or data['image']==''

      page_code=data['PRODUCT_ID'].to_s
      puts page_code
      return unless code==page_code

      data['OffersProps'].each {|offer_id,v|
        if sizes.include? v['RAZMER']
          @agent.get('http://swanshop.ru/index.php?logout=yes')
          authorize

          p=@agent.get "http://swanshop.ru/catalog/index.php?action=ADD2BASKET&id=#{offer_id}&quantity=#{sizes[v['RAZMER']]}" #&ajax_basket=Y
          puts p.search('.bx-basket-block a')[1].text
          sleep(1)
        end
      }

    end

    def order(articul, colors)
      debug_func "#{articul}, #{colors}"
      (code,art) = articul.split

      colors.each do |color, sizes|
        page=@agent.get("http://swanshop.ru/catalog/?q=#{art}&s=%D0%9D%D0%B0%D0%B9%D1%82%D0%B8")
        a=page.search('#section_list p.item_second_name a').first
        if a
          process_page(a.attr('href').to_s+'?preview=1',code,sizes)
        end
      end

    rescue Exception => e
      add_error_csv_data('Error adding to cart', articul)
      debug_func "Error: #{e}"

      #debug_func "ADDED DATA: length #{ids.length}, total cost length #{cost.length}"
    end

    def authorize
      page=@agent.get 'http://www.swanshop.ru/login/'
      login_form=page.form_with(name:'form_auth')
      login_form['USER_LOGIN']='<EMAIL>'
      login_form['USER_PASSWORD']='270279'
      page = @agent.submit(login_form, login_form.buttons[0])
    end

    def add_csv_data(fields)
      articul=fields['Артикул'].to_s
      color = ''
      size = fields['Размер'].to_s
      debug_func fields['Артикул']

      @csv_data[articul] = {} unless @csv_data.include? articul
      @csv_data[articul][color] = {} unless @csv_data[articul].include? color

      @csv_data[articul][color][size]=0 unless @csv_data[articul][color][size]
      @csv_data[articul][color][size]+=1
    end

  end
end
