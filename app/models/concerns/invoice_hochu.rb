#encoding: utf-8

module InvoiceHochu
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line, fields)
    size = '-'

    site_art = fields['Артикул'].upcase.strip.gsub(/^\d+ /, '').gsub('*', '').gsub(/ Б$/, '')

    if /^(С\d+)/ =~ site_art
      site_art = $1
    end

    if line['Название'] =~ /(\d+)$/
      size = $1
    end

    site_size = '-'
    site_size = fields['Размер'] if size != '-'

    if line['Название'].downcase.include? 'сумка' and fields['Наименование'].downcase.include? 'сумка'
      line['Артикул'].upcase.strip == site_art
    else
      line['Артикул'].upcase.strip == site_art and size == site_size
    end

  end

end