module Mf
  class MfParser < Downloader
    include InvoiceMf

    def initialize
      @dlparams={'price_file'=>:file}

      super
    end

    def process_page(url)
      tries ||= 2

      @log.info(url)
      page = @agent.get(url)
      puts url

      name=page.search('meta[property="og:title"]').first.attr('content').to_s.gsub(', MF','')

      name2=name.gsub(/\s/,'').downcase
      return unless @names[name2]

      desc=page.search('meta[itemprop="description"]').first.attr('content').to_s
      art=page.search('span.j-article').first.text

      sost=''
      sost=page.search('span.j-composition').text.strip if page.search('span.j-composition').first

      props=Hash.new
      page.search('.params .pp').each do |p|
        if p.search('span').count==2
          props[p.search('span')[0].text]=p.search('span')[1].text
        elsif p.search('p.note').count==1
          note=p.search('p.note').text
        end
      end

      props.delete('Страна бренда')
      props.delete('Страна производитель')
      props.delete('Сезон')
      props.delete('Комплектация')

      sex=props.delete('Пол')
      cat=name.split[0]

      if sex
        cat="Мужское - #{cat}" if sex.downcase.include? 'муж'
        cat="Женское - #{cat}" if sex.downcase.include? 'жен'
        cat="Детское - #{cat}" if sex.downcase.include? 'дет'
        cat="Детское - #{cat}" if sex.downcase.include? 'мал'
        cat="Детское - #{cat}" if sex.downcase.include? 'девоч'
      end

      prop_text=props.map {|k,v| "#{k.downcase}: #{v.downcase}"}.join ', '

      desc="#{desc}. Состав: #{sost}. #{prop_text}."

      desc.gsub!(/\.+/,'.')
      desc.gsub!(/ +/,' ')
      desc.gsub!(/^[ .]+/,'')

      pic_urls=page.search('ul.carousel a.j-carousel-image').map{|a| a.attr('href').to_s}
      pics=[]
      pic_urls.each_with_index {|url,i|
        pics<<savePic(url,"#{art}~#{i}",true)
      }

      cat_type = '850'
      cat_type = '102165' if cat.downcase.include? 'блуз'
      cat_type = '17' if cat.downcase.include? 'брюки'
      cat_type = '102179' if cat.downcase.include? 'жакет'
      cat_type = '102180' if cat.downcase.include? 'жилет'
      cat_type = '26' if cat.downcase.include? 'комбинезон'
      cat_type = '2105' if cat.downcase.include? 'комплект'
      cat_type = '102181' if cat.downcase.include? 'кофт'
      cat_type = '102186' if cat.downcase.include? 'куртк'
      cat_type = '286' if cat.downcase.include? 'накидка'
      cat_type = '129' if cat.downcase.include? 'пальто'
      cat_type = '1101' if cat.downcase.include? 'платье'
      cat_type = '102164' if cat.downcase.include? 'рубашк'
      cat_type = '1104' if cat.downcase.include? 'сарафан'
      cat_type = '102185' if cat.downcase.include? 'свитшот'
      cat_type = '307' if cat.downcase.include? 'туник'
      cat_type = '1373' if cat.downcase.include? 'футболк'
      cat_type = '19' if cat.downcase.include? 'юбк'
      cat_type = '102175' if cat.downcase.include? 'шорт'
      cat_type = '281' if cat.downcase.include? 'бридж'
      cat_type = '102177' if cat.downcase.include? 'костюм'
      cat_type = '3013' if cat.downcase.include? 'пижам'
      cat_type = '3013' if cat.downcase.include? 'сорочк'
      cat_type = '102184' if cat.downcase.include? 'толст'
      cat_type = '3011' if cat.downcase.include? 'халат'

      cat_type = '3011' if cat.downcase.include? 'шапк'
      cat_type = '108151' if cat.downcase.include? 'шарф'
      cat_type = '107838' if cat.downcase.include? 'сумк'
      cat_type = '55' if cat.downcase.include? 'купал'

      cat_type = '1179' if cat.downcase.include? 'кошел'
      cat_type = '107838' if cat.downcase.include? 'майк'

      if cat.downcase.include? 'муж'
        cat_type = '877' if cat.downcase.include? 'шорт'
        cat_type = '3000' if cat.downcase.include? 'брюки'
        cat_type = '30' if cat.downcase.include? 'пижам'
        cat_type = '51' if cat.downcase.include? 'костюм'
        cat_type = '30' if cat.downcase.include? 'футболк'
        cat_type = '32' if cat.downcase.include? 'джемп'

        cat_type = '108471' if cat.downcase.include? 'толст'
        cat_type = '108471' if cat.downcase.include? 'свит'
      end

      if cat.downcase.include? 'дет'
        cat_type = '1298' if cat.downcase.include? 'шапк'
        cat_type = '551' if cat.downcase.include? 'пижам'
        cat_type = '562' if cat.downcase.include? 'жилет'
        cat_type = '108460' if cat.downcase.include? 'футболк'
        cat_type = '108459' if cat.downcase.include? 'джемп'

        cat_type = '554' if cat.downcase.include? 'толст'
        cat_type = '554' if cat.downcase.include? 'свит'
        cat_type = '1179' if cat.downcase.include? 'кошел'
      end

      sleep(rand(0.5)+1.5)

      col=addCollection(cat,cat_type)
      addProduct(col,@names[name2][:art],name,@names[name2][:price],desc,@names[name2][:sizes],pics,@names[name2][:extra]) #unless @products.include? art

    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href)

      page.search('.j-products-container a.ref_goods_n_p').each {|a|
        process_page(a.attr('href').to_s)
      }

      if page.search('.pager a.next').count>0
        process_cat page.search('.pager a.next').first.attr('href').to_s
      end
    end

    def process_file(f)
      book = Spreadsheet.open f

      sheet = book.worksheets[0]
      sizes=''
      name=''
      sheet.rows[1..-1].each_with_index do |row,i|
        price=row[6]
        name=row[2]
        next if price.nil? or name.nil?
        #price.gsub!(/\s/,'')
        name=name.gsub(/\s/,'').downcase
        size=row[4]
        barcode=row[5].to_s.gsub('.0','')
        size='-' if size.nil?
        s=size.split(',').map(&:strip)
        s.delete_at(1) if s.count>1
        s.delete('0')
        size=s.join '-'

        if @names[name].nil?
          @names[name]={price:price, art:i, sizes:[size], extra:["#{size}~#{barcode}"]}
        else
          @names[name][:sizes]<<size
          @names[name][:extra]<<"#{size}~#{barcode}"
        end
      end
    end

    def run
      @names=Hash.new
      process_file(@price_file)
      process_cat("https://www.wildberries.ru/catalog/0/search.aspx?brand=19050&search=mf&sort=popular")
    end
  end
end
