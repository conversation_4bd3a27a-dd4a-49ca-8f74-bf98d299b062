#encoding:utf-8

module Forti
  class FortiParser < Downloader
  def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_page(url)
      puts url

      page=@agent.get url

      return if page.search('li.stock.minus').count>0

      cat=page.search('ul.breadcrumb li a')[1..2].map(&:text).join(' - ')

      code=page.search('input[name=product_id]').first.attr('value').to_s

      name=page.search('h1').first.text

      unless cat.include? ' - '
        if name.downcase.include? 'шапк'
          cat+=' - Шапки'
        elsif name.downcase.include? 'берет'
        cat+=' - Береты'
        elsif name.downcase.include? 'снуд'
          cat+=' - Снуды'
        elsif name.downcase.include? 'палантин'
          cat+=' - Палантины'
        elsif name.downcase.include? 'комплект'
          cat+=' - Комплекты'
        elsif name.downcase.include? 'кепк' or name.downcase.include? 'бейсболк'
          cat+=' - Кепки'
        else
          cat+=' - Разное'
        end
      end


      if /шапка$/=~name
        name.gsub!(/ шапка$/,'')
        name="Шапка #{name}"
      end

      if /кепка$/=~name
        name.gsub!(/ кепка$/,'')
        name="Кепка #{name}"
      end

      desc=page.search('span[itemprop=description]').text.strip

      props={}
      page.search('#tab-specification table tbody tr').each{|tr|
        props[tr.search('td')[0].text]=tr.search('td')[1].text.to_s.strip
      }

      desc+=". Состав: #{props['Состав']}" if props['Состав'] and props['Состав']!=''
      desc.gsub!('..','.')
      desc.gsub!(/^\. /,'')

      price=page.search('span[itemprop=price]').attr('content').to_s

      puts price

      return if price.to_i==0

      art=''
      if /itemprop="model">([^<]+)</=~page.body
        art=$1
      end

      art.force_encoding('utf-8')

      #return if product_added? "#{code} #{art}"

      variants=page.search('.owq-option tbody tr').map { |tr| tr.search('td')[-4].text.strip }

      puts variants.count

      return if variants.count==0

      pic_urls=[]

      pic_urls << page.search('img[itemprop=image]').attr('src').to_s if page.search('img[itemprop=image]').count>0

      page.search('.form-group .owl-item a.dataitem').each {|a| pic_urls<<a.attr('href').to_s}

      page.search('#image-additional a.dataitem').each {|a| pic_urls<<a.attr('href').to_s}

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}
      p2=@agent.post("http://forti.ru/index.php?route=product/product/getPImages&product_id=#{code}",{},ajax_headers)
      data=JSON.parse(p2.body)
      data['images'].each {|i| pic_urls<<i['main_img'] if i['main_img']}

      pic_urls=pic_urls.map {|url| url.gsub('/cache','').gsub(/-\d+x\d+/,'')}.uniq

      puts pic_urls

      return if pic_urls.empty?
      pics={}

      pic_urls.delete_if {|url| not url.start_with? 'http'}

      pic_urls.each_with_index {|url,i|
        fn="#{art}~#{i}".gsub('/','_')
        p=savePic(url,fn,true,true)

        @log.info "#{p} #{@picpath+'active/'+fn+'.jpg'}"

        pics[p]=Phashion::Image.new(@picpath+'active/'+fn+'.jpg')
      }

      puts pics.keys
      remove=[]
      pics.keys.reverse.each {|fn1|
        pic1=pics[fn1]
        pics.each {|fn2,pic2|
          break if fn2==fn1

          d=pic2.distance_from(pic1)
          puts "#{fn1} #{fn2} #{d}"
          if d<2
            remove<<fn1
            break
          end
        }
      }

      puts "Remove #{remove}"

      remove.each {|r| pics.delete r}
      puts pics.keys

      cat_type='857'
      cat_type='854' if cat.include? 'кепк' and cat.include? 'енщин'
      cat_type='1101' if cat.include? 'плать'
      cat_type='1180' if cat.include? 'ерчатк'

      cat_type='879' if cat.include? 'ужчин'

      cat_type='1298' if cat.include? 'етям'

      col=addCollection(cat,cat_type)

      path=URI(url).path

      addProduct(col,"#{code} #{art}",name,price,desc,variants,pics.keys,nil,0,nil,rrp:@rrp[path],source: url)
    end


    def process_cat(href)
      puts "Cat #{href}"
      page=@agent.get href

      page.search('.product-thumb h4 a').each do |a|
        process_page(a.attr('href').to_s)
      end
    end

  def process_cat_get_rrp(href)
    puts "Cat #{href}"
    page=@agent.get href

    page.search('.product-thumb').each do |div|
      a=div.at('h4 a')
      next unless a
      href=a.attr('href').to_s
      path=URI(href).path
      price=div.at('span.price-new').text.gsub(/\D/,'')
      @rrp[path]=price
    end
  end


  def run
      if @agent==nil then before end
      @rrp={}
      #page=@agent.get 'http://forti.ru/'

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

      form_data={email: @login, password: @password}


      process_cat_get_rrp('http://forti.ru/index.php?route=product/category&path=169&limit=5000')

      page = @agent.get 'http://forti.ru/zhenshchinam/'

      page.search('#sstore-3-level ul ul li a.list-group-item').each {|a|
        href=a.attr('href').to_s

        process_cat_get_rrp(href+'?limit=5000')
      }

      res=@agent.post('http://forti.ru/index.php?route=module/oct_popup_login/login',form_data,ajax_headers)

      page = @agent.get 'http://forti.ru/zhenshchinam/'

      unless page.search('a').find {|a| a.attr('href').to_s.include? 'logout'}
        raise 'Не получилось войти в аккаунт'
      end

      page.search('#sstore-3-level ul ul li a.list-group-item').each {|a|
        href=a.attr('href').to_s

        process_cat(href+'?limit=5000')
      }

      process_cat('http://forti.ru/index.php?route=product/category&path=169&limit=5000')


    end

  end

end

