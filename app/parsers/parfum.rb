#encoding: utf-8

require 'downloader'
#require 'json'
#require 'logger'
#require 'unicode_utils/downcase'
require 'csv'

class Parfum < Downloader
  attr_accessor :price_file

  def initialize
    @price_file=''
    @dlparams={'previous_purchase'=>:text,'price_file'=>:file,'login'=>:text,'password'=>:password}

    super
  end


  def processCat(url)
    @log.info(url)

    page = @agent.get(url)


    page.search('tr.goods_list').each do |tr|

      art=tr.search("a")[0].text.split(' ')[0]
      pic=nil
      if tr.search("img").length>0
        pic=tr.search("img")[0].attr('src').gsub('cache_','').gsub('/thumb150','')
      end

      if pic!=nil and pic!=''  and art!='' and art!=nil
	begin
      puts "#{art} #{pic}"

    	    savePic(pic,art,true)
	rescue Exception => e
	    puts "error saving #{pic}"
	end
      end


    end

  end



  def process_file(f)
    CSV.foreach(f,:col_sep=>';', :headers => true,:encoding=>'Windows-1251') do |fields|
      if fields[0].nil? then next end
      col=addCollection(fields[0],'100381')
      if fields[1].nil? then next end
      if fields[2].nil? then next end
      art=fields[1].strip
      name=fields[2].strip
      price=fields[3].strip.gsub(',','.')

      if art==nil then next end
      if price==nil then next end

      addProduct(col,art,'',price,name,[],[art])
    end
  end


  def run
    if @agent==nil then before end

    @agent.agent.http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    page = @agent.get('http://www.100sp.ru/login.php')

    login_form=page.form('login')
    login_form.email=@login
    login_form.password=@password
    page = @agent.submit(login_form, login_form.buttons.first)

    if page.body.include? 'fp_login_box' then raise 'Неверный логин/пароль для 100sp' end

    page = @agent.get("http://www.100sp.ru/purchase/#{@previous_purchase}")

    page.search("div.purchase-collection-list h3 a").each do |a|
      a=a.attr('href')
        processCat('http://www.100sp.ru'+a)
    end

    process_file(@price_file)

  end

  #handle_asynchronously :run

end