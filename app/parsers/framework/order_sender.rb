#encoding:utf-8
require_relative 'base_site_job'
require 'csv'

module Framework
  class OrderSender < Framework::BaseSiteJob
    attr_accessor :order_csv
    attr_accessor :for_new_site
    attr_accessor :agent_header
    attr_accessor :need_agent_header
    attr_accessor :file_result
    attr_accessor :pids
    attr_accessor :needed_cookies

    def initialize
      super
      @dlparams = { 'pids' => [:text, 'Номера закупок через запятую'] }.merge!(@dlparams)

      @dlparams['order_csv'] = :file

      @ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

      @needed_cookies = []
      @need_agent_header = false
      @csv_data = {}

      @prices = {}

      @error_csv_data = []
    end

    def add_error_csv_data(reason, articul, color = nil, size = nil, quantity = nil, link = nil)
      @error_csv_data << [articul, color, size, quantity, reason, link]
      error_func "ERROR: #{reason}. Articul: #{articul}, color: #{color}, size: #{size}"
    end

    def debug_func(msg)
      puts msg
      @log.debug msg
    end

    def error_func(msg)
      puts msg
      @log.error msg
    end

    def process
      @agent.user_agent = @agent_header if @need_agent_header

      @purchase.message = "Идет отправка заказа"
      @purchase.save

      parse_data

      @csv_data.each_with_index do |(art, sizes), i|
        @purchase.check_stop

        progress = (((i + 1).to_f / @csv_data.count.to_f) * 100).round

        @purchase.message = "Идет отрпавка заказов: #{progress}%"
        @purchase.save

        @purchase.send_ws_notification("Идет отрпавка заказов: #{progress}% (#{art})")

        order(art, sizes)
        if @agent
          #puts @agent.cookies
          if @needed_cookies and @needed_cookies.count > 0
            @purchase.cookies = @agent.cookies.select { |c| @needed_cookies.include?(c.name) }.map { |c| "#{c.name}=#{c.value}" }.join "\n"
          else
            @purchase.cookies = @agent.cookies.map { |c| "#{c.name}=#{c.value}" }.join '; '
          end
          @purchase.save!
        end
      end

      @purchase.message = "Заказ отправлен"
      @purchase.send_ws_notification("Отправка закончена", include_purchase: true)
      @purchase.save
      @purchase.save!
      create_wrong_csv_output
    end

    def get_headers
      ['Articul', 'Color', 'Size', 'Quantity', 'Reason']
    end

    def create_wrong_csv_output
      return if @error_csv_data.length == 0

      file_name = @file_path + 'wrong_data_' + Time.now.strftime('%Y-%m-%d_%H-%M') + '.csv'
      file_name_relative = @file_path_relative + 'wrong_data_' + Time.now.strftime('%Y-%m-%d_%H-%M') + '.csv'

      @purchase.message = "Не найдены некоторые товары. Проверьте последний файл wrong_data в #{@file_path}"
      @purchase.data = Hash.new if @purchase.data.nil?
      @purchase.data['error_file'] = file_name_relative
      @purchase.save

      CSV.open(file_name, 'wb',
               :write_headers => true,
               :col_sep => ';',
               :headers => get_headers #< column header
      ) do |headers|
        @error_csv_data.each do |f|
          headers << [f[0], f[1], f[2], f[3], f[4], f[5]]
        end
      end
    end

    def order(articul, quantity) end

    HEADERS = { 'status' => 'Статус',
                'articul' => 'Артикул',
                'name' => 'Наименование',
                'good_description' => 'Подробнее',
                'size' => 'Размер',
                'price' => 'Цена',
                'source' => 'Источник товара',
                'order_comment' => 'Примечание',
                'user_name' => 'Участник',
                'megaorder_id' => 'Мегазаказ',
                'oid' => '#',
                'user_org_comment' => 'Комментарий к участнику',
                'order_org_comment' => 'Комментарий к заказу'
    }

    def convert_headers(hash)
      ret = {}
      hash.each do |k, v|
        ret[HEADERS[k]] = v if HEADERS[k]
      end
      ret
    end

    def load_order_from_api
      api = SpApi.new

      @pids.each do |purchase|
        #@pids.gsub(/[^0-9,]/, '').split(',').uniq.each do |pid|
        pid=purchase['pid']
        api.change_purchase_status(pid, :processing) if purchase['switch_processing']

        orders = api.load_orders(@purchase, pid, skip_low_rating: !purchase['switch_processing'])
        if orders and orders.count>0
          skip_orders=[]
          SentOrder.create(pid: pid, skip_mids: skip_orders.join(','), purchase_id: @purchase.id)
        end

        orders.each do |line|
          fields = convert_headers(line)
          next if fields['Статус'] != 'ожидает подтверждения' && fields['Статус'] != 'не подтвержден'

          add_csv_data(fields)

          @lines_with_comments << fields if not fields['Примечание'].blank? or not fields['Комментарий к заказу'].blank? or not fields['Комментарий к участнику'].blank?
        end
      end
    end

    def load_order_from_file
      if @order_csv.is_a? Array
        @order_csv.each { |f| load_order_from_one_file(f) }
      else
        load_order_from_one_file(@order_csv)
      end
    end

    def load_order_from_one_file(f)
      str = File.open(f, 'r:utf-8', &:readline)
      sep = ';'
      if /sep=(.)/ =~ str
        sep = $1
      end

      CSV.foreach(f, :skip_lines => /^sep=/, :col_sep => sep, :headers => true, :encoding => 'utf-8', :liberal_parsing => true) do |fields|
        next if fields['#'] == '#' and fields['Цена'] == 'Цена'
        puts fields['Артикул']
        if @for_new_site
          add_csv_data_for_new_site(fields)
          next
        end
        # Сюда идет если выгрузка для 100сп
        next if fields['Статус'] != 'ожидает подтверждения' and fields['Статус'] != 'не подтвержден'
        add_csv_data(fields)
        @lines_with_comments << fields if not fields['Примечание'].blank? or not fields['Комментарий к заказу'].blank? or not fields['Комментарий к участнику'].blank?
      end
    end

    def parse_data
      @lines_with_comments = []

      if @pids and not @pids.empty?
        load_order_from_api
      else
        load_order_from_file
      end

      unless @lines_with_comments.empty?
        @purchase.send_ws_alert(@lines_with_comments.map(&:to_h), 'order_alert')
      end
    end

    def submit_order

    end

    def add_csv_data(fields)
      art = fields['Артикул'].to_s.gsub(/--\d+$/,'')
      @csv_data[art] += 1 if @csv_data.include? art
      @csv_data[art] = 1 unless @csv_data.include? art
      @prices[art] = fields['Цена']
    end

    def add_csv_data_for_new_site(fields)
      art = fields['Артикул'].to_s
      @csv_data[art] += 1 if @csv_data.include? art
      @csv_data[art] = 1 unless @csv_data.include? art
    end

    def before
      @purchase = Purchase.find(@purchase_id)

      if @purchase.nil?
        raise "Ошибка инициализации"
      end

      @log = Logger.new("log/dl-#{self.class.name.gsub('::', '_')}.txt")
      @log.level = Logger::DEBUG

      create_agent
    end

    def success(job)
      super
      return if @error_csv_data.length == 0

      @purchase.message = "Не найдены некоторые товары. Проверьте последний файл wrong_data в #{@file_path}"
      @purchase.save
    end

    def prepare_string(arg_str)
      return if arg_str.nil?
      str = arg_str
      # Обрабатывает строку и убирает всякие двойные пробелы, табуляции и прочие вещи

      str.gsub!("\t", ' ') # иначе можно посклеивать слова, где они только табом разделены
      str.gsub!(/ +/, ' ') # регэкспы наши большие друзья при обработке текста

      str.strip
    end

  end
end