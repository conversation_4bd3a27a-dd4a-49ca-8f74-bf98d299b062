#encoding: utf-8

require 'downloader'
#require 'json'
#require 'logger'
#require 'unicode_utils/downcase'
require 'spreadsheet'

module SportOptom
  class SportOptomParser < Downloader
    attr_accessor :price_file

    def initialize
      @dlparams={'price_file1'=>:file,'price_file2'=>:file,'price_file3'=>:file,
                 'price_file4'=>:file,'price_file5'=>:file,'price_file6'=>:file,'price_file7'=>:file}

      @pics=Hash.new {|hash,key| hash[key]=[] }

      super
    end


    def processPage(url,cat_tree)
      checkStop

      cat=cat_tree.join '-'

      puts url

      page = @agent.get(url)

      name=page.search('h1.title').first.text.to_s.strip
      puts name

      props={}
      page.search('ul.list-unstyled.description li').each do |li|
        k=li.search('b').text
        v=li.text.gsub(k,'').strip
        k=k.gsub(':','').strip
        props[k]=v
      end

      code=props['Код товара']

      art=props['Артикул']

      sost=props['Материал'].to_s

      desc=page.search('#tab-description').text.strip

      desc="#{desc}. Состав: #{sost}" if sost!=''

      name.gsub!(/ +/,' ')

      pics=page.search('#gallery_01 a.thumbnail').map{|a| a.attr('data-zoom-image').to_s}

      puts pics

      @data[code]={:code=>code, :art=>art, :name=>name,:desc=>desc,:pics=>pics,:cat=>cat, :cat_tree=>cat_tree}
    end

    def process_cat_data(page)
      cat_tree=page.search('ul.breadcrumb a')[1..2].map{|a| a.text.strip}

      page.search(".product-grid .caption h4 a:not(.brand)").each do |a|
        processPage(a['href'],cat_tree)
      end
    end

    def processCat(href)
      checkStop

      puts href

      page = @agent.get(href+'?pp=96')
      process_cat_data(page)
      ########
      return
      ######
      #
      if page.search('ul.pagination li a').count>1
        max=page.search('ul.pagination li a')[-2].text.to_i
        (2..max).each do |p|
          page2 = @agent.get(href+"?pp=96&page=#{p}")
          process_cat_data(page2)
        end
      end
    end

    def get_cat_id(cat1,name1)
      cat=cat1.downcase
      name=name1.downcase

      r=1267
      r=1202 if cat.include? 'скейт' or cat.include? 'круиз'
      r=109827 if cat.include? 'самокат'
      r=101046 if cat.include? 'вело запч'
      r=109863 if cat.include? 'единобор'
      r=826 if cat.include? 'футбол'
      r=109865 if cat.include? 'волейб'
      r=827 if cat.include? 'баскетб'
      r=830 if cat.include? 'хокке'
      r=1418 if cat.include? 'теннис' or cat.include? 'бадм'
      r=109864 if cat.include? 'дартс' or cat.include? 'билья'
      r=2842 if cat.include? 'художеств'
      r=828 if cat.include? 'плаван'
      r=109842 if cat.include? 'коньки'

      r=109600 if cat.include? 'тюби'
      r=1571 if cat.include? 'сноуб' or cat.include? 'лыжи'

      r=100395 if cat.include? 'туризм'
      r=100393 if cat.include? 'палатк'
      r=100392 if cat.include? 'рюкзак'
      r=100391 if cat.include? 'коврик' or cat.include? 'спальны'

      r
    end

    def process_file(f)
      puts f
      book = Spreadsheet.open f

      sheet1 = book.worksheet 0

      colName=''

      products={}

      sheet1.rows[21..-1].each_with_index do |row,i|
        next if row[2].nil?
        code=row[2].strip
        next if code==''

        price=row[6]
        next if price.nil?
        n2=row[3]
        (n,a,c,s)=n2.split(', ')
        s.gsub!(',','.') if s
        c.gsub!(',','.') if c

        s=s.to_s
        c=c.to_s

        next unless @data[code]

        desc=@data[code][:desc]
        cat=@data[code][:cat]
        cat_tree=@data[code][:cat_tree]
        pic_urls=@data[code][:pics]

        puts a,price

        pics=[]
        pic_urls.each_with_index  do |url,i|
          pics<<savePic(url,"#{code}~#{i}",true)
        end

        cat_id=get_cat_id(cat,n)

        products[code]={code:code,cat:cat,cat_id:cat_id,art:a,price:price,desc:desc,pics:pics, name:n,size:s,color:c,cat_tree:cat_tree}


      end

      merge_sizes(products)
    end

    def merge_sizes(products)
      ret={}

      @skip_arts=[]

      products.each do |code,prod|
        art=prod[:art]
        if art.nil? or art==''
          col=addCollection(prod[:cat],prod[:cat_id])

          pn=prod[:name]
          if prod[:color] and prod[:color]!=''
            pn="#{pn}. Цвет #{prod[:color]}"
          end

          addProduct(col,prod[:code],pn,prod[:price],prod[:desc],[prod[:size]],prod[:pics],{cat_tree:prod[:cat_tree],out:["#{prod[:size]}~#{code.gsub(/^0+/,'')}"]},prod[:cat_id])
        else
          next if @skip_arts.include? "#{art}~#{prod[:color]}"

          by_price={}
          same_art=products.values.select {|p| p[:art]==art}
          same_art.each do |v|
            by_price[v[:price]]=[] if by_price[v[:price]].nil?
            by_price[v[:price]]<<v
          end

          by_price.each do |price, prods|
            by_color={}
            same_color=prods.select {|p| p[:color]==prod[:color]}
            same_color.each do |v|
              by_color[v[:color]]=[] if by_color[v[:color]].nil?
              by_color[v[:color]]<<v
            end

            by_color.each do |color, prods2|
              sizes=prods.collect {|p| p[:size]}.uniq.sort
              sizes_codes=prods.collect {|p| [p[:size],p[:code].gsub(/^0+/,'')]}.map {|s,c| "#{s}~#{c}"}
              col=addCollection(prod[:cat],prod[:cat_id])

              pn=prod[:name]
              if prod[:color] and prod[:color]!=''
                pn="#{pn}. Цвет #{color}"
              end

              addProduct(col,prod[:code]+' '+art,pn,price,prod[:desc],sizes,prod[:pics],{cat_tree:prod[:cat_tree],out:sizes_codes},prod[:cat_id])
              @skip_arts<<"#{art}~#{prod[:color]}"
            end
          end
          end
      end

    end

    def run
      if @agent==nil then before end

      @data=Hash.new

      page = @agent.get('http://www.sportoptom.ru/')

      #processCat('https://sportoptom.ru/catalog/igrovye_vidy_sporta_setki_dlya_igrovyh_vidov_sporta')

      page.search(".navbar-collapse ul.nav > li.dropdown > .dropdown-menu > ul > li > a").each_with_index do |a|
                    processCat(a.attr('href').to_s.strip)
      end

      process_file(@price_file1)
      process_file(@price_file2) if @price_file2
      process_file(@price_file3) if @price_file3
      process_file(@price_file4) if @price_file4
      process_file(@price_file5) if @price_file5
      process_file(@price_file6) if @price_file6
      process_file(@price_file7) if @price_file7

      merge_small_collections(30)
    end

  end
end
