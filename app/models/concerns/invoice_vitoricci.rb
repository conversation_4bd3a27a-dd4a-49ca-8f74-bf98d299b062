module InvoiceVitoricci
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    site_art=fields['Наименование'].gsub('&amp;','&').downcase
    file_art=line['Название'].gsub('&amp;','&').downcase

    file_size = ''

    if / \(размер [12345SMLX]+ ([0-9-]+)/i =~ file_art
      file_size = $1
      file_art = file_art.gsub(/ \(размер.*/, '')
    end

    (site_art == file_art) and (fields['Размер'] == file_size)
  end
end