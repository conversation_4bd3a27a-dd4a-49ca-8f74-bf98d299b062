#encoding:utf-8
require_relative '../framework/parser'
require_relative 'limonti_product_config'

module Limonti
  class LimontiParser < Framework::Parser
    attr_accessor :login
    attr_accessor :password
    attr_accessor :stock_file
    attr_accessor :stock_data
    attr_accessor :stock_prices

    def get_page(link)
      begin
        @have_error_page = false
        @log.debug "Get page #{link}"
        return @agent.get(link)
      rescue Mechanize::ResponseCodeError => exception
        @log.error "Has exception on #{link}, response code - #{exception.response_code}"
        if exception.response_code == '404'
          @have_error_page = true
          return exception.page
        else
          raise # Some other error, re-raise
        end
      rescue
        @log.error "Has exception on #{link}, restart agent"
        @agent.shutdown
        @agent = create_agent
        @agent.request_headers
        sleep(5)
        return @agent.get(link)
      end
    end

    def process_products(category_page)
      return [] if @have_error_page
      super(category_page)
    end

    def initialize
      @dlparams={'login' => :text, 'password' => :password,'stock_file'=>:file}
      @stock_data=Hash.new { |h, k| h[k] = Hash.new(&h.default_proc) }
      @stock_prices=Hash.new { |h, k| h[k] = Hash.new(&h.default_proc) }

      super
    end


    def authorize
      page = @agent.get('http://limontishop.com/index.php')
      login_form=page.forms[0]
      login_form.USER_LOGIN=@login
      login_form.USER_PASSWORD=@password
      login_form.add_field!('ajax', 'y')
      login_form.action='https://limontishop.com/bitrix/templates/limonti/ajax/auth.php'

      ajax_headers = {'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}
      @agent.submit(login_form, login_form.buttons.first, ajax_headers)
      puts 'authorize'
    end

    def initialize_settings
      @agent.verify_mode = OpenSSL::SSL::VERIFY_NONE
      @added = []

      with_url('https://limontishop.com/index.php')
          .with_category_links(->(catalog_page){
            catalog_page.search('.drop_menu ul.side a').map{|a|a.attr('href')}.find_all{|link|
              not link.include? 'javascript' and not link.count('/')>3
            }
          })
          .with_pagination('a.next_page')
          .with_product_config(->(pc) {
            pc.with_product_selector(->(category_page) {
              result = []
              category_page.search('div.cat_item').each do |div|
                if div.search('a.name_class span.art').count>0
                  art=div.search('a.name_class span.art')[0].text.gsub('арт. ', '')
                  puts "art #{art}"
                  next if @added.include? art
                  next unless @stock_prices.has_key? art
                  @added<<art
                end

                price=div.search('p.price span.small').text
                next if price=='нет в наличии'

                result << {'product_link' => div.search('a').first.attr('href').to_s, 'main_picture' => div.search('img.fst').first.attr('src').to_s}
              end
              result
            })
                .with_parser(self)
                .with_category(->(product_page) {
                  if product_page.search('.bx_breadcrumbs ul li a').count>3
                    result = product_page.search('.bx_breadcrumbs ul li a')[2..3]

                    result = result.map { |a| prepare_string a.text}.join(', ')

                  else
                    result = prepare_string product_page.search('.bx_breadcrumbs ul li span')[-1].text
                  end

                  man_duplicate = result.include?('Мужское') and result.include? 'мужск'
                  women_duplicate = result.include?('Женское') and result.include? 'женск'
                  child_duplicate = result.include?('Детское') and result.include? 'детск'

                  if man_duplicate or women_duplicate or child_duplicate
                    result = result.split(', ').last.capitalize.to_s
                  end

                  result = result.gsub('бижутерия', '')
                  prepare_string result
                })
                .with_articul(->(product_page) {
                  @product_config.get_params(product_page)['Артикул'].gsub('/', '_')
                })
                .with_name(->(product_page) {
                  prepare_string product_page.search('//div[@class="detail_right"]/h1/text()[1]').to_s.gsub('*', '')
                })
                .with_price(->(product_page) {
                  product_page.search('.opt_price span.val')[0].text.gsub(' руб.', '').gsub(' ', '').strip.to_i
                })
                .with_description(->(product_page) {
                  prepare_string product_page.search('ul.dop_block li')[0].text.gsub(/[\r\n]/, ' ').gsub(/ +/, ' ')
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('https://limontishop.com')
                })
                .with_composition(->(product_page) {
                  params = @product_config.get_params(product_page)
                  return prepare_string params['Состав'] if params.has_key? 'Состав'
                })
                .with_sizes(->(product_page){
                  []
                })
                .with_category_type('850')
                .with_category_type('559', 'детск', 'белье')
                .with_category_type('546', 'детск', 'блузы')
                .with_category_type('561', 'детск', 'брюки')
                .with_category_type('551', 'детск', 'пижамы')
                .with_category_type('552', 'детск', 'платья')
                .with_category_type('556', 'детск', 'футболки')
                .with_category_type('556', 'детск', 'майка')
                .with_category_type('557', 'детск', 'шорты')
                .with_category_type('558', 'детск', 'юбки')
                .with_category_type('544', 'детск')
                .with_category_type('1914', 'бижутерия', 'кольца')
                .with_category_type('1916', 'бижутерия', 'колье')
                .with_category_type('1912', 'бижутерия', 'браслет')
                .with_category_type('1916', 'бижутерия', 'подвески')
                .with_category_type('1913', 'бижутерия', 'серьги')
                .with_category_type('1011', 'дома', 'полотенца')
                .with_category_type('1004', 'дома', 'постельные')
                .with_category_type('25', 'женск', 'аксессуары')
                .with_category_type('15', 'женск', 'блузки')
                .with_category_type('16', 'женск', 'жакеты')
                .with_category_type('24', 'женск', 'домашняя')
                .with_category_type('112', 'женск', 'бижутерия')
                .with_category_type('313', 'женск', 'обувь')
                .with_category_type('17', 'женск', 'брюки')
                .with_category_type('13', 'женск', 'водолазки')
                .with_category_type('4678', 'женск', 'джинсы')
                .with_category_type('303', 'женск', 'кардиганы')
                .with_category_type('2893', 'женск', 'купальники')
                .with_category_type('128', 'женск', 'куртки')
                .with_category_type('102', 'женск', 'носки')
                .with_category_type('1541', 'женск', 'пуховики')
                .with_category_type('15', 'женск', 'рубашки')
                .with_category_type('351', 'женск', 'шарфы')
                .with_category_type('114', 'женск', 'сумки')
                .with_category_type('304', 'женск', 'толстовки')
                .with_category_type('307', 'женск', 'туники')
                .with_category_type('1373', 'женск', 'футболки')
                .with_category_type('304', 'женск', 'худи')
                .with_category_type('7', 'женске', 'шапки')
                .with_category_type('1360', 'женск', 'шорты')
                .with_category_type('351', 'женск', 'палантин')
                .with_category_type('2893', 'женск', 'белье')
                .with_category_type('102', 'женск', 'колготки')
                .with_category_type('26', 'женск', 'комбинезон')
                .with_category_type('19', 'женск', 'юбки')
                .with_category_type('129', 'женск', 'пальто')
                .with_category_type('20', 'женск', 'платья')
                .with_category_type('286', 'женск', 'плащи')
                .with_category_type('1104', 'женск', 'сарафаны')
                .with_category_type('301', 'женск', 'свитеры')
                .with_category_type('949', 'женск', 'спорт')
                .with_category_type('14', 'женск', 'топы')
                .with_category_type('128', 'женск', 'куртка')
                .with_category_type('872', 'мужск', 'трусы')
                .with_category_type('118', 'мужск', 'домашняя')
                .with_category_type('956', 'мужск', 'спортивная')
                .with_category_type('70', 'мужск', 'аксессуары')
                .with_category_type('31', 'мужск', 'брюки')
                .with_category_type('957', 'мужск', 'ветровки')
                .with_category_type('32', 'мужск', 'джемпера')
                .with_category_type('4679', 'мужск', 'джинсы')
                .with_category_type('879', 'мужск', 'шапки')
                .with_category_type('33', 'мужск', 'куртки')
                .with_category_type('120', 'мужск', 'рубашки')
                .with_category_type('30', 'мужск', 'футболки')
                .with_category_type('906', 'мужск', 'шарфы')
                .with_category_type('877', 'мужск', 'шорты')
                .with_category_type('916', 'мужск', 'пуховик')
                .with_category_type('1444', 'мужск', 'толстовки')
                .with_create_new_agent_func(->() { create_agent })

          }, Limonti::LimontiProductConfig)
    end

    def read_stock_file(stock_file)
      book = Spreadsheet.open stock_file

      ws = book.worksheet 0

      ws.each_with_index do |row, index|
        next if row[0].nil?

        art=row[0].to_s.gsub(/\.0$/,'')
        (n,s,c)=row[1].split(',').map(&:strip)

        price=row[2].to_f.ceil

        @stock_data[art][c]=[] if @stock_data[art][c].empty?
        @stock_data[art][c]<<s
        @stock_prices[art][c]=price
      end
    end

    def process
      read_stock_file(@stock_file) if @stock_file
      super
    end

    def create_agent
      result = super
      result.verify_mode = OpenSSL::SSL::VERIFY_NONE
      authorize
      result
    end
  end
end