#encoding: utf-8

require 'downloader'

class Gia < Downloader

  def initialize
    @dlparams={}

    super
  end


  def processPage(url)
    tries ||= 2

    @log.info(url)
    page = @agent.get(url)
    puts url

    return if page.search('div#path a').count<3

    cat=page.search('div#path a')[1].text+'-'+page.search('div#path a')[2].text
    name=page.search('#content h1')[0].text.strip
    art=page.search('#content h1')[0].attr('data-product')

    desc=page.search('.description p').map(&:text).join '. '

    desc=desc.gsub(/\u00a0/, ' ').gsub(/ +/,' ')
    desc=desc.gsub(/&gt;&gt;.*?&lt;&lt;/,'')
    desc=desc.gsub(/>>.*?<</,'')

    sizes=page.search('form.variants tr.variant td')[0].to_s.scan(/р\.(.*?)<br/).map{|t| t[0]}

    price=page.search('span.price')[0].text.gsub('руб','').gsub(' ','').to_f*0.9
    price=price.ceil

    pic_urls=page.search('//div[@class="images"]/a[@data-rel="group"]').map{|a| a.attr('href')}
    puts pic_urls
    pics=[]
    pic_urls.each_with_index do |url,i|
      savePic(url,"#{art}~#{i}",false)
      pics<<"#{art}~#{i}"
    end

    col=addCollection(cat,'850')
    addProduct(col,art,name,price,desc,sizes,pics)


  rescue Mechanize::Error => e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(url)
    @log.info(url)
    page = @agent.get(url)

    page.search('ul.products li.product h3 a').each do |a|
      processPage(a.attr('href'))
    end

  end


  def run
    page=@agent.get 'http://gia-maria.ru/'


    page.search("#catalog_menu li").each do |li|
      if li.search('li').length==0
        processCat(li.search('a')[0].attr('href')+'?page=all')
      end

    end

  end
  #handle_asynchronously :run

end