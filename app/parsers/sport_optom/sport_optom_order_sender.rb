module SportOptom
  class SportOptomOrderSender < Framework::OrderSender

    def initialize
      super
      @file_result=true

      @data=Hash.new
    end

    def add_csv_data(fields)
      extra=fields['Источник товара']
      t=extra.split('^')
      size_map=Hash[*t.map {|x| x.split('~')}.flatten]
      size=fields['Размер']
      name=fields['Наименование']

      if size=='-'
        code=fields['Артикул'].gsub(/ .*/,'')
      else
        code=size_map[size]
      end

      code="00#{code}" if code.start_with? '-'

      art=fields['Артикул'].gsub(/^[0-9-]+ /,'')

      if @data[code].nil?
        @data[code]={art:art,name:name,size:size,count:1}
      else
        @data[code][:count]+=1
      end
    end

    def process
      before

      super

      book = Spreadsheet::Workbook.new
      sheet = book.create_worksheet

      sheet[0,0]='Код'
      sheet[0,1]='Артикул'
      sheet[0,2]='Наименование'
      sheet[0,3]='Размер'
      sheet[0,4]='Количество'

      i=1
      @data.each { |code,data|

        sheet[i,0]=code.to_s.rjust(8, "0")
        sheet[i,1]=data[:art]
        sheet[i,2]=data[:name]
        sheet[i,3]=data[:size]
        sheet[i,4]=data[:count]
        i+=1
      }

      spreadsheet = StringIO.new

      book.write spreadsheet

      d=DateTime.now
      df=d.strftime('%Y%m%d')

      after(nil)

      return ["sportoptom_order-#{df}.xls",'application/vnd.ms-excel',spreadsheet.string]
    end
  end
end