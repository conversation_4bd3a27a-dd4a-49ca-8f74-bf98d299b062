#encoding:utf-8
require_relative '../framework/parser'
require_relative 'setty_product_config'
require 'spreadsheet'

module Setty
  class SettyParser < Framework::Parser
    def initialize
      @dlparams = {'price_file' => :file}
      super
    end

    def initialize_settings
      excel_products = parse_excel

      with_url('http://www.setty.ru/')
        .with_category_links('/index.php?option=com_virtuemart&page=shop.browse&category_id=39&Itemid=212', true)
        .with_category_links('/index.php?option=com_virtuemart&page=shop.browse&category_id=40&Itemid=212', true)
        .with_category_links('/index.php?option=com_virtuemart&page=shop.browse&category_id=38&Itemid=212', true)
        .with_category(->(category_page){
          prepare_string category_page.search('#vmMainPage h3').first.text.capitalize
        })
        .with_product_config(->(pc){
          pc.with_product_selector('.browseProductImageContainer > a')
            .with_articul(->(product_page){
              prepare_string product_page.search('#Артикул_field option').first.text
                                 .gsub('В', 'B').gsub('М', 'M').gsub('Р', 'P').gsub('С', 'C')
            })
            .with_name(->(product_page){
              prepare_string product_page.search('#vmMainPage td > p').first.text
            })
            .with_images(->(product_page){
              images = product_page.search('a.MagicZoom').map{|a|a.attr('href')}
              images.concat product_page.search('.MagicScroll > a').map{|a|a.attr('href')}

              images.uniq
            })
            .with_image_config(->(ic){
              ic.with_default_url('http://www.setty.ru/')
            })
            .with_description(->(product_page){
              p = product_page.search('td[valign=top] > p').to_a.find{|p|
                p.text.downcase.to_s.include? 'описание'
              }

              return prepare_string p.text.gsub('Описание:', '')

              span = product_page.search('td > p > span').to_a.find{|p|
                strong = p.search('strong')
                strong.length > 0 and strong.text.downcase.to_s.include? 'описание'}

              prepare_string span.text.gsub('Описание:', '')
            })
            .with_sizes(->(product_page){
              product_page.search('#Размер_field option').map{|opt|prepare_string opt.text}
            })
            .with_composition(->(product_page){

              p = product_page.search('td[valign=top] > p').to_a.find{|p|
                p.text.downcase.to_s.include? 'состав'
              }

              if p.nil?
                puts 'ERROR CANNOT FIND COMPOSITION'
                puts product_page.uri.to_s
                return ''
              end

              return prepare_string p.text.gsub('Состав:', '')
              span = product_page.search('td > p > span').to_a.find{|p|
                strong = p.search('strong').first

                not strong.nil? and strong.text.downcase.to_s.include? 'состав'}
              prepare_string span.text.gsub('Состав:', '')
            })
            .with_excel_products(->(){
              excel_products
            })
            .with_category_type('302', 'блузон')
            .with_category_type('17', 'брюки')
            .with_category_type('302', 'джемпер')
            .with_category_type('52', 'жилет')
            .with_category_type('303', 'кардиган')
            .with_category_type('128', 'куртка')
            .with_category_type('129', 'пальто')
            .with_category_type('20', 'платье')
            .with_category_type('307', 'туника')
            .with_category_type('19', 'юбка')
            .with_category_type('15', 'блузка')
            .with_category_type('17', 'бриджи')
            .with_category_type('16', 'жакет')
            .with_category_type('26', 'комбинезон')
            .with_category_type('14', 'топ')
            .with_category_type('1104', 'сарафан')
            .with_category_type('286', 'плащ')
            .with_category_type('1360', 'шорты')
        }, Setty::SettyProductConfig)
    end

    def parse_excel
      result = []
      book = Spreadsheet.open @price_file

      book.worksheets.take(3).each do |ws|
        category = prepare_string ws.rows[0][0].to_s

        ws.each do |row|
          # Если нет цены
          next if row[3].nil?

          name = prepare_string row[1].capitalize.to_s
          articul = prepare_string row[2].gsub('В', 'B').gsub('М', 'M').gsub('Р', 'P').gsub('С', 'C')
          is_sale = false
          is_new = name.include? 'new'

          name = prepare_string name.gsub('new', '') if is_new

          price = row[3].to_i

          unless row[4].nil?
            is_sale = true
            price = row[4].to_i
          end

          additional = {
              'is_sale' => is_sale,
              'is_new' => is_new
          }

          result.push Framework::SizeableParsedProduct.new(category, articul, [], name, '', price, [], nil, additional)

          @log.debug " #{category} #{articul} #{name} #{price} #{additional}"
          puts " #{category} #{articul} #{name} #{price} #{additional}"
        end
      end

      result
    end
  end
end

