class SpLoadBrands
  include SpAuthorize

  def initialize
    @agent = Mechanize.new
    authorize
    super
  end

  def render_svg(fn)
    img = Magick::Image::read(fn).first
    img.resize_to_fit!(512, 512)
    f = fn.gsub('.svg', '') + '.png'
    img.write(f)
    f
  end

  def migrate_brands
    if SpBrand.all.count == 0
      Cache.where("key like 'brand_ids%'").each do |c|
        name = c.key.gsub('brand_ids_', '')
        sp_id = c.data.to_i
        SpBrand.find_or_create_by(name: name, sp_id: sp_id)
      end
    end

  end

  def create_brand(name, cats, desc, pic)
    return if name.strip==''

    puts "Create #{name},#{cats},#{pic}"
    data = { 'YiiApp_modules_org_modules_purchase_components_BrandTagForm[name]' => name,
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[no_trademark]' => 0,
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[is_public]' => 1,
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[description]' => desc,
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[alias]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[makerCountry]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[makerSite]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[category1]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[category2]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[category3]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[category4]' => '',
             'YiiApp_modules_org_modules_purchase_components_BrandTagForm[image]' => ''
    }

    cats.each_with_index { |cat, i| data["YiiApp_modules_org_modules_purchase_components_BrandTagForm[category#{i + 1}]"] = cat }

    if pic.nil? or not File.exist? pic
      pic = create_brand_logo(name)
    end

    data['YiiApp_modules_org_modules_purchase_components_BrandTagForm[image]'] = File.new(pic, "rb")

    p = @agent.post 'https://www.100sp.ru/org/purchase/brandTag/create', data
    nil
  end

  def create_samson_brand(brand)
    cats = Hash.new(0)
    Product.where(brand_name: brand).find_each do |p|
      cats[p.category] += 1
    end

    cats = cats.sort_by { |k, v| -v }.to_h.keys[0..4]

    create_brand(brand, cats, '', nil)
  end

  def get_new_brands(dl)
    purchases = Purchase.where(dlclass: dl)
    new_brands = Set.new
    purchases.each do |purchase|
      new_brands+=get_new_brands_by_purchase purchase
    end
    new_brands
  end

  def get_new_brands_by_purchase(purchase)
    new_brands = Set.new
    col_ids = purchase.collections.select(:id).map(&:id)
    brands = Product.where(disabled: false, collections_products: { collection_id: col_ids }).joins('INNER JOIN collections_products on products.id=collections_products.product_id').select(:brand_name).distinct.map(&:brand_name)
    new_brands += brands.select { |b|
      b != nil and
        b != 'Укажите бренд' and
        SpBrand.where("lower(name)=:a or aliases ?| array[:a]", a: [b.downcase]).count == 0
    }
    new_brands
  end

  def create_4fresh_brands
    new_brands = get_new_brands('ForFresh::ForFreshParser')
    puts new_brands

    p = @agent.get "https://4fresh.ru/brands/"

    p.search('#farmerslist > section ul.col li article, #brandslist > section ul.col li article').each do |article|
      name = article.search('div.title')[0].text.to_s.strip
      next unless new_brands.any? { |b| b.downcase == name.downcase }

      desc = article.search('div.desc.text-block')[0].text.to_s.strip
      pic = article.at('figure img')

      if pic and not pic.attr('data-src').blank?
        url = pic.attr('data-src')
        fn = Dir.tmpdir + "/brandpic"
        @agent.download('https://4fresh.ru'+url, fn)
        if url.end_with? 'svg'
          fn = render_svg(fn)
        else
          File.rename(fn,fn+'.png')
          fn+='.png'
        end
      else
        fn = create_brand_logo(name)
      end

      cats = Hash.new(0)
      Product.where.not(category:nil).where(brand_name: name).find_each do |p|
        cats[p.category] += 1
      end
      cats = cats.sort_by { |k, v| -v }.to_h.keys[0..3]
      create_brand(name, cats, desc, fn)

    end
  end

  def create_kristaller_brand(brand)
    p = @agent.get "https://kristaller.pro/vendors/"
    unless @krist_pics
      @krist_pics = {}
      p.search('.vendors-section-item').each do |div|
        name = div.search('span.item-title').text.to_s.strip
        pic = div.at('span.image img')
        if pic
          url = pic.attr('src').to_s.gsub('resize_cache/', '').gsub('69_24_1/', '')
          @krist_pics[name] = url
        end
      end
    end

    if @krist_pics[brand]
      fn = Dir.tmpdir + "/brandpic.png"
      @agent.download(@krist_pics[brand], fn)
    end

    cats = Hash.new(0)
    Product.where(brand_name: brand).find_each do |p|
      cats[p.category] += 1
    end

    cats = cats.sort_by { |k, v| -v }.to_h.keys[0..4]

    create_brand(brand, cats, '', fn)
  end

  def create_sima_brand(brand)
    # @agent.set_proxy("**************", 8080) unless Rails.env.development?

    r = @agent.get "https://www.sima-land.ru/api/v3/trademark/?name=#{brand}"
    d = JSON.parse(r.body)
    return unless d['items'] and d['items'][0]
    if d['items'][0]['photo']
      pic = d['items'][0]['image']
      fn = Dir.tmpdir + "/brandpic"
      @agent.download(pic, fn)
      img = Magick::Image::read(fn).first
      if img.format == 'PNG'
        img_list = Magick::ImageList.new
        img_list.read(fn)
        img_list.new_image(img_list.first.columns, img_list.first.rows) { |pic| pic.background_color = "white" }
        img = img_list.reverse.flatten_images
      end
      fn += '.png'
      img.write(fn)
    end

    desc = d['items'][0]['description']

    cats = Hash.new(0)
    Product.where(brand_name: brand).find_each do |p|
      cats[p.category] += 1
    end

    cats = cats.sort_by { |k, v| -v }.to_h.keys[0..4]

    create_brand(brand, cats, desc, fn)
  end

  def create_sima_brands
    new_brands = get_new_brands('Simaland::SimalandParser')
    puts new_brands
    new_brands.each do |b|
      create_sima_brand(b)
    end
  end

  def create_samson_brands
    new_brands = get_new_brands('SamsonOld')
    new_brands.each do |b|
      create_samson_brand(b)
    end
  end

  def create_krist_brands
    new_brands = get_new_brands('Kristaller::KristallerParser')
    new_brands.each do |b|
      create_kristaller_brand(b)
    end
  end

  def create_plusminus_brands
    new_brands = get_new_brands_by_purchase(Purchase.find(242))
    new_brands.each do |b|
      create_brand_without_logo(b)
    end
  end

  def create_arti_brands
    new_brands = get_new_brands_by_purchase(Purchase.find(330))
    new_brands.each do |b|
      create_brand_without_logo(b)
    end
  end

  def create_brand_without_logo(brand)
    cats = Hash.new(0)
    Product.where(brand_name: brand).find_each do |p|
      cats[p.category] += 1
    end

    cats = cats.sort_by { |k, v| -v }.to_h.keys[0..4]
    fn = create_brand_logo(brand)
    create_brand(brand, cats, '', fn)
  end

  #  def create_nopics_brands(purchase_id)
  #    purchase = Purchase.find purchase_id
  #    col_ids = purchase.collections.select(:id).map(&:id)
  #    brands = Product.where(disabled: false, collections_products: { collection_id: col_ids }).joins('INNER JOIN collections_products on products.id=collections_products.product_id').select(:brand_name).distinct.map(&:brand_name)
  #    new_brands = brands.select { |b| b != nil and b != 'Укажите бренд' and SpBrand.find_by_name(b.gsub(' ', '').upcase).nil? }
  #    new_brands.each do |b|
  #      create_nopic_brand(b)
  #    end
  #end

  def create_brands
    load_brands
    create_4fresh_brands
    create_sima_brands
    create_samson_brands
    create_krist_brands
    create_plusminus_brands
    create_arti_brands
    load_brands(3)
  end

  def create_brand_logo(text)
    text = text.split.map(&:first).join.upcase
    new_img = ::Magick::Image.new(500, 500)
    new_img.format = 'PNG'
    new_img = new_img.color_floodfill(1, 1, ::Magick::Pixel.from_color('#ffffff'))

    text_container = Magick::Image.read("label:#{text}") { |pic|
      pic.font = 'arial'
      pic.stroke = 'transparent'
      pic.fill = '#000000'
      pic.size = "500x500"
    }.first

    new_img.composite!(text_container, 0, 50, Magick::SrcOverCompositeOp)
    fn = "#{Dir.tmpdir}/#{text}.png"
    new_img.write(fn)
    fn
  end

  def process_brand_page(page, global)
    page.search('table.items tbody tr').each do |tr|
      id = tr.search('td')[1].search('small')[0].text.strip
      name = tr.search('td')[1].search('a')[0].text.strip
      aliases = tr.search('td.alias')[0].text.to_s.downcase.split(',').map(&:strip).uniq
      aliases.delete('')
      b = SpBrand.find_by_sp_id(id)
      if b
        b.aliases = aliases
        b.name = name
        b.global = global
        b.save
      else
        b = SpBrand.new(sp_id: id, name: name, global: global)
        b.aliases = aliases
        b.save
      end
    end

  end

  def load_brands(max_pages = nil)
    next_page = 'https://www.100sp.ru/org/purchase/brandTag/index?BrandTag[is_global]=1'
    p = 0
    while next_page do
      page = @agent.get next_page
      process_brand_page(page, true)
      next_page = page.at('.pagination li.next:not(.disabled) a')&.attr('href')
      p += 1
      break if max_pages and p >= max_pages
    end

    next_page = 'https://www.100sp.ru/org/purchase/brandTag/index?BrandTag[is_global]=0'
    while next_page do
      page = @agent.get next_page
      process_brand_page(page, false)
      next_page = page.at('.pagination li.next:not(.disabled) a')&.attr('href')
      p += 1
      break if max_pages and p >= max_pages
    end

  end
end