#encoding: utf-8

module InvoiceIvanovo
  extend ActiveSupport::Concern

  def unprocessed_invoice?
    true
  end

  def read_invoice(file)
    ret=[]
    workbook = Spreadsheet.open(file)
    sheet = workbook.worksheet(0)


    code_num=get_col_number(sheet,'Артикул')
    name_num=get_col_number(sheet,'Товары (работы, услуги)')
    q_num=get_col_number(sheet,'Количество')
    price_num=get_col_number(sheet,"Сумма")

    raise "Bad file" if not code_num or not name_num or not q_num

    invoice_name=''

    in_data=false

    sheet.each do |row|
      invoice_name=row[2].strip if row[2].to_s.include? 'Заказ клиента'

      next if row.nil?

      next if row[code_num].blank?


      if row[code_num]=='Артикул'
        in_data=true
        next
      end

      next unless in_data

      art=row[code_num].to_s.strip.gsub(/\.0$/,'')
      name=row[name_num]
      q=row[q_num].to_i

      price='0'
      price=row[price_num].to_f/q.to_f if price_num

      ret<<{'Артикул'=>art,'Название'=>name,'Количество'=>q, 'Цена закуп'=>price,'#invoice_name'=>invoice_name}
    end
    ret
  end

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)

    t=line['Название']
    return if t.blank?
    t.gsub!('(2 шт.)','')
    t.gsub!('(джерси)','')
    t.gsub!('(220)','')
    #puts t
    color=''
    size=''

    matches=t.scan(/\(((?>[^)(]+|\g<0>)*)\)/)
    if matches.last
      color=matches.last[0]
      if /(\d+) размер, (.*)/=~color
        size=$1
        color=$2
      end

      if /(\S+) размер/=~color and size==''
        size=$1
        color=''
      end
    end


    site_color=''

    if / ([А-ЯA-ZЁ].*)/i=~fields['Артикул']
      site_color=$1
    end

    site_color.gsub!(/ +/,' ')

    site_color='' if site_color=='клеточка в ассортименте'

    site_size=''
    site_size=fields['Размер'] if /^\d{2}$/=~fields['Размер']

    if /^(\d{2})\.\d{2}$/=~fields['Размер']
      site_size=$1
    end

    #    color=''
#    size=''
#    if /\((\S+) размер,((?>[^)(]+|\g<0>)*)\)/=~t
#      size=$1
#      color=$2
#      color.strip!
#    elsif /\((\S+) размер\)$/=~t
#      size=$1
#    elsif /\(((?>[^)(]+|\g<0>)*)\)/=~t
#      color=$1
#      color=color.split('(')[1] if color.include? '('
#      color.strip!
#    end

#   if color.include? 'вороту'
#      size=color
#      color=''
#    end

    size.gsub!(/ .*/,'')
    site_size.gsub!(/ .*/,'')

    color='' if color.upcase.include? 'РОСТ'

#color.gsub!(')','')
#   color.gsub!(/ +/,' ')

    site_color='' if site_color=='Не указано'

    site_art=fields['Артикул'].gsub(site_color,'').strip.upcase

    line['Артикул'].strip.upcase==site_art and color.upcase==site_color.upcase and size==site_size
  end

end