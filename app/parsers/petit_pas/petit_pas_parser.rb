#encoding:utf-8
require_relative '../framework/parser'

module PetitPas
  class PetitPasParser < Framework::Parser
    attr_accessor :price_file
    attr_accessor :prices

    def initialize
      @dlparams={'price_file' => :file}
      @prices=Hash.new
      super
    end


    def initialize_settings
      @agent.verify_mode = OpenSSL::SSL::VERIFY_NONE
      with_url('https://petitpas-shop.com/catalog/')
        .with_category_links(->(category_page){
          result = []
          list_items = category_page.search('.leftMenu > li')#.drop(1)
          #list_items[0], list_items[-1] = list_items[-1], list_items[0]
          list_items.each{ |li|
            hrefs = li.search('a')
            if hrefs.length == 1
              result << hrefs[0]
              next
            end
            result.concat(li.search('ul li a'))
          }
          indexes = result.each_index.select{|i|result[i].text.include? 'Plus'}
          indexes.each_with_index {|index, i|
            # Если индекс меньше количества, то оставляем на месте
            next if index < indexes.length - 1

            # Замещаем с той ссылкой, индекс которой совпадает с i
            result[i], result[index] = result[index], result[i]
          }
          result.map{|a|a.attr('href').to_s}.uniq
        })
        .with_category(->(category_page){
          category_page.search('#breadcrumbs span')[-2..-1].map {|s| s.text.strip}.join '-'
          #unless category_page.search('.leftMenu').first.nil?
          # @category = get_category(category_page)
          #end
          #@category
        })
        .with_pagination('#linkToNext > a')
        .with_product_config(->(pc){
          pc.with_logging(->(msg){@log.info msg}, ->(msg){@log.info msg})
          pc.with_product_selector('.catDescr > a')
            .with_articul(->(product_page){
              text = prepare_string product_page.search('.itemOptions').first.text
              @log.info "Art #{text} url #{product_page.uri}"
              if text.include? '_'
                text = text.match(/Артикул:\s*(.*)/).captures[0].to_s
                return text.split('_')[0]
              end
              return text.match(/Артикул:\s*(?<articul>[A-Z\d]+)_?(?<color>.*)/)['articul']
            })
            .with_parser(self)
            .with_name(->(product_page){
              prepare_string product_page.search('h1').first.text
            })
            .with_rrp(->(product_page){
              price = product_page.search('#itemPrice > span').first.text.gsub(' ', '').gsub(',', '.').to_f
              price = product_page.search('#itemPrice span.pricesale').first.text.gsub(' ', '').gsub(',', '.').to_f if product_page.search('#itemPrice span.pricesale').count>0
              price

            })
            .with_sizes(->(product_page){
              product_page.search('#chooseSizeForm > a:not(.notHover)').map{|a|prepare_string a.text}
            })
            .with_description(->(product_page){
              props={}
              if product_page.at('.itemOptions .accordion > p')
                product_page.search('.itemOptions .accordion > p').each do |p|
                  title= p.text.strip
                  text=p.next_element.text
                  props[title]=text
                end
                return prepare_string "#{props['Описание'].to_s} #{props['Состав'].to_s} #{props['Уход за изделием'].to_s} #{props['Параметры модели'].to_s}"
              else
                return prepare_string product_page.search('.itemOptions').last.text
              end
            })
            .with_images(->(product_page){
              images = product_page.search('#smallItemsPictures > a').map{|a|a.attr('data-zoom-image').to_s}
              images.concat(product_page.search('#bigImageItem img').map{|a|a.attr('data-zoom-image').to_s})
              images.uniq
            })
            .with_image_config(->(ic){
              ic.with_default_url('https://petitpas-shop.com')
            })
            .with_category_type('850')
            .with_category_type('314', 'босоножки')
            .with_category_type('1930', 'домашняя', 'plus')
            .with_category_type('1929', 'домашняя', 'беременных')
            .with_category_type('24', 'домашняя', 'взрослых')
            .with_category_type('551', 'домашняя', 'детей')
            .with_category_type('318', 'обувь', 'балетки')
            .with_category_type('53', 'обувь', 'бантики')
            .with_category_type('415', 'обувь', 'детская')
            .with_category_type('415', 'обувь', 'каблучки')
            .with_category_type('80', 'отдыха', 'plus')
            .with_category_type('206', 'отдыха')
            .with_category_type('1003', 'сна')
            .with_category_type('100381', 'парфюм')
            .with_category_type('351', 'шали')
            .with_category_type('88', 'украшения')
        }, PetitPas::PetitPasProductConfig)
    end

    def get_category(category_page)
      current_li = category_page.search('.leftMenu li.catalogCurrent').first
      if current_li
        return prepare_string current_li.search('a').first.text
      end
      current_li = category_page.search('.leftMenu li').find{|li| not li.search('.catalogCurrent').first.nil?}
      parent = prepare_string current_li.search('a').first.text
      child = prepare_string current_li.search('.catalogCurrent > a').first.text
      "#{parent}, #{child}"
    end

    def process_file(f)
      CSV.foreach(f,:col_sep=>';', :headers => false,:encoding=>'Windows-1251') do |fields|
        art=fields[0]
        price=fields[1]
        @prices[art]=(price.gsub(',','.').to_f).ceil
      end
    end

    def process
      process_file(@price_file)
      super
    end
  end
end