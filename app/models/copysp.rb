#encoding: utf-8

require 'downloader'
require 'csv'
#require 'logger'
#require 'unicode_utils/downcase'

class Copysp < Downloader
  attr_accessor :spid
  attr_accessor :merge_cols
  attr_accessor :filter_file

  def initialize
    @dlparams={'spid'=>:text,'merge_cols'=>:text,'filter_file'=>:file}
    @piccnt=0
    super
  end

  def processCol(href)
    page = @agent.get(href)
    #pp page
    catname=page.search('h1')[0].text.strip

    page.search('tr.goods_list').each do |tr|
      art=tr.search('./td[2]/a/text()').to_s.strip
      pic=tr.search('./td[3]//a/img/@src').to_s.gsub('cache_','').gsub('/thumb150','')
      price=tr.search('./td[4]/text()').to_s.strip.gsub(',','.').to_f/(1+@rate/100)
      price=price.round(2)

      desc=tr.search('./td[5]').text.strip
      sizes=[]
      tr.search('td.sizes_orders td').each_slice(3) do |t|
        sizes<<t[0].text.strip
      end

      image=savePic(pic,@piccnt,true)

      if not @merge_cols.nil? and not @merge_cols==''
        catname=@merge_cols
      end

      if not @filter_price.nil?
        art.gsub!(/^[0-9]+ /,'')
        if @filter_price.include? art
          price=@filter_price[art]
        else
          next
        end
      end


      n=''
      if tr.search('span.articul-for-user').length>0
        n=art
        art=tr.search('span.articul-for-user').text.gsub('Артикул:','').strip
      end


      col=addCollection(catname,@catTypeId)
      addProduct(col,art,n,price,desc,sizes,[image])

      @piccnt+=1
    end
  end


  def createCatMap(parent,prefix)

    begin
      c=Cache.find_by_key("100sp_catmap_#{parent}")
      text=c.data
    rescue
      page=@agent.post 'http://www.100sp.ru/getCategories',{:id=>parent}
      text=page.body
      Cache.create(key:"100sp_catmap_#{parent}",data: text)
    end

    d=JSON.parse(text)
    d['data'].each do |k,v|
      puts k
      t=prefix.clone
      t<<v
      puts t.join(' / ')
      puts ''
      @catMap[t.join(' / ')]=k
      createCatMap(k,t)

    end
  end

  def read_filter_file
    @filter_price=Hash.new
    CSV.foreach(@filter_file,:col_sep=>';', :headers => false,:encoding=>'Windows-1251') do |fields|
      art=fields[0].strip
      price=fields[1].strip
      if not art.nil?
        @filter_price[art]=price
      end
    end
  end

  def process_purch(pid,cols=nil)
    page = @agent.get("http://www.100sp.ru/purchase.php?pid=#{pid}")

    @rate=page.search('table.table td')[2].text.gsub('%','').to_f
    puts @rate

    page.search("//div[@class='purchase-collection-list']/div").each do |div|
      cid=div.attr('id')
      puts cid
      next if not cols.nil? and not cols.include? cid

      catType=div.search('./div[2]/text()[2]')[1].to_s.strip
      puts catType
      @catTypeId=@catMap[catType]

      processCol('http://www.100sp.ru/collection.php?cid='+cid)
    end

  end

  def get_pid_by_cid(cid)
    page = @agent.get('http://www.100sp.ru/collection.php?cid='+cid)
    return page.search('.sp-breadcrumb a')[1].attr('href').to_s.gsub('/purchase.php?pid=','')
  end

  def run
    if @agent==nil then before end
    @catMap=Hash.new

    page = @agent.get('http://www.100sp.ru/login.php')

    login_form=page.form('login')
    login_form.email='<EMAIL>'
    login_form.password='Tushkan65'
    page = @agent.submit(login_form, login_form.buttons.first)

    if not @filter_file.nil?
      read_filter_file
    end

    createCatMap(0,[])

    @purchase_cols=Hash.new


    pids=[]
    col_ids=[]
    @spid.split(',').each do |id2|
      if id2.to_i<2000000
        pids<<id2
      else
        col_ids<<id2
      end

    end

    pids.each do |pid|
      @purchase_cols[pid]=nil
    end

    col_ids.each do |cid|
      pid=get_pid_by_cid(cid)
      if not @purchase_cols.has_key? pid
        @purchase_cols[pid]=[cid]
      else
        @purchase_cols[pid]<<cid
      end

    end

    @purchase_cols.each do |pid,cids|
      process_purch(pid,cids)
    end
  end
end