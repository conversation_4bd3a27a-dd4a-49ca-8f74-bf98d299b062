module Ecolan
  class EcolanParser < Downloader
    include InvoiceEcolan

    def initialize
      @url = "https://ecolan37.ru/"
      @dlparams = {}
      @collection = nil

      super
    end

    def init_cat_data
      @pc=Framework::Site::ProductConfig.new

      @pc.with_category_type('998')
      @pc.with_category_type('107108', 'кухни')
      @pc.with_category_type('857', 'головные')
      @pc.with_category_type('107111', 'комплекты', 'постельного', '1,5')
      @pc.with_category_type('107112', 'комплекты', 'постельного', '2')
      @pc.with_category_type('3030', 'комплекты', 'постельного', 'детские')
      @pc.with_category_type('107112', 'комплекты', 'постельного', 'евромакси')
      @pc.with_category_type('107112', 'комплекты', 'постельного', 'евростандарт')
      @pc.with_category_type('107113', 'комплекты', 'постельного', 'семейные')
      @pc.with_category_type('107103', 'комплекты', 'столового')
      @pc.with_category_type('107130', 'коврики')
      @pc.with_category_type('107128', 'полотенца')
      @pc.with_category_type('3025', 'простыни')
      @pc.with_category_type('107137', 'уголки')
      @pc.with_category_type('107138', 'комплекты', 'сауны')
      @pc.with_category_type('3026', 'наволочки')
      @pc.with_category_type('108148', 'платки')
      @pc.with_category_type('3011', 'халаты')
      @pc.with_category_type('3013', 'сорочки')
      @pc.with_category_type('1485', 'трикотаж')
      @pc.with_category_type('107114', 'пододеяльники')
      @pc.with_category_type('107126', 'наматрасники')
      @pc.with_category_type('107123', 'наперники')
      @pc.with_category_type('107120', 'одеяла')
      @pc.with_category_type('107115', 'подушки')
      @pc.with_category_type('107118', 'покрывала')
      @pc.with_category_type_by_name('118', 'мужская', 'пижама')
      @pc.with_category_type_by_name('120', 'мужская', 'сорочка')
      @pc.with_category_type_by_name('872', 'мужские', 'трусы')
      @pc.with_category_type_by_name('107138', 'мужской', 'сауны')
      @pc.with_category_type_by_name('2175', 'детские', 'комплект', 'крестильный')
      @pc.with_category_type_by_name('844', 'детские', 'комплект')
      @pc.with_category_type_by_name('551', 'детские', 'пижама', 'девочки')
      @pc.with_category_type_by_name('564', 'детские', 'пижама', 'мальчика')
      @pc.with_category_type_by_name('552', 'детские', 'платье', 'девочки')
      @pc.with_category_type_by_name('551', 'детские', 'сорочка', 'девочки')
      @pc.with_category_type_by_name('551', 'детские', 'халат')
    end

    def process_textiles_list(href, category)
      tries ||= 2

      @log.info href
      page = @agent.get(href)

      if page.search('meta[itemprop="productID"]').first.nil?
        sleep(27)
        page = @agent.get(href)
        return if page.search('meta[itemprop="productID"]').first.nil?
      end

      # описание товара
      description = page.search('div.shortDescription').text + "\n"

      characteristics = ""

      #
      page.search('div.elementProperties > div.propertyList div.propertyName').each {|property|
        characteristic_as_link = property.next_element.search('a')
        if !characteristic_as_link.to_s.empty?
          characteristics += property.text + ": " + characteristic_as_link.text.gsub(/\s+/,' ').strip + "\n"
        else
          characteristics += property.text + ": " + property.next_element.text.gsub(/\s+/,' ').strip + "\n"
        end
      }

      # описание с характеристиками вместе
      description += characteristics

      # артикул
      vendor_code = page.search('div.reviewsBtnWrap > div.row.article > span').text.strip

      # наименование товара
      name = page.search('h1.changeName').text

      # парсим необходимые для запроса цен данные
      product_id, iblock_id, prop_id, price_code = prepare_request_params(page)
      level = page.search('div.elementSkuProperty.elementSkuDropDownProperty').attr('data-level').to_s

      # изображения товара
      pictures = []
      pictures_carousel = page.search('div#moreImagesCarousel > div.carouselWrapper > div.slideBox > div.item > a')

      # проверяем, если картинка единственная
      if !pictures_carousel.to_s.empty?
        pic_urls = pictures_carousel.each_with_index {|a_as_picture, index|
          picture_url = a_as_picture.attr('href').to_s

          fn="#{vendor_code} #{product_id}~#{index}"
          pictures<<savePic(picture_url, fn, true)
        }
      else
        single_picture_url = page.search('div#pictureContainer > div.pictureSlider > div.item > a').attr('href').to_s
        fn="#{vendor_code} #{product_id}~0"
        pictures<<savePic(single_picture_url, fn, true)
      end

      Cache.set_value('ecolan',"#{vendor_code } #{product_id}~url",href)

      request_headers = {
          'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
          'X-Requested-With' => 'XMLHttpRequest'
      }

      # выпадающий список для товаров с размерами
      prices_dropdown = page.search('ul.elementSkuPropertyList.skuDropdownList > li')

      if !prices_dropdown.to_s.empty?
        prod=@purchase.find_product_by_art(vendor_code)
        if prod
          prod.sizes=''
          prod.save
        end

        page.search('ul.elementSkuPropertyList.skuDropdownList > li').each {|size|
          data_name = size.attr('data-name').to_s.gsub(/\s+/,' ').strip
          data_value = size.attr('data-value').to_s.gsub(/\s+/,' ').strip

          # сверим для начала имеющиеся товары
          entire_description_div = page.search('div.changeDescription')
          start_position = ""
          size_desc=''
          entire_description_div.search('b').each {|b|
            if b.text.downcase.gsub(/[^А-я0-9]/,'') == ("Размер:" + data_value).downcase.gsub(/[^А-я0-9]/,'')
              start_position = b.text
            elsif start_position!=''
              end_position = b.text
              size_desc = entire_description_div.text.split(start_position).last.split(end_position).first
              break
            end
          }

          if size_desc==''
            if start_position!=''
              size_desc=entire_description_div.text.split(start_position).last
            else
              size_desc=entire_description_div.text.strip.gsub(/^размер.*?\./i,'').strip
            end
          end
          #break if size_desc == ''

          all_sizes = page.search('ul.elementSkuPropertyList.skuDropdownList > li').map {|li|
            "RAZMER_1:#{li.attr('data-value').to_s}"
          }.join(';') + ';'

          request_params = {
              act: "selectSku",
              props: all_sizes,
              params: data_name + ":" + data_value,
              level: level,
              iblock_id: iblock_id,
              prop_id: prop_id,
              product_id: product_id,
              highload: "",
              "price-code": price_code,
              stores_params: ""
          }

          # запрос цен
          prices_request = @agent.post(
              @url + 'bitrix/templates/dresscode/components/dresscode/catalog.item/detail/ajax.php',
              request_params,
              request_headers
          )

          if size_desc=='' and name.downcase.include? 'плед'
            case data_value.gsub(/[,.]/,'')
            when '15сп'
              size_desc='Размер: 150х210см'
            when '2сп'
              size_desc='Размер: 180х210см'
            when 'евро'
              size_desc='Размер: 200х210см'
            end
          end

          prices_json = prices_request.body
          parsed_prices = JSON.parse(prices_json)
          price = parsed_prices[0]['PRODUCT']['PRICE']['PRICE']['PRICE'].to_i

          if parsed_prices[0]['PRODUCT']['PRICE']['PRICE_ROZNICA']
            rrp = parsed_prices[0]['PRODUCT']['PRICE']['PRICE_ROZNICA'].gsub(/[^0-9,]/,'').to_i
          end

          product_size_id=parsed_prices[0]['PRODUCT']['ID']

          cat_name=category
          cat_type=@pc.get_category_type(cat_name,name)

          if category.include? 'белье'
            cat_name = category + " - " + data_value
            @collection = addCollection(cat_name,cat_type)
            p = addProduct(@collection, vendor_code+' '+product_id+' '+product_size_id+' '+data_value, name+' '+data_value, price, description+"\n"+size_desc,[], pictures, nil, nil,rrp:rrp,source:href )
            unless p.weight
              p.weight = ChatGpt.new.get_product_weight(p.name)
              p.save
            end
          else
            cat_name = category
            @collection = addCollection(cat_name,cat_type)

            prod=@purchase.find_product_by_art(vendor_code+' '+product_id+' '+product_size_id)
            if prod and @product_added.include?(vendor_code+' '+product_id+' '+product_size_id)
              prod.add_size(price,data_value.gsub(',','.'))
            else
              @product_added<<vendor_code+' '+product_id+' '+product_size_id
              p = addProduct(@collection, vendor_code+' '+product_id+' '+product_size_id, name, price, description+"\n"+size_desc,[data_value.gsub(',','.')], pictures, nil, nil,rrp:rrp,source:href)
              unless p.weight
                p.weight = ChatGpt.new.get_product_weight(p.name)
                p.save
              end
            end


          end


        }
      else
        description += "\n" + page.search('div.changeDescription').text
        price = page.search('div.smallElementToolsContainer > div.mainTool span.priceContainer > span.priceVal').text.gsub(/[^\d]/, '')
        rrp = page.search('div.smallElementToolsContainer > div.mainTool span.detail_price_roznica').text.gsub(/[^0-9,.]/, '').to_i
        cat_type=cat_type=@pc.get_category_type(category,name)
        pr_id2=page.search('meta[itemprop="productID"]').first.attr('value').to_s
        @collection = addCollection(category,cat_type)
        addProduct(@collection, vendor_code+' '+product_id+' '+pr_id2, name, price, description, [],pictures, nil, nil,rrp:rrp,source:href)
      end


    rescue Mechanize::ResponseCodeError => e
      puts e
      return if e.response_code == '404'
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

# подготовка параметров для запроса цен
    def prepare_request_params(page)
      product_id = page.search('div#catalogElement').attr('data-product-id').to_s
      iblock_id = page.search('div#catalogElement').attr('data-iblock-id').to_s
      prop_id = page.search('div#catalogElement').attr('data-prop-id').to_s
      price_code = page.search('div#catalogElement').attr('data-price-code').to_s

      return product_id, iblock_id, prop_id, price_code
    end

    def process_cat(href, category)
      @log.info "Process cat #{href}"

      page=@agent.get(href)

      page.search('div.items.productList > div.item.product.sku').each_with_index {|div, index|
        a = div.search('div.tabloid.nowp > div.productTable > div.productColImage > a').attr('href')
        process_textiles_list(a.to_s, category)
      }

    rescue Mechanize::ResponseCodeError => e
      @log.info e.message
    end

# процедура входа
    def sign_in
      url = "https://ecolan37.ru/auth/?backurl=/"
      # необходимы ваши данные для входа в систему с параметрами USER_LOGIN USER_PASSWORD
      request_params = {
          'data' => '{
        "AUTH_FORM": "Y"
        "TYPE": "AUTH"
        "backurl": "/auth/?backurl=%2F"
          "USER_LOGIN": "<EMAIL>"
        "USER_PASSWORD": "27021979"
        "Login": "Войти"
      }'
      }

      prices_request = @agent.post(
          url,
          request_params,
          { 'Content-Type' => 'application/x-www-form-urlencoded' }
      )
    end

    def sign_out
      url = "https://ecolan37.ru"
      page = @agent.get(url)

      sign_out_link = url + page.search('li.top-auth-exit > a').attr('href').to_s

      @agent.get(url)
    end

    def run
      init_cat_data

      sign_in

      @product_added=[]

      page = @agent.get @url + "catalog"
      page.search("div.sectionItems > div.item").each_with_index {|div, index|
        column = div.search("div.itemContainer")
        section_list = column.search("div.column > div.sectionList > div.section > a")

        if !section_list.to_s.empty?
          section_list.each {|a|
            process_cat(a.attr('href').to_s+'?SHOWALL_1=1', a.text.to_s)
          }
        else
          a = column.search("div.column > a")
          process_cat(a.attr('href').to_s+'?SHOWALL_1=1', a.text.to_s)
        end
      }
    end
  end
end
