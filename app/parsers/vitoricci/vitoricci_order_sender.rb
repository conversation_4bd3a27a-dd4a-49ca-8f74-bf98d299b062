# frozen_string_literal: true

module Vitoricci
  class VitoricciOrderSender < Framework::OrderSender
    #https://sp.vitoricci.ru/add_to_cart.php?nr=1&products_id=1819570&qnt=1
    #nr:
    # 1
    # products_id:
    # 1819570
    # qnt:
    # 1

    def authorize
      page = @agent.get 'https://sp.vitoricci.ru/'
      data = {'email_address' => '<EMAIL>', 'password' => 'qki4k', 'submit' => 'Войти'}
      @agent.post 'https://sp.vitoricci.ru/login.php?action=process', data
    end

    def clean
      #
      page = @agent.get 'https://sp.vitoricci.ru/shopping_cart.php'
      page.search('tr.CartProduct input[type=hidden]').each do |input|
        @agent.get("https://sp.vitoricci.ru/delete_from_cart.php?del=1&pid=#{input.attr('value').to_s}")
        sleep(0.1)
      end
    end

    def order(art, sizes)
      debug_func "order #{art} #{sizes}"

      page = @agent.get art

      product_ids = {}
      page.search('#select_size option').each do |option|
        if /(\d+)$/ =~ option.attr('value').to_s
          id = $1
          size = option.text.strip
          size = option.text.strip.split(' ').last unless sizes[size]
          if sizes[size]
            res = @agent.get "https://sp.vitoricci.ru/add_to_cart.php?nr=1&products_id=#{id}&qnt=#{sizes[size]}"
            sleep(0.3)
            res = @agent.get "https://sp.vitoricci.ru/"
            cart = res.at('.cartMenu span.cartRespons').text.strip
            sum = res.at('h3.subtotal').text.strip
            puts art,size,cart,sum,''
          else
            puts "#{size} not found"
          end

        end
      end
    end

    def add_csv_data(fields)
      art=fields['Источник товара'].to_s
      size = fields['Размер'].to_s
      @csv_data[art]={} unless @csv_data.include? art
      if @csv_data[art].include? size
        @csv_data[art][size] +=1
      else
        @csv_data[art][size] = 1
      end
    end

  end
end