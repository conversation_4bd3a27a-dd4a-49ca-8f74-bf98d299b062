class LlcPp < PpBase
  include SpAuthorize

  def initialize
    @purchase_id = 157
    super
  end



  def send_order
    data = load_all_orders

    return if data.empty?

    sender = LookLikeCat::LookLikeCatOrderSender.new
    sender.purchase_id = @purchase_id
    sender.before
    sender.authorize
    sender.clean

    arts = {}
    data.each do |o|
      arts[o['articul']] = {} unless arts[o['articul']]
      arts[o['articul']] = Hash.new(0) unless arts[o['articul']][o['size']]
      arts[o['articul']][o['size']] += 1
    end

    arts.each do |art, sizes|
      sender.order(art, sizes)
    end

    number_in_cart = sender.get_number_in_cart

    puts "number_in_cart=#{number_in_cart}"

    if number_in_cart != data.count
      notify("Ошибка отправки заказа в LookLikeCat", "Отправлено #{number_in_cart} из #{data.count} товаров")
    end

    sender.submit_order

    order_sent(data)
  end
end