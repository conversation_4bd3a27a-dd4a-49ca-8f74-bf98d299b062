#encoding:utf-8
require_relative '../framework/site/product_config'
module AlisaFashion
  class AlisaFashionProductConfig < Framework::Site::ProductConfig
    def get_page(link)
      begin
        # sleep(5+rand(0.3))
        @agent.get(link)
      rescue Net::ReadTimeout, Net::OpenTimeout => exception
        @error_log_func.call "Has exception on #{url}, restart agent"
        sleep(1.minutes)
        return @agent.get(link)
      rescue Mechanize::ResponseCodeError => exception
        puts 'error'
        if exception.response_code == '502'
          puts 'error 502'
          @agent.shutdown
          @agent = @create_agent_func.call
          @agent.request_headers
          sleep(1.minute)
          unless link.include? 'http://' or link.include? 'https://'
            link = "http://alisafashion.ru/#{link}" unless link.start_with? '/'
            link = "http://alisafashion.ru#{link}" if link.start_with? '/'
          end

          return @agent.get(link)
        else
          raise # Some other error, re-raise
        end
      end
    end

    def get_articul(product_page)
      regexes = [
          /(?<articul>[\w]+?[\d\-]+)/,
          /модель (?<articul>[а-яА-Я]+\s(бм|мех)?)/,
          /(?<articul>[\wа-яА-Я]+-\d+)/
      ]
      regexes.each do |regex|
        @articul_regex = regex
        result = super(product_page)
        return result unless result.blank?
      end

      articul_text = prepare_string @articul_func.call(product_page).gsub('женская', '').gsub('демисезонная', '').gsub('женский', '')
      arr = articul_text.split
      arr.drop(1).take(arr.length - 2).join ' '
    end

    def longest_common_substr(strings)
      shortest = strings.min_by &:length
      maxlen = shortest.length
      maxlen.downto(0) do |len|
        0.upto(maxlen - len) do |start|
          substr = shortest[start, len]
          return substr if strings.all? { |str| str.include? substr }
        end
      end
    end

    def get_color(product_page)
      name = @name_func.call(product_page)
      result = prepare_string name.split(get_articul(product_page))[-1]
      if result == prepare_string(name.gsub(get_articul(product_page), ''))
        result = ''
      end
      result
    end

    def parse_product(product_page, link = nil)
      return nil if product_page.search('.errorPage .numberError').length > 0
      return nil if @articul_func.call(product_page).downcase.to_s == 'каталог'
      return nil if product_page.search('span[itemprop=price]').length > 1
      page = product_page
      if page.search('[href="#dropDownList"]').first.nil?
        @authorize_func.call
        page = get_page(link)
      end
      puts page.search('[href="#dropDownList"]').first.text
      @color = get_color(page)
      result = super(page, link)
      result.name = prepare_string result.name.gsub(result.articul, '')
      result
    end

    def save_images(product_elem, articul)
      super(product_elem, "#{articul}~#{@color}")
    end

    def add_product_new(product, json=nil, cat_type=nil)
      existing_product = ProductNew.find_by(purchase_id: @purchase.id, sku: product.articul)
      if existing_product.nil?
        product.name = prepare_string product.name.gsub(@color, '')
        super(product, json, cat_type)
        return
      end

      colors=JSON.parse(existing_product.colors)
      get_json(product, @color).each { |key, value| colors[key] = value }

      existing_product.colors = colors.to_json
      existing_product.save!
    end

    def with_create_new_agent_func(create_agent_func)
      @create_agent_func = create_agent_func
      self
    end
    def with_authorize_func(authorize_func)
      @authorize_func = authorize_func
      self
    end
  end
end