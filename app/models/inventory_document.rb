class InventoryDocument < ApplicationRecord
  belongs_to :user
  enum doc_type: [:import, :sale, :direct_entry, :writeoff, :receipt, :adjust, :revalue]
  has_many :inventory_document_lines

  def fulfill_transit(line)
    if line.transit_info
      amount = line.amount_change.to_i
      new_transit_info = []
      line.transit_info.each do |t|
        if t['amount'].to_i < 0
          amount += t['amount'].to_i

          raise "Error processing transit info: #{line.transit_info}" if amount < 0
          SimaTransit.where(id: t['id']).update_all(arrived_at: Time.now)
          new_transit_info << t
          break if amount == 0
        end
      end
      line.transit_info = new_transit_info
      line.amount_change = amount
      line.save
    end

    line
  end

  def post(purchase_id=nil, only_raise_prices:false)

    ActiveRecord::Base.transaction do
      self.lock!
      raise 'Документ уже проведен' if posted

      raise "Invalid document type: nil" if doc_type.nil?

      self.posted_at = Time.now

      @log = Logger.new('log/inventory.txt')

      self.inventory_document_lines.each do |line|
        @log.info line.as_json

        prod = line.inventory_product

        next unless prod
        #raise "В документе есть строки с несопоставленными товарами" unless prod

        @log.info prod.as_json

        prod.stock = [] if prod.stock.nil?

        stock_el_idx=prod.stock.find_index {|s| s['size']==line.size}
        stock_el=prod.stock[stock_el_idx] if stock_el_idx

        stock_el={'size'=>line.size, 'q'=>0, 'buy_price'=>0,'sp_price'=>0,'retail_price'=>0} if stock_el.nil?
        stock_el['q']=0 if stock_el['q'].nil?

        if line.line_type == 'relative'
          #sp_price = line.sp_price
          #sp_price = prod.stock[line.size]['sp_price'] if sp_price.nil?
          #new_sp_price = nil
          #new_sp_price = (prod.buy_price * 1.5).round(2) if prod.buy_price
          #new_sp_price = prod.price if new_sp_price.nil?

          #if sp_price.nil? or new_sp_price.to_f > sp_price.to_f
          #  sp_price = new_sp_price
          #end

          if doc_type == 'sale'
            stock_el['q'] += line.amount_change.to_i
          else
            new_sp_price=stock_el['sp_price'].to_f
            new_retail_price=stock_el['retail_price'].to_f
            new_rrp=stock_el['rrp'].to_f

            if doc_type!='sale' and doc_type!='writeoff'
              if only_raise_prices
                new_sp_price=[stock_el['sp_price'].to_f,line.sp_price.to_f].max
                new_retail_price=[line.retail_price.to_f,stock_el['retail_price'].to_f].max
                new_rrp=[line.rrp.to_f,stock_el['rrp'].to_f].max
                new_rrp = nil if line.rrp.to_f<0
              else
                new_sp_price=line.sp_price.to_f
                new_retail_price=line.retail_price.to_f
                new_rrp=line.rrp.to_f
                new_rrp = nil if line.rrp.to_f<0
              end

            end

            if doc_type=='receipt'

              #TODO: Update sima transit!!!
              line = fulfill_transit(line)

              new_retail_price=stock_el['retail_price'].to_f if new_retail_price<stock_el['retail_price'].to_f
              new_sp_price=stock_el['sp_price'].to_f if new_sp_price<stock_el['sp_price'].to_f
              new_rrp=stock_el['rrp'].to_f if new_rrp<stock_el['rrp'].to_f
            end

            stock_el['q'] = 0 if stock_el['q'] < 0
            stock_el['q'] += line.amount_change.to_i
            stock_el['retail_price']=new_retail_price
            stock_el['sp_price']=new_sp_price
            stock_el['buy_price']=line.buy_price if line.buy_price
            stock_el['rrp']=new_rrp
          end
        else
          line.prev_price_data = prod.stock
          if doc_type=='adjust'
            stock_el['q'] = line.amount_change
          else
            stock_el['q'] = line.amount_change
            stock_el['sp_price']= [line.sp_price.to_f,stock_el['sp_price'].to_f].max
            stock_el['retail_price'] = [line.retail_price,stock_el['retail_price']].max
            stock_el['buy_price'] = line.buy_price ? line.buy_price : stock_el['buy_price']
            stock_el['rrp'] = line.rrp ? line.rrp : stock_el['rrp']
            stock_el['rrp'] = nil if line.rrp<0
          end
          line.save
        end


        @log.info prod.stock

        if (doc_type == 'receipt' or doc_type == 'import' or doc_type == 'adjust') and stock_el['q'] and stock_el['q'].to_i > 0
          raise 'Не указана закупка для добавления новых товаров' if purchase_id.nil?

          purch = Purchase.find purchase_id

          if prod.inventory_folder.nil?
            new_col_name='НОВЫЙ ТОВАР'

            new_col = InventoryFolder.where(name: new_col_name, user_id: purch.id).first

            new_col = InventoryFolder.create(name: new_col_name, user_id: purch.id, sp_pid:purch.sp_pid) if new_col.nil?
            new_col.sp_pid=purch.sp_pid
            new_col.sp_pid = 850710 if new_col.sp_pid.to_i == 0
            @log.info "#{new_col.as_json}"
            prod.inventory_folder= new_col
            prod.save
            new_col.save
          end
        end

        if stock_el_idx
          prod.stock[stock_el_idx]=stock_el
        else
          prod.stock<<stock_el
        end

        prod.save
      end
      self.posted = true
      save
    end

  end

  def unpost
    raise 'Документ не проведен' unless posted

    ActiveRecord::Base.transaction do

      self.posted_at = nil

      @log = Logger.new('log/inventory.txt')

      self.inventory_document_lines.each do |line|
        @log.info line.as_json

        prod = line.inventory_product

        next if prod.nil?

        @log.info prod.as_json

        prod.stock = [] if prod.stock.nil?

        stock_el_idx=prod.stock.find_index {|s| s['size']==line.size}
        stock_el=prod.stock[stock_el_idx] if stock_el_idx

        stock_el = { 'q' => 0 } if stock_el.nil?
        stock_el['q'] = 0 if stock_el['q'].nil?

        if line.line_type == 'relative'
          stock_el['q'] -= line.amount_change.to_i
        else
          prod.stock = line.prev_price_data
        end

        if stock_el_idx
          prod.stock[stock_el_idx]=stock_el
        else
          prod.stock<<stock_el
        end

        @log.info prod.stock
        prod.save

        if line.transit_info
          line.transit_info.each do |t|
            if t['amount'].to_i < 0

              #raise "Error processing transit info: #{line.transit_info}" if amount < 0
              SimaTransit.where(id: t['id']).update_all(arrived_at: nil)
              line.amount_change -= t['amount'].to_i
            end
          end
          line.save
        end


      end

      self.posted = false
      save
    end
  end

  def get_non_existing_products(sheet)
    ret=[]
  end

  def fill_file(file)
    workbook = RubyXL::Parser.parse_buffer(file.read)
    sheet = workbook.worksheets[0]

    non_existing_products=get_non_existing_products(sheet)

    headers = {}

    not_found=[]

    sheet.sheet_data.rows.each_with_index do |row|
      next if row.nil? or row[0].value.blank?
      if headers.empty?
        row.cells.each_with_index { |c, i| headers[c.value.strip.downcase] = i }
        next
      end

      sku=row[headers['артикул']].value

      prod = InventoryProduct.find_by_sku(sku)
      unless prod
        prod_data={}
        headers.each {|h,i| prod_data[h]=row.cells[i].value.strip}
        not_found<<prod_data
      end

      line_type = InventoryDocumentLine.line_types[:relative]

      if headers['размеры сп']
        row[headers['размеры сп']].value.split(',').each do |d|
          (size, q) = d.split('@')
          #          if headers['тип значения']
          #            if row[headers['тип значения']].value == 'абсолютное'
          #   sizes=prod.stock.select {|s| s['size']==size} if prod.stock
          #              q=-sizes[0]['q'] if sizes
          #            end
          #end
          next if q.to_i == 0

          q=-q if self.doc_type=='sale' or self.doc_type=='writeoff'

          il=InventoryDocumentLine.create(inventory_product: prod, size: s, amount_change: q.to_i, inventory_document: self, line_type: line_type)
          not_found.last['inventory_line_id']=il.id unless not_found.last['inventory_line_id']
        end

      elsif headers['количество'] and headers['размер']
        s = row[headers['размер']].value.strip
        q = row[headers['количество']].value.to_i
        next if q.to_i == 0
        il=InventoryDocumentLine.create(inventory_product: prod, size: s, amount_change: -(q.to_i), inventory_document: self, line_type: line_type)
      elsif headers['количество'] and row[headers['количество']].value.strip == 'ВСЕ'
        prod.stock.each do |s, d|
          q = -d['q'].to_i
          next if q.to_i == 0
          il=InventoryDocumentLine.create(inventory_product: prod, size: s, amount_change: q, inventory_document: self, line_type: line_type)
        end
        not_found.last['inventory_line_id']=il.id if il and not_found.last['inventory_line_id'].nil?

      end

    end
  end

  def populate_adjust(purchase_id,revalue=false)
    p = Purchase.find purchase_id
    line_type = InventoryDocumentLine.line_types[:relative]

    added_ids = []

    InventoryProduct.all.each do |prod|
      next if added_ids.include? prod.id

      prod.stock.each do |s|
        InventoryDocumentLine.create(inventory_product: prod, size: s['size'], old_amount: s['q'], amount_change: 0, inventory_document: self, line_type: line_type, sp_price: s['sp_price'], buy_price:s['buy_price'],retail_price:s['retail_price'])
      end

      added_ids << prod.id

    end
  end
end
