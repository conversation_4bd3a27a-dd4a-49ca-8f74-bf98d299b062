# frozen_string_literal: true

class ForfreshInvoiceAdapter < SkuInvoiceAdapter
  def initialize
    super
    @name_col_name = 'Товары (работы, услуги)'
  end

  def get_invoice_name(sheet)
    sheet.each do |row|
      next unless row
      if row[0].to_s.include? 'Счет на оплату'
        return row[0].to_s
      end
    end
    ''
  end

  def invoice_comparator(line,fields)
    site_art=fields['Артикул'].gsub(/ .*/,'').strip
    line['Артикул'].to_s.upcase.strip==site_art.upcase
  end

end
