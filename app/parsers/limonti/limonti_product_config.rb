#encoding:utf-8

require_relative '../framework/site/product_config'
#require 'phashion'
require 'htmlentities'

module Limonti
  class LimontiProductConfig < Framework::Site::ProductConfig
    def get_page(link)
      begin
        sleep(rand(0.9)+0.8)
        @agent.get(link)
      rescue
        @error_log_func.call "Has exception on #{link}, restart agent"
        @agent.shutdown
        @agent = @create_agent_func.call
        @agent.request_headers
        sleep(5)
        return @agent.get(link)
      end
    end

    def with_create_new_agent_func(create_agent_func)
      @create_agent_func = create_agent_func
      self
    end

    def get_sizes_table_map
      sizes_page = get_page('https://limontishop.com/bitrix/templates/limonti/include/table-size-text.php')

      titles = sizes_page.search('.m_ttl[style]').map { |title| prepare_string HTMLEntities.new.decode(title.text) }

      tables = sizes_page.search('.sizes_tab').map { |table|
        table.search('tr').map { |tr|
          if tr.search('th').length > 0
            tr.search('th').map { |th| prepare_string th.text }
          else
            tr.search('td').map { |td| prepare_string td.text }
          end
        }
      }

      @sizes_table_map = tables
    end


    def initialize
      super

      @skip_categories = [
          'бижутерия',
          'аксессуары',
          'обувь',
          'купальники',
          'шапки',
          'палантин',
          'белье',
          'колготки',
          'трусы',
          'шарфы'
      ]

      @skip_brands=['ELISEEVA','АРТ-МАРИ', 'AVIVA','AQUAREL','BRILLARE','COMVILL','EFFA','F5','FELIBELLA','FILIGRANA','FOMAS','FORMALAB','GLAMOUR','GOLDEN LADY','GROSSBERG','HOUSE','ISSI','PLASTININA','LILLY','LOWRY','PLANDA','LAVINA','LISSA','LUCKY','MARHATTER','MIXIN','MAGNOLICA','MARHATTER','MARI-LINE','EXPROMT','MAXVAL','MERLIS','MINIMI','AMBAROFF','MILTON','MISHELE','NORMANN','ORANGE','OZKAN','BELLEZA','PELICAN','PERSONAGE','SAMEGAME','SISI','STEINBERG','SALENA','SERGEO','SHELLY','SILVIAN HEACH','SNOWIMAGE','STIGLER','T-SOD','TOPDESIGN','TOM TAILOR','VENUSITA','VIS-A-VIS','VENERA','VIAGGIO','VITO','YAX','YSG','КУРСКИЙ','ЛИНА','ТВО','ФЕМИНА','BONITTO','BROSTEM','BEAUTY','CANOE','EVOLUTION','ERREA','FOMAS','FORMALAB','CARDIN','TORRO','TAILOR','ALBINAT','LAMA','PRIMA VERA','VIVID','КАРАМЕЛЛИ','КАРАПУЗ','ПОЛЯРИК','MILAVITSA','PELICAN','PELICAN','LORMAR','AVELINE','LUXURY','VAU']

      with_sizes_table(->(product_page) {
        category = @category_func.call(product_page)

        category_lower = category.downcase


        return [] if @skip_categories.any?{|skip_category| category_lower.include? skip_category}

        return [] if not category_lower.include? 'мужское' and not category_lower.include? 'женское'

        is_man = category_lower.include? 'мужское'

        if is_man
          return @sizes_table_map[6] if category_lower.include? 'джинсы'
          return @sizes_table_map[5] if category_lower.include? 'брюки' or category_lower.include? 'шорты'
          return @sizes_table_map[4] if category_lower.include? 'домашняя'
          return @sizes_table_map[3]
        end

        return @sizes_table_map[2] if category_lower.include? 'джинсы'
        return @sizes_table_map[1] if category_lower.include? 'брюки' or category_lower.include? 'шорты' or category_lower.include? 'юбки'

        @sizes_table_map[0]
        # и для разных товаров разные таблицы
      })
    end

    def run(agent, category_page)
      @agent = agent

      get_sizes_table_map

      initialize_image_config
      product_elements = get_product_elements(category_page)
      product_elements.each { |obj|
        puts obj
        link = obj['product_link']
        @log_func.call link if @with_logging
        product_page = get_page(link)
        @get_main_picture_func = ->() {
          obj['main_picture']
        }
        create_new_product product_page, link
      }
    end

    def get_params(product_page)
      params=Hash.new
      product_page.search('ul.detail_pars li').each do |li|
        title=li.search('p span').text
        val=li.search('p').text.gsub(title, '').strip
        title.gsub!(' ', '')
        title.gsub!(':', '')
        params[title]=val
      end
      params
    end

    def get_quantity_json(product_page)
      /window.offersquan = ({.*?});/=~product_page.body
      JSON.parse($1.gsub("'", '"'))
    end

    def get_data_json(product_page)
      /window.offers = ({.*?});/=~product_page.body
      JSON.parse($1.gsub("'", '"'))
    end

    def parse_product(product_page, link=nil)

      unless /window.offersquan = ({.*?});/=~product_page.body
        puts 'Option not found'
        @error_log_func.call 'Option not found'
        return nil
      end

      unless /window.offers = ({.*?});/=~product_page.body
        puts 'Offers not found'
        @error_log_func.call 'Offers not found'
        return nil
      end

      result = super(product_page, link)

      return nil if @skip_brands.any?{|b|result.name.downcase.include? b.downcase}

      return nil if result.price == 0

      is_sale = product_page.search('.opt_price span.noval').length > 0

      result.category = "#{result.category} - Скидки" if is_sale

      result
    end

    def save_images(product_page, articul)
      images = super(product_page, articul)

      sort_images(images, @get_main_picture_func.call)
    end

    def sort_images(images, main_picture)
      begin
        tmpfile=Dir::Tmpname.make_tmpname '/tmp/limonti', nil
        @agent.download(main_picture, tmpfile)
        main=Phashion::Image.new(tmpfile)
        result=images.sort_by { |image|
          pic2=Phashion::Image.new("#{@picture_path}#{image.path}.jpg")
          pic2.distance_from(main)
        }
        File.delete(tmpfile)
        result
      rescue => e
        @error_log_func.call "Error in sorting images"
        @error_log_func.call e.to_s
        @error_log_func.call e.backtrace
        return images
      end
    end

    def create_new_product(product_page, link=nil)
      @purchase.check_stop
      product = parse_product(product_page, link)

      return if product.nil?

      return unless @parser.stock_data.has_key? product.articul


      # бежим по цветам
      # группируем по цене
      # клонируем продукты, добавляем json

      colors = get_colors(product_page)
      json = {}
      colors.each do |color, color_data|
        next unless @purchase.stock_data[product.articul].has_key? color

        @get_images = ->(p) {
          color_data['pics']
        }
        images = save_images(product_page, "#{product.articul}~#{color_data['id']}")

        next if images.length == 0

        # для нового сайта
        json[color] = {}
        json[color]['pics'] = images.map { |i| i.to_json }
        json[color]['sizes'] = {}

        color_data['sizes'].group_by { |s, p| p }.each do |price, group_array|
          sizes = group_array.map { |key_value_pair| key_value_pair[0] }
          sizes=@purchase.stock_data[product.articul][color]

          clone_product = product.clone

          clone_product.sizes = sizes.sort_by { |s| s.to_i }
          clone_product.images = images
          clone_product.price = price

          clone_product.price = @purchase.stock_prices[product.articul][color]

          clone_product.name = "#{clone_product.name} - #{color}"

          add_product(clone_product)

          clone_product.get_json_sizes.each { |key, value| json[color]['sizes'][key] = value }
        end
      end

      add_product_new(product, json)
    end

    def get_colors(product_page)
      data_json = get_data_json(product_page)

      colors = {}
      discount=product_page.search('.discount_text b').text.gsub('%', '').to_i

      data_json.each do |key, value|
        next if value['quan'].to_i < 1

        size, color = key.split(':')
        price = value['optprice'].gsub(' ', '').gsub(',', '.').to_i
        price = price * 1.05 if discount < 30
        colors[color] = {} unless colors.include? color
        colors[color]['sizes'] = {} if colors[color]['sizes'].nil?
        colors[color]['id'] = value['id'] if colors[color]['id'].nil?
        colors[color]['sizes'][size] = price
        if colors[color]['pics'].nil?
          colors[color]['pics'] = product_page.search(".im_area.id#{value['id']} a.fullimgitem")
                                      .map { |a| a.attr('href').to_s }
        end

      end

      colors
    end
  end
end