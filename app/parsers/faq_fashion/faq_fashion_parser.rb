#encoding:utf-8
require_relative '../framework/parser'
require_relative 'faq_fashion_product_config'
require 'csv'

module FaqFashion
  class FaqFashionParser < Framework::Parser
    def initialize_settings
      with_url('http://faq-fashion.ru/')
          .with_category_links('ul#accordion li.haschild ul a')
          .with_category_links('ul#accordion li:not(.haschild) a')
          .with_pagination(->(category_page) {
            result = category_page.search('.pagination .links > a').find { |a| a.text == '>' }
            return nil if result.nil?
            return result.attr('href').to_s
          })
          .with_product_config(->(pc) {
            pc.with_product_selector(->(category_page) {
              category_page.search('.products-block .product-block').map { |div|
                href = div.search('a.product-zoom')[0]
                           .attr('onclick').to_s
                           .gsub("ajaxDialog('", '')
                           .gsub("','quickBuy'); return false;", '')
                articul = div.search('b')[0].text.gsub('Артикул: ', '').downcase

                {href: href, articul: articul}
              }
            })
                .with_picture_path(@picture_path)
                .with_agent(@agent)
                .with_images(->(product_page) {
                  images = product_page.search('.MagicToolboxSelectorsContainer a').map { |a| a.attr('href').to_s }
                  images << product_page.search('a.MagicZoomPlus')[0].attr('href').to_s if images.size==0

                  images
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://faq-fashion.ru/')
                })
                .with_category_type('850')
                .with_category_type('19', 'юбки')
                .with_category_type('20', 'платья')
                .with_category_type('13', 'водолазки')
                .with_category_type('24', 'домашняя')
                .with_category_type('16', 'жакеты')
          }, FaqFashion::FaqFashionProductConfig)
    end

    def get_csv_products
      page=@agent.get 'http://faq-fashion.ru/download/Bokova.csv'

      text=page.body.gsub('"', "'")
      conv=Encoding::Converter.new('Windows-1251', 'UTF-8')
      text=conv.convert(text)

      result = []

      CSV.parse(text, :headers => true, :col_sep => ';') do |fields|
        articul=fields['Артикул']
        puts articul
        name=fields['Наименование']
        size_table=[]
        stock=[]

        size_table=fields['Сетка'].split('*') unless fields['Сетка'].nil?
        stock=fields['Наличие'].split('*') unless fields['Наличие'].nil?

        sizes=Hash[size_table.zip stock].select { |k, v| v!='0' }.keys
        category=fields['ГруппаНоменклатуры']
        price=(fields['ЦенаОптФакт'].to_f*1.05).ceil
        rrp=(fields['ЦенаОптФакт'].to_f*1.35).ceil
        next if sizes.size==0

        description=''

        fields.to_h.keys[11..20].each_with_index do |k, i|
          next if i == 1 # пропускаем состав
          next if fields[i+11].nil?
          #puts k
          if k=='Примечание'
            description+=fields[i+11]
          else
            description+="#{k}: #{fields[i+11]}. "
          end
        end

        result << Framework::SizeableParsedProduct.new(category, articul, [], name, description, price, sizes,nil,{},rrp)
        result[-1].composition = fields[12]
      end
      result
    end

    def process
      products = get_csv_products

      @product_config.initialize_image_config

      grouped_products = {}

      products.each do |product|
        main_articul = product.articul.split('-').first
        color = product.name.split.last
        puts main_articul, color
        grouped_products[main_articul] = {} if grouped_products[main_articul].nil?
        grouped_products[main_articul][color] = product
      end

      puts grouped_products

      grouped_products.each do |articul, hash|
        json = {}
        additional = {}
        hash.each do |color, product|
          product_page = get_product_page(product.articul)
          next if product_page.nil?
          images = @product_config.save_images(product_page, product.articul)
          next if images.length == 0
          product.images = images
          @product_config.get_json(product, color).each{|k,v|json[k]=v}
          additional[color] = {articul: product.articul}
          @product_config.add_product(product)
        end

        clone_product = hash.first.last.clone
        clone_product.articul = articul
        clone_product.additional = additional
        @product_config.add_product_new(clone_product, json)
      end
    end

    def get_product_page(articul)
      search_page = get_page "http://faq-fashion.ru/search/?search=#{articul}"

      link = search_page.search('.image > a').first

      return nil if link.nil?

      get_page link.attr('href').to_s
    end
  end
end