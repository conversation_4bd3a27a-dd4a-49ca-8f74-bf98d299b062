#encoding:utf-8
require_relative '../framework/parser'
require_relative 'artshock_product_config'

module Artshock
  class ArtshockParser < Framework::<PERSON><PERSON><PERSON>
    def initialize_settings
      with_url('http://artshock.net')
        .with_category_links('.submenurow li > a')
        .with_pagination(->(category_page){
          elem = category_page.search('.pagination li > a').find{|a|a.text == '>'}
          return nil if elem.nil?
          elem.attr('href')
        })
        .with_product_config(->(pc){
          pc.with_product_selector('.catalog-product a')
            .with_category(->(product_page){
              category = product_page.search('.breadcrumb li:not(:last-of-type) a')
                  .drop(1).map{|a| prepare_string a.text}

              return category[0]

              if category.length == 2 and category[0] == category[1]
                category = category[0]
              elsif category[0].include? 'Брюки' and category[1].include? 'Брюки'
                category = category[1]
              elsif category[0].include? 'Кофты' and category[1].include? 'Кофты'
                category = category[1]
              elsif category[0].include? 'Тёплые' and category[1].include? 'Тёплые'
                category = category[1]
              elsif category[0].include? 'Футболки' and category[1].include? 'Футболки'
                category = category[1]
              else
                category = category.join ', '
              end

              # is_new = (not product_page.search('.product-label').first.nil?)
              #
              # category = "#{category} - Новинки" if is_new
              category
            })
            .with_name(->(product_page){
              prepare_string product_page.search('h1').first.text
            })
            .with_articul(->(product_page){
              prepare_string product_page.search('.card-product-art > span').first.text
            })
            .with_price(->(product_page){
              product_page.search('.card-product-price > span').first.text.gsub(' ', '').to_i
            })
            .with_sizes(->(product_page){
              product_page.search('.section-razmer > .radio .val-name').map{|span| prepare_string span.text.gsub(/\(.+?\)/, '')}
            })
            .with_description(->(product_page){
              prepare_string product_page.search('.card-desc > p:not(.desc)').map{|p|prepare_string p.text}.join ' '
            })
            .with_images(->(product_page){
              product_page.search('.carousel-inner > .item > a').map{|a|a.attr('href')}
            })
            .with_image_config(->(ic){
              ic.with_default_url('http://artshock.net')
            })
            .with_category_type('850')
            .with_category_type('62', 'блузки', 'большие')
            .with_category_type('102165', 'блузки')
            .with_category_type('102196', 'брюки', 'большие')
            .with_category_type('102176', 'брюки')
            .with_category_type('102175', 'шорты')
            .with_category_type('102167', 'юбки, юбки')
            .with_category_type('80', 'верхняя', 'большие')
            .with_category_type('102186', 'верхняя')
            .with_category_type('129', 'пальто')
            .with_category_type('102179', 'пиджаки')
            .with_category_type('108150', 'снуды')
            .with_category_type('1955', 'детская')
            .with_category_type('3013', 'пижамки')
            .with_category_type('3011', 'домашняя')
            .with_category_type('64', 'костюмы', 'большие')
            .with_category_type('26', 'комбинезоны, комбинезоны')
            .with_category_type('102177', 'комбинезоны')
            .with_category_type('66', 'кофты', 'большие')
            .with_category_type('102197', 'свитера, туники')
            .with_category_type('96', 'платья', 'большие')
            .with_category_type('2024', 'платья')
            .with_category_type('1450', 'платья-мини')
            .with_category_type('64', 'спорт', 'большие')
            .with_category_type('949', 'спортивные костюмы')
            .with_category_type('79', 'футболки', 'большие')
            .with_category_type('1373', 'футболки')
            .with_category_type('107838', 'аксессуары')
            .with_category_type('1967', 'детская')
            .with_category_type('1685', 'мужская')
        }, Artshock::ArtshockProductConfig)
    end
  end
end