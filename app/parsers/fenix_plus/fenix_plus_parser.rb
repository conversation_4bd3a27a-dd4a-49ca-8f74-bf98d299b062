# frozen_string_literal: true
module FenixPlus
  # frozen_string_literal: true
  class FenixPlusParser < Downloader
    def initialize
      @products = []
      @products_in_folder = Hash.new(0)
      @product_info = {}
    end

    def guess_cat(cat, name)
      cat = cat.downcase
      name = name.downcase

      ret = nil
      if cat.include?("ежед") || name.include?("ежед") then ret = 100396 end
      if cat.include?("блокн") || name.include?("блокн") then ret = 100396 end
      if cat.include?("альб") || name.include?("альб") then ret = 100396 end
      if cat.include?("офис") && ret.nil? then ret = 101022 end
      if cat.include?("книг") || cat.include?("книж") then ret = 101034 end
      if cat.include?("школ") then ret = 1387 end

      ret ||= 101022
      ret
    end

    def login
      @agent.get('https://phoenix-plus.ru')
      data = {
        'backurl' => '/catalog/',
        'AUTH_FORM' => 'Y',
        'TYPE' => 'AUTH',
        'USER_LOGIN' => '<EMAIL>',
        'USER_PASSWORD' => '270279!',
        'OTP_REMEMBER' => 'Y'
      }
      @agent.post('https://phoenix-plus.ru/auth/', data)
    end

    def get_catalog_tree(page)
      ret = {}
      page.search('a[href^="/catalog"]').each do |a|
        href = a['href']
        route = href.split('/').reject(&:empty?).reject { |el| el == 'catalog' }
        name = a.search('span[class$=name-title]').text
        item_count = a.search('span[class$=add]').text.gsub(/[()]/, '').to_i

        ret[route.join('~')] = { itemCount: item_count, href: href, name: name, route: route }
      end

      ret.each do |path, data|
        if data[:route].length > 1
          data[:route].each_with_index do |_, i|
            ret.delete(data[:route][0..i].join('~')) if i > 0
          end
        end
        ret.delete(path) if path.include?('aktsiya') || path.include?('torgovoe_oborudovanie')
      end
      ret
    end

    def process_product(href, rrp)
      puts href
      begin
        page = @agent.get(href)
        return if page.search('.product-info [data-pack]').length != 1

        sku = page.search('.product-info-mobile .product-article').text.gsub(/арт\./, '').strip
        barcode = ''
        name = page.search('.product-info-mobile .product-description').text.strip
        pictures = page.search('.product-photos img').map { |img| img['src'] }.uniq

        buy_price = page.search('span.product-price-current').text.gsub(/[^.0-9]/, '').to_f.ceil
        price = (buy_price * 1.05).ceil
        min_order = page.search('.product-info [data-pack]')[0]['data-pack']

        s = min_order != '1' ? "Уп. #{min_order} шт, цена за 1" : '-'

        prices = { price => { rrp: rrp, sizes: [s], buy_price: buy_price } }

        cats = page.search('div[itemprop=breadcrumb] a span').map(&:text).reject { |t| t == 'Главная' || t == 'Каталог' }

        description = page.search('.product-features-content #t2 .product-features-content-item-inner').text.strip

        props = page.search('.product-features-content #t1 .row').each_with_object({}) do |row, hash|
          k = row.search('.dt').text.downcase
          v = row.search('.dd').text
          hash[k] = v
        end

        brand = props.delete('бренд') || 'Феникс+'

        props3 = page.search('.product-features-content #t3 .row').each_with_object({}) do |row, hash|
          k = row.search('.dt').text.downcase
          v = row.search('.dd').text
          hash[k] = v
        end

        barcode = props3['штрихкод единицы товара'] if props3['штрихкод единицы товара']

        props_text = props.map { |k, v| "#{k}: #{v}" }.join(', ')

        description += ". #{props_text}".gsub(/\.+/, '.')

        product = {
          sku: sku,
          source: href.gsub('/catalog/', ''),
          prices: prices,
          name: name,
          folderTree: cats,
          pictures: pictures,
          description: description,
          cat_id: guess_cat(cats.join('-'), name),
          brand: brand,
          barcode: barcode
        }
        @products << product
        folder_key = cats.join('~')
        @products_in_folder[folder_key] += 1

      rescue => e
        puts(e.message)
      end
    end

    def process_cat(href)
      puts "Cat #{href}"
      page = @agent.get("#{href}?perpage=27")

      page.search('.catalog-item-inner').each do |div|
        product_href = div.search('a.catalog-item-link')[0]['href']
        t = div.search('span.catalog-item-name-pre').text.gsub(/арт\. +/i, '').gsub(/,(.|[\r\n])*/, '')
        rrp = div.search('span.catalog-item-price-current').text.gsub(/[^.0-9]/, '').to_f.ceil
        puts href
        puts t
        puts rrp
        @product_info[product_href] = rrp
      end

      next_href = page.search('a.pager-item-last')
      process_cat(next_href[0]['href']) if next_href.any?
    end

    def run
      before unless @agent

      page = @agent.get('https://phoenix-plus.ru/ajax/getMenuSections.php')
      cats = get_catalog_tree(page)
      i = 0
      cats.each do |_, cat|
        process_cat(cat[:href])
        i += 1
        break if i == 1 # Уберите этот комментарий, чтобы обработать все категории
      end

      login

      @product_info.each do |url, rrp|
        process_product(url, rrp)
        sleep 0.777
      end
    end

  end
end
