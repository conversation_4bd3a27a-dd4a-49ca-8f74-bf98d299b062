#encoding:utf-8
require_relative '../framework/site/product_config'

module Filisi
  class FilisiProductConfig < Framework::Site::ProductConfig

    def get_page(link)
      begin
        @log_func.call "Get page #{link}"
        sleep(rand()*0.8+1)
        return @agent.get(link)
      rescue Mechanize::ResponseCodeError => exception
        @error_log_func.call "Has exception on #{link}, response code - #{exception.response_code}"
        if exception.response_code == '404'
          return nil
        else
          @error_log_func.call "Has exception on #{link}, restart agent"
          @agent.shutdown
          sleep(5.minutes)
          @agent = @create_agent_func.call
          @agent.request_headers
          return @agent.get("#{link}")
        end
      rescue
        @error_log_func.call "Has exception on #{link}, restart agent"
        @agent.shutdown
        @agent = @create_agent_func.call
        @agent.request_headers
        sleep(5)
        return @agent.get("#{link}")
      end
    end

    def parse_product(product_page, link=nil)
      result = super(product_page, link)
      return nil if result.images.empty?
      return nil if result.price == 0
      result
    end

    def with_create_new_agent_func(create_agent_func)
      @create_agent_func = create_agent_func
      self
    end
  end
end