module Soltterra
  class SoltterraParser < Downloader
    def initialize
      @url = "https://soltterra.ru/"
      @dlparams = {}
      @collection = nil

      super
    end

    def process_lamp(href, category)
      @log.info "Process lamp #{href}"
      page = @agent.get(href)

      # описание товара
      characteristics = {}
      description = ""
      page.search('span[@itemprop="description"] > p').each_with_index do |p, index|
        next if index == 0
        key_value = p.text.split(":")
        if key_value[1].nil?
          key_value = p.text.split
          next if key_value == []
          characteristic_key = key_value[0].strip
          characteristic_value = key_value.drop(1).join(' ').strip
        else
          characteristic_key = key_value[0].strip
          characteristic_value = key_value[1].strip
        end
        characteristics[characteristic_key] = characteristic_value
      end

      # артикул
      # vendor_code = characteristics.delete('Артикул')
      product_id = page.search('input[name=product_id]').attr('value').to_s
      itemid = page.search('input[name=Itemid]').attr('value').to_s
      vendor_code = product_id + "_" + itemid

      # описание с характеристиками вместе
      details = characteristics.map { |key, value| "#{key}: #{value}" }.join ', '
      description += details

      # наименование товара
      name = page.search('h1[@itemprop="name"]').text.strip
      puts name

      # изображения товара
      pictures = []
      main_picture_url = page.search('div#mob > a').attr('href').to_s

      pictures<<savePic(main_picture_url, "#{vendor_code}~0", true)

      # цена
      price = page.search('div[@itemprop="offers"] > span').text.gsub(/[^\d.]/, '').gsub('.', '')

      @collection = addCollection(category,'108415')
      addProduct(@collection, vendor_code, name, price, description, [],pictures)

        # sleep(rand(0.3)+0.3)

    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_lamps_list(href, category)
      @log.info "Process lamps list #{href}"

      page = @agent.get(href)

      loop do
        next_link=nil
        li = page.search('div[@align="center"] > ul.pagination > li')
        next_link = li[-1].at('a:contains("Следующая")') if li[-1]

        page.search('div#product_list > div').each_with_index {|div, index|
          td = div.search('div#header16 > div.browseProductContainer > table')
          a = td.search('tr > td[@width="75%"] > h2 > a').attr('href')
          process_lamp(a.to_s, category)
        }

        break unless next_link

        next_page = next_link.attr('href').to_s
        page = @agent.get(next_page)
      end
    end

    def run
      page = @agent.get(@url)
      page.search("div#vmMainPage > table > tr").each_with_index {|tr, index|
        tr.search('td').each_with_index {|td, index|
          a = td.search('div#header13 > a')
          category = a.attr('title').to_s
          process_lamps_list(a.attr('href').to_s, category)
        }
      }
    end

  end
end
