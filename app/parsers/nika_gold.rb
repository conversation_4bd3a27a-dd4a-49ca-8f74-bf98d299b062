#encoding: utf-8

class NikaGold < Downloader
  attr_accessor :gold

  def initialize
    @dlparams={'gold'=>:checkbox}

    @unique_product_links =%w(/serebro/1-8241/ /serebro/z1-8837/ /serebro/z1-8217/)

    super
  end


  def parse_catalog(url)
    page = @agent.get url
    # Категории, по которым смотреть - #left_menu > li > a
    categories = page.search('.side_menu_categories .menu > li > .link > a').map{|a| a.attr('href').to_s}
    # categories = []
    categories << 'http://nika-gold.ru/serebro/category/category_687/'

    categories.each_with_index do |link, i|
      #next if i < 5

      puts "PARSE CATEGORY #{link}"
      category_page = @agent.get(link)

      # ССылки продуктов .product-list li > a
      prod_links = category_page.search('.product-list .product_name > a').map { |l| l.attr('href').to_s }
      puts "Prod_links count: #{prod_links.length}"
      prod_links.each { |l| process_product l }

      # Страницы - .pagination > li > a
      page_links = category_page.search('.pagination > li > a')

      next if page_links.length == 0

      # [-2] - Последняя страница
      # максимальный номер .pagination > li > a [-2] text
      max_num = page_links[-2].text.to_i
      # И по всем бежать

      puts "Max_num #{max_num}"
      (2..max_num).each { |i|
        puts "Page #{i}"
        @agent.get("#{link}?page=#{i}")
            .search('.product-list .product_name > a')
            .map { |l| l.attr('href').to_s }
            .each { |l| process_product l }
      }
    end
  end

  def process_product(url)
    puts url
    # Например, в детской коллекции есть битые ссылки
    # if url.match(/\/(?:product|serebro|collection)\/\d+\//i).nil? and not @unique_product_links.include? url
    #   puts "ERROR: Wrong url format. #{url}"
    #   return
    # end

    page_id = url.split('/')[-1]

    # if %r!/([0-9]+)/$!=~url
    #   page_id=$1
    # else
    #   return
    # end

    url = url.gsub('/serebro/serebro', '/serebro/product')

    begin
      product_page = @agent.get url
    rescue Mechanize::ResponseCodeError => e
      return if e.response_code == '404'
      raise e
    end

    # На странице с продуктом находится вся информация
    # Category .breadcrumbs a [-2] [-1] and #product-categories a
    category = product_page.search('.breadcrumbs a')[-2].text + '-' + product_page.search('.breadcrumbs a')[-1].text
    # Articul .hint
    articul = prepare_string product_page.search('.hint').text
    # Name h1. Name contains articul, so just remove it
    name = prepare_string product_page.search('h1').text.gsub(articul, '')
    articul = prepare_string articul.gsub('Артикул:','')
    name = prepare_string name.gsub(articul, '')
    # Price .price [data-price]
    price = product_page.search('.price').attr('data-price').to_s.to_f*0.75
    price = product_page.search('.price').attr('data-price').to_s.to_f*0.85 if category.include? 'Йонизаторы'
    price = product_page.search('.price').attr('data-price').to_s.to_f if category.include? 'Ложки'
    price=price.ceil

    description=prepare_string product_page.search('#product-description').text.strip
    unless description.empty?
      description+='.' unless description.end_with? '.'
      description+=' '
    end

    # Description #product-features tr each [0] + [1]
    props=product_page.search('#product-features tr').map{|tr| "#{prepare_string tr.search('.name').first.text}:#{prepare_string tr.search('.value').first.text}"}
    props.delete_if {|el| el.include? 'рок изготовления' or el.include? 'склад'}
    description+=props.join ', '


    # Images #zoom_01 data-zoom-image
    images = product_page.search('.image > a').map{|el|el.attr('href').to_s}.uniq

    pics=[]
    images.each_with_index { |url,i |
      fn="#{page_id}~#{i}"
      savePic(url,fn,true)
      pics<<fn
    }

    sizes = []
    if category.include? 'Кольца' or name.include? 'Кольцо' or name.include? 'Перстень' or category.include? 'Печатки'
      # Sizes only for rings select.service-variants option regex (\d+,?\d?)\s*\(.*\) [0] replace , .
      # Для серебра есть еще один селект, поэтому берем [-1]
      sizes = product_page.search('select.service-variants')[-1].search('option')
                  .map{|o| o.text.match(/(\d+,?\d?)\s*\(.*\)/).captures[0].gsub(',','.')}
    end

    cat_id='88'
    cat_id='70' if category.include? 'мусжк'
    cat_id='979' if category.include? 'детей'

    col=addCollection(category,cat_id)
    addProduct(col,articul,name,price,description,sizes,pics)
  end


  def run
    if @agent==nil then before end
    @agent.agent.http.verify_mode = OpenSSL::SSL::VERIFY_NONE

    if @gold
      parse_catalog 'http://nika-gold.ru/'
    else
      parse_catalog 'http://nika-gold.ru/serebro'
    end

  end
end