#encoding:utf-8
require_relative '../framework/site/excel_product_config'

module Rijjini
  class RijjiniProductConfig < Framework::Site::ProductConfig

    def add_product(product)
      product.images = save_images(product)
      super product
    end

    def with_agent(agent)
      @agent = agent
      initialize_image_config
    end

    def save_images(product)
      @agent.content_encoding_hooks << lambda { |httpagent, uri, response, body_io|
        response['Content-Encoding'] = ''
      }
      saved_images = []
      product.images.each_with_index { |url, i|
        result = @image_config.run(url, product.articul, i, product.additional)
        saved_images << result unless result.nil?
      }
      @agent.content_encoding_hooks.pop
      saved_images
    end
  end
end