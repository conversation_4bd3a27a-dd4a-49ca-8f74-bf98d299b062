# frozen_string_literal: true
#
class ChatGptWeightBatch < ChatGptBatchService
  def run(purchase_id, use_description: false)
    b = ChatGptBatch.where(purchase_id: purchase_id, batch_type: self.class.name).first
    return if b && (b.status == 'in_progress' || b.status == 'validating')

    gpt = ChatGpt.new
    lines = []

    purchase = Purchase.find(purchase_id)
    purchase.products.active.where(weight: nil).each do |product|
      prod_name = product.name
      prod_name = "#{product.name}, описание: #{product.desc[0..100]}" if use_description
      lines << gpt.get_product_weight(prod_name, batch_id: "prod_weight_#{product.id}")
      break if Rails.env.development? # limit for development
    end

    return if lines.count == 0

    fn = Rails.root.join('files', "chatgpt_weight_#{purchase_id}.jsonl").to_s
    File.open(fn, 'w') { |f| f.write(lines.join("\n")) }
    r = gpt.upload_file(fn, 'batch')
    puts r
    r = gpt.create_batch(r['id'])
    puts r
    ChatGptBatch.create(batch_id: r['id'], status: 'validating', purchase_id: purchase_id, filename: fn, expires_at: r['expires_at'], batch_type: self.class.name)

    gpt.client.batches.list
  end

  def process_result(batch)
    gpt = ChatGpt.new

    content = gpt.client.files.content(id: batch['output_file_id'])
    #puts content[0..100]

    content.each do |res|
      if res['custom_id'] && res['custom_id'].start_with?('prod_weight_')
        product_id = res['custom_id'].split('_')[2].to_i
        product = Product.find(product_id)

        begin
          weight_data = JSON.parse(res.dig('response', 'body', "choices", 0, "message", 'content'))
          weight = weight_data['weight']
          
          # Apply a safety margin by increasing weight by 10% and rounding up
          product.save
          
          puts "Updated weight for product #{product.id}: #{weight} → #{product.weight}"
        rescue => e
          puts "Error processing weight for product #{product.id}: #{e.message}"
        end
      end
    end
  end
end