require 'pony'

class SimaPp
  include SpAuthorize

  def initialize
    @log = Logger.new('log/sima-order-sender.txt')
    @token = Simaland::SimalandParser::JWT_TOKEN

    @ordered_for_stock = Hash.new(0)
  end

  def notify_error(text, subject)
    Pony.mail({
                :to => ['<EMAIL>', '<EMAIL>'], #
                :via => :smtp,
                :via_options => {
                  :address => 'smtp.gmail.com',
                  :port => '587',
                  :enable_starttls_auto => true,
                  :user_name => '<EMAIL>',
                  :password => 'wkrrgjviuxkwdmhp',
                  :authentication => :plain, # :plain, :login, :cram_md5, no auth by default
                  :domain => 'localhost.localdomain' # the HELO domain provided by the client to the server
                },
                :from => '<EMAIL>', :subject => subject, :html_body => text
              })
  rescue => e
    @log.error e.message
  end

  def load_goods(sids)
    @log.info sids
    ret = {}
    sids.each_slice(50) do |sid_part|
      res = @agent.get 'https://www.sima-land.ru/api/v3/item/', { sid: sid_part.join(',') }, nil, { 'x-api-key' => @token }
      items = JSON.parse(res.body)
      items['items'].each do |item|
        ret[item['sid'].to_i] = item['minQty']
        # ret[item['sid'].to_i]=5 if item['sid'].to_i==4820708
        # ret[item['sid'].to_i]=12 if item['sid'].to_i==320959
      end
    end
    puts ret
    ret
  end

  def load_orders(purchase, days_old: 60)

    authorize unless @sp_authorized

    pid = purchase.sp_pid

    date = (DateTime.now - days_old.days).strftime('%Y-%m-%d')
    # d={pid:pid,dateFrom:date,processingStatusFilter:2}
    d = { purchase: pid, dateFrom: date } #, processingStatus: 'wait_confirmation'

    # res = @agent.get 'https://www.100sp.ru/org/purchase/processOrders/api?', d, nil, { 'x-api-key' => SpApi::API_KEY }
    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', d, nil, { 'x-api-key' => SpApi::API_KEY }
    data = JSON.parse(res.body)

    ret = []
    if data['result']
      data['data'].each do |line|
        if line['megaorder_id'].to_i ==  880962179
          next if line['is_finished']=='недопоставка'
          next if SimaOrder.find_by_sp_order_id(line['oid'])
          ret << line
          next
        end
        next unless line['status'] == 'ожидает подтверждения'
        next unless line['payment_sum'].to_f > 0
        next unless line['megaorder_id'].to_i > 0
        next if line['is_finished'] == 'недопоставка'
        next if line['order_org_comment'].to_s.include? 'пропустить'
        next if line['megaorder_org_comment'].to_s.include? 'пропустить'

        @ordered_for_stock[art] += 1 if line['user_name'] == 'Moral'

        next if SimaOrder.find_by_sp_order_id(line['oid'])

        if line['articul'].start_with? 's'
          line['articul'] = find_sid(line['articul'].gsub(/--\d+$/, ''), line['size'], purchase)
        end

        ret << line
      end
    end
    ret
  end

  def load_orders_for_confirm(pid)
    authorize unless @sp_authorized
    #@agent.set_proxy("109.110.63.176", 8080) unless Rails.env.development?

    return [] if Rails.env.development? and pid.to_i != 990138

    date = (DateTime.now - 60.days).strftime('%Y-%m-%d')

    # d={pid:pid,dateFrom:date,processingStatusFilter:2}
    d = { purchase: pid, dateFrom: date } #, processingStatus: 'wait_confirmation'

    # res = @agent.get 'https://www.100sp.ru/org/purchase/processOrders/api?', d, nil, { 'x-api-key' => SpApi::API_KEY }
    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', d, nil, { 'x-api-key' => SpApi::API_KEY }
    data = JSON.parse(res.body)

    ret = []
    if data['result']
      data['data'].each do |line|
        # next unless line['status'] == 'ожидает подтверждения'
        next unless line['payment_sum'].to_f > 0
        next unless line['megaorder_id'].to_i > 0
        next if line['is_finished'] == 'недопоставка'

        next unless SimaOrder.find_by_sp_order_id(line['oid'])

        o = SimaOrder.find_by_sp_order_id(line['oid'])
        next if o.invoice_processed_at != nil

        art = line['articul']
        q = 1

        line['articul'].gsub!(/--\d+$/, '')
        # purchase=Purchase.where(sp_pid:pid).first
        # if purchase and art.start_with? 's'
        #  begin
        #    art = find_sid(line['articul'].gsub(/--\d+$/,''), line['size'], purchase)
        #  rescue
        #  end
        # end

        if /(\d+)-(\d+)/ =~ art
          art = $1
          q = $2.to_i
        end

        if r1 = ret.find { |l| l['Артикул'] == art }
          r1['Заказов'] += q
          r1['oid'] << line['oid']
        else

          ret << {
            'Артикул' => art,
            'Наименование' => line['name'],
            'Подробнее' => line['good_description'],
            'Размер' => line['size'].to_s.split('@')[0],
            'Заказов' => q,
            'oid' => [line['oid']],
            'Подтверждено' => 0
          }
        end
      end
    end

    ret
  end

  def update_stock_and_prices

  end

  def get_current_sima_order
    last_order_id = Cache.get_value('sima', 'last_sima_order_id')
    return nil if last_order_id.nil?

    res = @agent.get "https://www.sima-land.ru/api/v3/order/#{last_order_id}/", {}, nil, { 'x-api-key' => @token }
    d = JSON.parse(res.body)

    return nil if d and d['is_reorder_denied']
    return last_order_id if d and d['status_id'] and d['status_id'] == 8
    nil
  end

  def find_sid(sku, size, purchase)

    parent_id=sku.gsub('s','')
    sima_product = SimaProduct.where("data ->> 'parent_item_id' = ? AND data ->> 'modifier_value' = ?", parent_id, size)[0]
    if sima_product
      ret = sima_product.sid
      pack_size = SimaProduct.where("data ->> 'parent_item_id' = ?", parent_id).map {|p| p['data']['minQty'].to_i}.max

      if pack_size>1
        ret = "#{ret}-#{pack_size}"
      end
      @log.info "Real art #{ret}"
      return ret.to_s
    end

    #.map {|p| p['data']['minQty'].to_i}.max

    ps = Product.where('art like ?', sku + '%').where(purchase_id: purchase.id).order(updated_at: :desc)

    ps.each do |p|
      if p
        extra = JSON.parse(p.extra)
        @log.info size.inspect
        @log.info extra['size_data']
        if extra['size_data'][size]
          ret = extra['size_data'][size].to_s
          @log.info "Real art #{ret}"
          return ret
        end
        if extra['size_data'][size.gsub('.', ',')]
          ret = extra['size_data'][size.gsub('.', ',')].to_s
          @log.info "Real art #{ret}"
          return ret
        end

        ####### find size by start
        extra['size_data'].each do |s, real_art|
          next unless s.start_with? size
          ret = real_art.to_s
          @log.info "Real art #{ret}"
          return ret
        end
        ##########

      end
    rescue
    end

    @log.info "Error finding #{sku}, #{size}, #{purchase.id}"
    raise "Error finding #{sku}, #{size}, #{purchase.id}"
  end

  def mark_megaroders_including_sid(sid) end

  def get_pp_in_stock(sku)
    p = SpProduct.joins(:sp_purchase).where(sku: sku).where('sp_purchases.delivery_days > 0 AND sp_purchases.delivery_days < 5').first
    return 0 if p.nil?

    @api ||= SpApi.new

    @pp_stocks ||= {}
    return @pp_stocks[p.gid] if @pp_stocks[p.gid]

    stock = @api.get_stock(p.gid)
    if stock['-'] && stock['-'][:stock].to_i > 0
      @pp_stocks[p.gid] = stock['-']
      return stock['-']
    end
    nil
  end
  def get_in_stock(art)
    p = InventoryProduct.where("sku LIKE 'SIMA%'").where(supplier_sku: art).first
    return 0 if p.nil? or p.stock.nil?

    s = p.stock.select { |d| d['size'] == '-' && d['q'].to_i >= 0 }
    return 0 if s.empty?
    s.first['q'].to_i
  end

  def write_off_pp(gid, amount)
    raise "Ошибка списания из ПП" unless @pp_stocks[gid]

    @pp_stocks[gid][:stock] -= amount
    @pp_stocks[gid][:written_off] ||= 0
    @pp_stocks[gid][:written_off] += amount

    @pp_stocks[gid][:modified] = true
    @pp_stocks[gid][:stock] = 0 if @pp_stocks[gid][:stock] < 0
  end

  def write_off(art, amount, pids, user_id)
    if @writeoff_document.nil?
      @writeoff_document = InventoryDocument.create(
        user_id: user_id,
        doc_type: InventoryDocument.doc_types[:writeoff],
        name: "Списание для закупок Сима",
        posted: false
      )

      @log.info @writeoff_document
      TgBotNotify.send_message("Создан документ списания для закупок Сима. " +
                                 "https://spup.primavon.ru/inventory#/inventory_document/#{@writeoff_document.id}"
      )

    end

    prod = InventoryProduct.where("sku LIKE 'SIMA%'").where(supplier_sku: art).first
    if prod
      prod_id = prod.id
    else
      prod_name = "Товар #{l['gid']} #{l['articul']} #{l['name']} не найден в базе"
    end

    sizes = Hash.new(0)

    stock_temp = prod.stock.clone

    total_stock = get_in_stock(art)
    current_size = 0
    while total_stock > 0 and amount > 0
      if stock_temp[current_size]['q'] > 0
        stock_temp[current_size]['q'] -= 1
        total_stock -= 1
        amount -= 1
        sizes[stock_temp[current_size]['size']] += 1
      end
      current_size += 1
      current_size = 0 if current_size > stock_temp.count - 1
    end


    sizes.each do |size, q|
      idl = InventoryDocumentLine.where(inventory_product_id: prod_id,
                                        inventory_document_id: @writeoff_document.id,
                                        line_type: InventoryDocumentLine.line_types[:relative],
                                        size: size).first
      if idl
        idl.amount_change -= q
        idl.save
      else
        InventoryDocumentLine.create(
          inventory_product_id: prod_id,
          name: prod_name,
          size: size,
          amount_change: -q,
          inventory_document: @writeoff_document,
          line_type: InventoryDocumentLine.line_types[:relative]
        )
      end

    end
  end

  def get_fully_confirmed_megaorders(all_orders, confirmed_orders)
    megaorders = all_orders.map { |o| o['megaorder_id'] }.uniq
    megaorders.delete_if { |id| all_orders.select { |o| o['megaorder_id'] == id }.count != confirmed_orders.select { |o| o['megaorder_id'] == id }.count }
    megaorders
  end
  def send_order(force = false)
    sp_api = SpApi.new

    orders = []
    pids = []

    written_off = Hash.new(0)

    confirmed_from_stock = []
    confirmed_from_stock_pp = []

    user = nil
    Purchase.where(dlclass: 'Simaland::SimalandParser', is_pp: true).each do |p|
      next if p.sp_pid.to_i == 0
      pids << p.sp_pid
      orders += load_orders(p)
      user = p.user
    end

    # Purchase.where(dlclass:'Simaland::SimalandParser',is_pp:true).each {|p| orders+=load_orders}

    @log.info "Will send order for: #{orders}"

    orders.delete_if { |o| SimaOrder.find_by_sp_order_id(o['oid']) }

    order_sum = orders.sum { |o| o['price'].to_i }

    puts "New order sum: #{order_sum}"
    @log.info "New order sum: #{order_sum}"

    since_last_order = 999999999
    last_order = SimaOrder.order(:created_at).last
    if last_order
      since_last_order = (Time.now - last_order.created_at) / 60
    end

    puts "Minutes since last order: #{since_last_order}"
    @log.info "Minutes since last order: #{since_last_order}"

    return if order_sum < 3000 && since_last_order < 59 && !force

    @log.info "After removing already sent: #{orders}"

    return true if orders.count == 0

    puts orders

    # @agent.set_proxy("109.110.63.176", 8080) unless Rails.env.development?

    sids = orders.map { |d| d['articul'].to_i }.uniq
    good_info = load_goods(sids)
    puts good_info
    items_data = Hash.new(0)

    orders_from_transit = []

    orders.each do |o|
      if SimaOrder.find_by_sp_order_id(o['oid'])
        @log.info "Order #{o['oid']} #{o['gid']} already processed"
        next
      end
      q = 1
      art = o['articul']
      if art.to_s.include? '-'
        (art, q) = art.split('-')
        q = q.to_i
      end

      if Rails.env.development?
        art = '2272573'
      end

      # check stock
      in_stock = get_in_stock(art)
      in_stock -= written_off[art]
      @log.info "In stock: #{art} #{in_stock}"

      in_stock_pp = get_pp_in_stock(art)
      @log.info "In stock PP: #{art} #{in_stock_pp}"

      ## write off from sp hack TODO:
      in_stock = 0

      if in_stock > 0
        if in_stock >= q.to_i
          write_off(art, q.to_i, pids, user.id)
          written_off[art] += q.to_i
          q = 0
        else
          write_off(art, in_stock, pids, user.id)
          written_off[art] += in_stock
          q -= in_stock
        end


        orders_from_transit << o

        confirmed_from_stock << o
        #SimaOrder.where(sp_order_id: o['oid']).update_all(invoice_name: 'Списано из наличия', invoice_processed_at: Time.now)
      end

      #      if in_stock_pp && in_stock_pp[:stock] > 0
      #        if in_stock_pp[:stock] >= q.to_i
      #          write_off_pp(in_stock_pp[:gid], q.to_i)
      #          q = 0
      #        else
      #          write_off_pp(in_stock_pp[:gid], in_stock_pp[:stock])
      #          q -= in_stock_pp[:stock]
      #        end


      #orders_from_transit << o

      # o['size_id'] = in_stock_pp[:size_id]
      #        confirmed_from_stock_pp << o
        #SimaOrder.where(sp_order_id: o['oid']).update_all(invoice_name: 'Списано из наличия', invoice_processed_at: Time.now)
      #end


      # check transit
      # in_transit = SimaTransit.get_product_available_in_transit(art)
      in_transit = 0
      @log.info "In transit: #{art} #{in_transit}"
      if in_transit > 0

        t = SimaTransit.get_first_unfulfilled_transit(art)
        @log.info "First unfulfilled transit: #{t.inspect}"

        if in_transit >= q.to_i
          SimaTransit.add_deduction(art, q.to_i, o['megaorder_id'])
          q = 0
        else
          SimaTransit.add_deduction(art, in_transit, o['megaorder_id'])
          q -= in_transit
        end

        orders_from_transit << o

        sp_api.confirm_orders([o['oid']], o['purchaseId'], comment: t.invoice)
        SimaOrder.where(sp_order_id: o['oid']).update_all(invoice_name: t.invoice, invoice_processed_at: Time.now)
      end

      if q > 0
        items_data[art.to_i] += q.to_i
        o['sima_sid'] = art.to_i
      end
    end

    megaorders = get_fully_confirmed_megaorders(orders, confirmed_from_stock + confirmed_from_stock_pp)
    if megaorders.count > 0
      links = megaorders.map { |m| "https://www.100sp.ru/org/megaorder/#{m}" }.join(', ')
      text = "Заказы Сима #{links} полностью подтверждены из наличия"
      TgBotNotify.send_message(text)
    end

    return if Rails.env.development?

    return if items_data.count == 0

    items_data.each do |sid, q|
      min_order = 1
      min_order = good_info[sid] if good_info[sid]
      next unless min_order > 1 and q % min_order > 0
      left = min_order - q % min_order
      items_data[sid] += left

      # row = SimaTransit.find_by_art sid
      # if row
      #        row.amount += left
      #        row.save
      # else
      #        SimaTransit.create(art: sid, amount: left)
      # end

    end

    d = {
      payment_type_id: 0,
      manager_id: 1833,
      comment: '',
      notification_type_id: 1,
      counterparty_id: 1234378,
      manager_action: 2,
      contact_person: 'Алексей Боков',
      is_use_digital_signature: true,
      phone: '89046288826',
      person_type: 3,
      "deliveryTypeId": 3,
      "contact_name": 'Алексей Боков',
      "contact_phone": '89046288826',
      "paymentTypeId": 0,
      "contact_email": '<EMAIL>',
      'userDeliveryAddress' => {
        'street' => 'Калинина',
        'house_num' => '160',
        'settlement_id' => 27503886
      },
      #"payment_type_id":0

    }
    d[:items_data] = items_data.map { |k, v| { item_sid: k, qty: v } }
    reorder_id = get_current_sima_order
    d['re_ordering_id'] = reorder_id if reorder_id

    @log.info d
    puts d

    @agent.log = Logger.new('log/mechanize.txt')

    begin
      res = @agent.post 'https://www.sima-land.ru/api/v3/order/checkout-by-products/', d.to_json, { 'x-api-key' => @token, 'Content-type' => 'application/json;charset=UTF-8', 'Accept' => 'application/json' }
      @log.info res.body.force_encoding('utf-8')
      puts res.body.force_encoding('utf-8')
    rescue Mechanize::ResponseCodeError => e
      @log.info e.page.body.force_encoding('utf-8')
      puts e.page.body.force_encoding('utf-8')
      r = JSON.parse(e.page.body.force_encoding('utf-8'))
      if r[0]['field'] == 're_ordering_id'
        d.delete 're_ordering_id'
        res = @agent.post 'https://www.sima-land.ru/api/v3/order/checkout-by-products/', d.to_json, { 'x-api-key' => @token, 'Content-type' => 'application/json;charset=UTF-8', 'Accept' => 'application/json' }
        @log.info res.body.force_encoding('utf-8')
        puts res.body.force_encoding('utf-8')
      else

        stock_error = false

        r.each do |err|
          next unless err['field'] == 'items' && /product with sid (\d+) not enough on stocks/ =~ err['message']

          sid = $1

          stock_error = true

          megaorders = orders.select { |o| o['articul'].to_s.gsub(/-[-0-9]+$/, '') == sid }.map { |o| o['megaorder_id'] }.uniq

          megaorders.each { |id| sp_api.add_megaorder_comment('пропустить', id) }

          text = megaorders.map { |m| "<a href='https://www.100sp.ru/org/megaorder/#{m}'>#{m}</a>" }.join('<br/>')
          text = "Этого товара недостаточно в наличии: #{sid}, он был в следующих заказах: <br/>#{text}<br/><br/>В заказы сейчас добавлен комментарий " +
            "'пропустить', эти заказы пока не отправляются. Надо решить проблему и удалить комментарий при необходимости"

          notify_error(text, 'Недостаточно товара при отправке ПП Сима-ленд')
          TgBotNotify.send_message(text)

        end

        notify_error(r.to_s, 'Ошибка при отправке ПП Сима-ленд') unless stock_error
      end
    end

    if res.nil?
      return
    end
    d = JSON.parse(res.body)

    if d and d['is_enqueued']
      orders.each do |o|
        next if o['sima_sid'].nil?

        SimaOrder.create(sp_sku: o['articul'], sima_order_id: d['id'], sp_order_id: o['oid'], sp_megaorder_id: o['megaorder_id'],
                         sp_gid: o['gid'], sp_user: o['user_name'], sima_sid: o['sima_sid'])
      end
      Cache.set_value('sima', 'last_sima_order_id', d['id'])

      sleep(30)

      res = @agent.get "https://www.sima-land.ru/api/v3/order/#{d['id']}/?expand=interests,items",{},nil, { 'x-api-key' => @token, 'Accept' => 'application/json' }

      @log.info res.body.force_encoding('utf-8')
      order_data = JSON.parse(res.body)

      not_ordered = []
      orders.each do |o|
        next if order_data['items'].find { |i| i['item_sid'].to_i == o['articul'].to_i } ||
          orders_from_transit.find { |i| i['articul'].to_i == o['articul'].to_i }

        not_ordered << o
      end

      t = not_ordered.map { |o| "#{o['megaorder_id']}: #{o['articul']}" }.join(',')
      @log.info "Marking not delivered: #{t}"

      not_ordered.each do |o|
        sp_api.mark_not_delivered(o['megaorder_id'], [o['oid']])
      end

      if @writeoff_document
        confirmed_from_stock.each do |o|
          sp_api.confirm_orders([o['oid']], o['purchaseId'], comment: "Списано из наличия, документ списания #{@writeoff_document.id}")
        end

        ## write off from sp directly
        #
        #@writeoff_document.post
        #ploadStocksToSp.delay(:queue => 'upload_to_sp').run(user)
      end

      #modified_gids = @pp_stocks.select { |gid, d| d[:modified] }.keys
      #if modified_gids.count > 0
      #modified_gids.each do |gid|
      #sp_api.update_stock(gid, @pp_stocks[gid][:size_id], @pp_stocks[gid][:stock])
      #        end

      #text = 'Товары списаны для ПП под заказ: ' +
      #          modified_gids.map { |gid| "https://www.100sp.ru/good/#{gid} - #{d[:written_off]} шт" }.join(', ')
      #TgBotNotify.send_message(text)
      #end

      true
    end

  rescue Mechanize::ResponseCodeError => e
    @log.info "Error: #{e.page.body.force_encoding('utf-8')}"
    puts "Error: #{e.page.body.force_encoding('utf-8')}"
    puts e.backtrace
  rescue Exception => e
    TgBotNotify.send_message("Ошибка при отправке ПП Сима-ленд: #{e.to_s}")
    notify_error("#{e.to_s}<br/><pre>#{e.backtrace}</pre>", 'Ошибка при отправке ПП Сима-ленд') unless stock_error
  end

  def confirm_bill

  end

  def get_unconfirmed_orders
    megaorders = {}
    SimaOrder.where(invoice_processed_at: nil).where('created_at<?', DateTime.now - 5.days).order(:created_at).each do |o|
      megaorders[o.sp_megaorder_id] = [] unless megaorders[o.sp_megaorder_id]
      megaorders[o.sp_megaorder_id] << o
    end
    megaorders
  end

  def process_unconfirmed_orders(megaorders)
    pids = {}
    api = SpApi.new
    megaorders.keys.each do |megaorder|
      mo = api.load_megaorder(megaorder)
      mo.each do |order_id, status|
        if status[:confirm] == 'подтвержден' && status[:process] == 'недопоставка'
          sima_order = SimaOrder.find_by_sp_order_id(order_id)
          if sima_order && sima_order.invoice_processed_at.nil?
            sima_order.invoice_processed_at = Time.now
            sima_order.invoice_name = 'Недопоставка'
            sima_order.save
          end

        end
      end
    end
  end
  def process_missing_in_invoice
    megaorders = get_unconfirmed_orders
    process_unconfirmed_orders(megaorders)
    megaorders = get_unconfirmed_orders
    return if megaorders.keys.count == 0

    text = 'Есть необработанные недопоставки по симе:<br/><br/>'
    text += '<a href="https://spup.primavon.ru/#/sima_orders">Обратботка недопоставок</a><br/><br/>'

    tg_text = 'Есть необработанные недопоставки по симе. Обработка: https://spup.primavon.ru/#/sima_orders'

    TgBotNotify.send_message(tg_text)
    notify_error(text, 'Недопоставка Сима')
  end

  def reorder_collection
    # post https://www.100sp.ru/org/collection/goodsPriority
    # priority[0][gid] ...
    # priority[8][gid]: 974437904
    # priority[8][priority]:  8
    # cid: 16511531
  end
end