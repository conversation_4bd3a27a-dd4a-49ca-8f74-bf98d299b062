class UploadPurchase
  include SpAuthorize

  # @param [Integer] type purchase type 1 - sp, 3 - shop
  def create_new_purchase(name, type)
    raise "Wrong type: #{type}" unless [1, 3].include? type
    data = {
      'PurchaseEditForm[purchaseNumber]': '',
      'PurchaseEditForm[name]': name[0..59],
      'Purchases[fee]': '10',
      'Purchases[purchase_type]': type,
      'Purchases[duration_type]': 'once',
      'yt0': 'Создать покупку'
    }

    @log.info data

    @agent.redirect_ok = false
    page = @agent.post('https://www.100sp.ru/org/purchase/create', data)
    @agent.redirect_ok = true
    if page.code == '302'
      pid = page.header['location'].split('=').last.to_s.strip
      return pid
    end
    nil
  end

  def import_update(pid, data, purchase)
    @log=Logger.new('log/purchase_upload_add.txt')

    authorize
    do_import(pid, data, purchase, missing: 'drop_remains', overwrite: ['price', 'recommendedPrice'])
  end

  def do_import(pid, data, purchase, missing: 'drop_remains', overwrite: nil)
    out = CSV.generate(col_sep: ';') do |csv|
      data.each do |line|
        csv << line
      end
    end

    #out = "\xEF\xBB\xBF" + out
    fn = "/tmp/import_#{pid}_#{DateTime.now.to_i}.csv"
    File.write(fn, out)
    file = File.new(fn)

    #return if purchase.id==303

    params = {
      purchase: pid,
      duplicate: 'articul',
      photos: 'new',
      missing: missing,
      remains: 'overwrite',
      collections: 'leave',
      email: '<EMAIL>',
      file: file
    }

    overwrite&.each { |o| params["overwrite[#{o}]"] = o }

    @agent.open_timeout = 600
    @agent.read_timeout = 600

    purchase.send_ws_notification("Импорт на 100sp начат")

    @log.info pid
    @log.info params

    #return if purchase.is_pp && purchase.sp_pid.to_i != 983239 && purchase.sp_pid.to_i != 972652

    res = @agent.post "https://www.100sp.ru/org/purchase/importExport/apiImport", params, { 'x-api-key' => '60df1839d5b7869aae42e7fc1034b21f71e0899b5637b2ba42715a0a94099b7c' }
    @log.info res.body[0..1000]
    JSON.parse(res.body)
  end

  def add_products(purchase,pid,exclude)
    @log=Logger.new('log/purchase_upload_add.txt')

    authorize

    data = purchase.get_export_data(exclude_arts: exclude)

    return if data.count<=1
    r=do_import(pid, data, purchase, missing: 'nothing')

    @log.info "Imported CSV to #{pid}, result #{r}"
    sleep(180)

  end

  def add_products_data(pid, data, purchase)
    @log=Logger.new('log/purchase_upload_add.txt')

    authorize

    return if data.count<=1
    r=do_import(pid, data, purchase, missing: 'nothing')

    @log.info "Imported CSV to #{pid}, result #{r}"
    sleep(180)

  end

  def run(purchase,variants=nil )
    @log=Logger.new('log/purchase_upload.txt')

    @log.info "Start uploading #{purchase.id} #{purchase.name}"
    raise "Не загружены данные о покупке" unless purchase.purchase_data
    raise "Данные о покупке в старом формате, нужно обновить" unless purchase.purchase_data.is_a? Hash

    @log.info "Authorizing"
    authorize
    @log.info "Authorized"

    if variants.nil?
      variants = purchase.get_variants
    end

    @log.info variants

    purchase.purchase_data.each do |variant, data|
      @log.info variant
      @log.info data
      next unless variants.include? variant
      next unless data.is_a? Hash

      raise "Данные о покупке в старом формате, нужно обновить" unless data['type']
      @log.info "Creating purchase"
      pid = create_new_purchase("Для заливки #{purchase.name} #{variant}".strip, data['type'].to_i)
      @log.info "Created new purchase #{pid}, variant #{variant}"

      SpPurchase.find_or_create_by(
        user_id:purchase.user&.id,
        company_id:purchase.company&.id,
        pid:pid,
        mid:data['megapurchase_id'],
        name:"Для заливки #{purchase.name} #{variant}".strip,
        purchase_id:purchase.id,
        variant: variant,
        state:'Не начато'
      )

      raise "Ошибка создания закупки" if pid.blank?

      sleep(5)
      ld = LoadPurchaseData.new
      ld.upload(purchase, pid, variant: variant, skip_pics: true)
      collections = nil

      @log.info "Uploaded purchase data to #{pid}, variant #{variant}"

      unless variant.blank?
        collections = purchase.collections.select { |c| c.tags2!=nil and c.tags2.include? variant }.map { |c| c.id }
      end
      data = purchase.get_export_data(collections: collections)
      r=do_import(pid, data, purchase)

      if r['result']
        t1=Time.now.to_i
        @log.info "Imported CSV to #{pid}, variant #{variant}"
        sleep(180)
        ld.upload_purchase_pictures(purchase, pid, variant)
        sleep(5)

        @log.info "Uploaded collection pictures to #{pid}, variant #{variant}"

        upload_images = SpUploadCollectionImages.new
        upload_images.pid = pid
        upload_images.variant = variant
        upload_images.run(purchase, nil)

        @log.info "Finished uploading #{purchase.id} to #{pid}, variant #{variant}"

        t2=Time.now.to_i
        if t2-t1<200
          sleep 200-(t2-t1)
        end
      else
        @log.info "Error importing CSV to #{pid}, variant #{variant}: #{r['errors'][0]}"
        sleep(200)
      end
      @log.info "Finished sleeping after import #{purchase.id} to #{pid}, variant #{variant}"
    end

  end
end
