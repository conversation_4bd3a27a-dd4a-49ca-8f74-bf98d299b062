#encoding:utf-8
require_relative '../framework/file_download_config'

module LalaStyle
  class LalaStyleFileDownloadConfig < Framework::FileDownloadConfig
    def with_download_page(download_url)
      @download_page_url = download_url
      self
    end

    def save_file(file_name)
      download_page = @agent.get(@download_page_url)
      file_url = @download_url.call(download_page)
      puts file_url
      @agent.get(file_url).save(file_name)
    end
  end
end