module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user
    def connect
      self.current_user = find_verified_user
    end

    private
    def find_verified_user
      #if Rails.env.development?
      # User.find(1)
      #else
      token=nil
      token=request.params[:jwt].split[1] if request.params[:jwt] and request.params[:jwt].include? 'Bearer'
      verified_user=Warden::JWTAuth::UserDecoder.new.call(token, :user, nil) if token

      #Rails.logger.info env['warden'].user
        if verified_user
          verified_user
        else
          reject_unauthorized_connection
        end
      #end
    end
  end
end
