#encoding:utf-8
require_relative '../framework/site/product_config'

module Ivimarket
  class IvimarketProductConfig < Framework::Site::ProductConfig
    def with_excel_products(get_excel)
      @get_excel = get_excel
      self
    end

    def parse_product(product_page, link=nil)
      articul = get_articul(product_page)

      return nil if articul.nil?

      site_product = super(product_page, link)

      excel_product = @get_excel.call.find{|p| p.articul == articul}

      return nil if excel_product.nil?
      site_product.price = excel_product.price
      site_product.sizes = excel_product.sizes.clone

      site_product
    end
  end
end