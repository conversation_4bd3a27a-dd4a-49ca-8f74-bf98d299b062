(function(){"use strict";var t={9899:function(t,e,s){var o=s(5471),n=s(5453),r=s(8505),i=s(6750),a=s(6521),c=s(4739),l=s(7954),u=s(1735),d=function(){var t=this,e=t._self._c;return e(r.A,[e(i.A,{attrs:{app:"",color:"primary",dark:""}},[e(u.sw,[t._v(" SPUP Склад ")]),e(a.A,{staticClass:"ml-5",attrs:{to:"/purchases"}},[t._v("Покупки")]),e(a.A,{staticClass:"ml-5",attrs:{to:"/products"}},[t._v("Товары")]),e(a.A,{staticClass:"ml-5",attrs:{to:"/inventory_documents"}},[t._v("Документы")]),e(a.A,{staticClass:"ml-5",attrs:{to:"/pp"}},[t._v("ПП")]),e(a.A,{staticClass:"ml-5",attrs:{href:"http://spup.primavon.ru/"}},[t._v("Заливки")])],1),e(l.A,[e(c.A,{attrs:{fluid:""}},[e("router-view")],1),e("Modal")],1)],1)},p=[],h=s(7210),m=s(8834),_=s(4089),v=s(3224),g=s(7410),f=function(){var t=this,e=t._self._c;return e(v.A,{attrs:{row:"","justify-center":""}},[e(_.A,{attrs:{"max-width":"700",scrollable:""},model:{value:t.dialog,callback:function(e){t.dialog=e},expression:"dialog"}},[e(h.A,[e(m.ri,{staticClass:"headline"},[t._v(t._s(t.title))]),e(m.OQ,[e("p",{staticClass:"font-weight-black"},[t._v(t._s(t.text)+" ")]),e("pre",[t._v(t._s(t.trace)+" ")])]),e(m.SL,[e(g.A),e(a.A,{attrs:{color:"green darken-1",text:""},on:{click:t.close}},[t._v("Закрыть")])],1)],1)],1)],1)},y=[],A={data(){return{}},computed:{dialog(){return this.$store.state.errorModal},text(){return this.$store.state.errorModalText},trace(){return this.$store.state.errorModalTrace},title(){return this.$store.state.errorModalTitle}},methods:{close(){this.$store.dispatch("hideModalAction")}}},k=A,b=s(1656),x=(0,b.A)(k,f,y,!1,null,null,null),P=x.exports,D={name:"App",components:{Modal:P},data:()=>({})},C=D,w=(0,b.A)(C,d,p,!1,null,null,null),$=w.exports,S=(s(4301),s(2654));o.Ay.use(n.A);var I=new n.A({lang:{locales:{ru:S.A},current:"ru"}}),L=s(173),j=s(1526),O=s(1373),T=s(152),F=s(8),z=s(8230),M=s(9456),V=s(980),q=s(6520),E=s(8412),B=s(9940),U=s(7402),R=function(){var t=this,e=t._self._c;return e("div",{staticClass:"product"},[t.loading?e("div",[t._v(" Загрузка... ")]):t._e(),t.product?e(z.A,[e(c.A,[e(E.A,t._l(t.product.pics,(function(s,o){return e(j.A,{key:o,attrs:{cols:"1"}},[e(q.A,{attrs:{origin:"center center",transition:"scale-transition"},scopedSlots:t._u([{key:"activator",fn:function({on:n,attrs:r}){return[e(V.A,t._g(t._b({attrs:{src:t.product.small_pics[o],"max-height":"100",contain:""}},"v-img",r,!1),n),[e(a.A,{attrs:{fab:"","x-small":"",icon:""},on:{click:function(e){return e.stopPropagation(),t.deleteImage(s)}}},[e(M.A,[t._v("mdi-delete ")])],1)],1)]}}],null,!0)},[e(h.A,[e(V.A,{attrs:{src:s}})],1)],1)],1)})),1),e(E.A,[e(j.A,[e(F.A,{attrs:{counter:"","small-chips":"",dense:"","truncate-length":"50"},on:{change:t.selectImage}})],1),e(j.A,[e(B.A,{attrs:{outlined:"",clearable:"",label:"Ссылка на картинку"},model:{value:t.uploadUrl,callback:function(e){t.uploadUrl=e},expression:"uploadUrl"}})],1),e(j.A,[e(a.A,{attrs:{color:"success",dark:"",small:""},on:{click:t.upload}},[t._v(" Загузить "),e(M.A,{attrs:{right:"",dark:""}},[t._v("mdi-cloud-upload")])],1)],1)],1),e(E.A,[e(j.A,{attrs:{cols:"1"}},[t._v(" ID: "+t._s(t.product.id))]),e(j.A,{attrs:{cols:"2"}},[e(B.A,{attrs:{label:"Артикул",required:""},model:{value:t.product.sku,callback:function(e){t.$set(t.product,"sku",e)},expression:"product.sku"}})],1),e(j.A,{attrs:{cols:"2"}},[e(B.A,{attrs:{label:"Артикул поставщика",required:""},model:{value:t.product.supplier_sku,callback:function(e){t.$set(t.product,"supplier_sku",e)},expression:"product.supplier_sku"}})],1),e(j.A,{attrs:{cols:"3"}},[e(B.A,{attrs:{label:"Название",required:""},model:{value:t.product.name,callback:function(e){t.$set(t.product,"name",e)},expression:"product.name"}})],1),e(j.A,{attrs:{cols:"4"}},[e(O.A,{staticClass:"elevation-1",attrs:{headers:t.stockHeaders,items:t.stock,dense:"","hide-default-footer":""},scopedSlots:t._u([{key:"item.barcode",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.barcode},on:{"update:returnValue":function(e){return t.$set(s.item,"barcode",e)},"update:return-value":function(e){return t.$set(s.item,"barcode",e)}},scopedSlots:t._u([{key:"input",fn:function(){return[e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.barcode,callback:function(e){t.$set(s.item,"barcode",e)},expression:"props.item.barcode"}})]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.barcode)+" ")])]}},{key:"item.actions",fn:function({item:s}){return[e(M.A,{attrs:{small:""},on:{click:function(e){return t.deleteSize(s)}}},[t._v(" mdi-delete ")])]}}],null,!1,2219166332)})],1)],1),e(E.A,[e(j.A,[e(U.A,{attrs:{name:"input-7-1",filled:"",label:"Описание","auto-grow":""},model:{value:t.product.desc,callback:function(e){t.$set(t.product,"desc",e)},expression:"product.desc"}})],1)],1)],1)],1):t._e(),e(E.A,[e(j.A,{staticClass:"text-right"},[e(a.A,{staticClass:"mr-3",attrs:{color:"normal"},on:{click:t.showOperations}},[t._v(" Посмотреть движение ")]),e(a.A,{staticClass:"mr-3",attrs:{color:"normal"},on:{click:t.cancel}},[t._v(" Закрыть "),e(M.A,{attrs:{right:""}},[t._v(" mdi-close")])],1),e(a.A,{staticClass:"mr-3",attrs:{color:"normal"},on:{click:t.createCopy}},[t._v(" Создать копию "),e(M.A,{attrs:{right:""}},[t._v(" mdi-content-copy")])],1),e(a.A,{attrs:{color:"primary",loading:t.saving},on:{click:t.save}},[t._v(" Сохранить "),e(M.A,{attrs:{right:""}},[t._v(" mdi-content-save")])],1)],1)],1),e(E.A,[e(j.A,[e(O.A,{staticClass:"elevation-1",attrs:{headers:t.operationsHeader,items:t.operations,"items-per-page":1e3,dense:"","hide-default-footer":""},on:{"dblclick:row":t.openInventoryDocument}})],1)],1)],1)},H=[],N=(s(4114),s(4335)),Q=s(5353);o.Ay.use(Q.Ay);const W=new Q.Ay.Store({state:{errorModal:!1,errorModalText:"",errorModalTrace:"",errorModalTitle:"",productsOptions:{},documentsOptions:{},spCategories:null},mutations:{showErrorDialog(t,e){t.errorModal=!0,t.errorModalText=e.text,t.errorModalTrace=e.trace,t.errorModalTitle=e.title},hideErrorDialog(t){t.errorModal=!1},setProductsOptions(t,e){t.productsOptions=e},setDocumentsOptions(t,e){t.documentsOptions=e},setSpCategories(t,e){t.spCategories=e}},actions:{showModalAction({commit:t},e){t("showErrorDialog",e)},hideModalAction({commit:t}){t("hideErrorDialog")},setProductsOptions({commit:t},e){t("setProductsOptions",e)},setDocumentsOptions({commit:t},e){t("setDocumentsOptions",e)},setSpCategories({commit:t},e){t("setSpCategories",e)}},getters:{getErrorModal(t){return t.errorModalText},getProductsOptions(t){return t.productsOptions},getDocumentsOptions(t){return t.documentsOptions},getSpCategories(t){return t.spCategories}}}),Z=N.A.create({baseURL:"https://spup.primavon.ru/",headers:{"Content-type":"application/json"}});Z.interceptors.request.use((function(t){console.log("req");const e=localStorage.getItem("Auth_token");return console.log(e),e&&(t.headers["Authorization"]=e),t})),Z.interceptors.response.use((t=>t),(t=>{if(console.log(t.response.data),401===t.response.status)return delete localStorage.Auth_token,Promise.reject(t);let e=t.response.data;"string"===typeof e&&(e={text:e}),e.title="Ошибка",W.dispatch("showModalAction",e)}));var G=Z,J={getAllProducts(t,e=0,s=20){let o="/inventory_products?page="+e+"&per_page="+s;return t&&(o+="&collection_id="+t),G.get(o)},getProduct(t){return G.get("/inventory_products/"+t)},saveProduct(t){return console.log(t),G.patch("/inventory_products/"+t.id,t)},createProduct(){return G.post("/inventory_products.json")},getProductOperations(t){return G.get(`/inventory_products/${t}/get_operations`)},copyProduct(t,e){return console.log(t),G.post(`/inventory_products/${t.id}/copy`,{new_sku:e})},getAllInventoryDocuments(t=0,e=20,s=null,o=!1,n={}){const r=Object.entries(n).map((t=>`filter[${t[0]}]=${t[1].join(",")}`)).join("&");return G.get(`/inventory_documents.json?page=${t}&per_page=${e}&sort_by=${s}&desc=${o}&${r}`).catch((t=>{console.log(t)}))},getInventoryDocument(t){return G.get(`/inventory_documents/${t}.json`)},postInventoryDocument(t,e,s=!1){return G.patch(`/inventory_documents/${t}.json`,{post:!0,only_raise_prices:s,purchase_id:e})},saveInventoryDocument(t,e){return G.patch(`/inventory_documents/${t}.json`,{lines:e})},unpostInventoryDocument(t){return G.patch(`/inventory_documents/${t}.json`,{post:!1})},deleteInventoryDocument(t){return G.delete(`/inventory_documents/${t}.json`)},recalcInventoryDocument(t,e,s,o){return G.post(`/inventory_documents/${t}/recalc_prices.json`,{src_price_type:e,dest_price_type:s,multiplier:o})},createInventoryDocument(t){return G.post("/inventory_documents.json",{doc_type:t})},fillAdjust(t,e){return G.post(`/inventory_documents/${t}/fill_adjust.json`,{id:t,purchase_id:e})},fillRevalue(t,e){return G.post(`/inventory_documents/${t}/fill_revalue.json`,{id:t,purchase_id:e})},saveAdjust(t,e){return G.post(`/inventory_documents/${t}/save_adjust.json`,{id:t,lines:e})},checkProductsExist(t){return G.post("/inventory_products/check_products_exist.json",t,{headers:{"Content-Type":"multipart/form-data"}})},fillDocumentWithFile(t,e){return G.post(`/inventory_documents/${t}/fill_file.json`,e,{headers:{"Content-Type":"multipart/form-data"}})},getAllSpPp(t=0,e=20){return G.get("/purchases.json?pp=true&page="+t+"&per_page="+e)},getAllPurchases(){return G.get("/purchases.json")},getTransit(){return G.get("/api3/sima_transit")},getAllCategories(){return G.get("/sp_categories/index.json")},getPurchase(t){return G.get(`/purchases/${t}.json`)},getCollections(t){return G.get(`/inventory_folders/${t}.json`)},getSpImportFile(t){return G.get(`/purchases/${t}/getcsv?use_stock=true`)},searchProductOnServer(t,e=!1){return G.get(`/api2/search/${t}/${e}`)},createInventoryDocumentLine(t,e,s=null){return G.post("/inventory_document_lines.json",{doc_id:t,product:e,size:s})},updateInventoryLine(t,e,s){return G.patch(`/inventory_document_lines/${t}.json`,{new_amount:e,check:s})},updateDiscountData(t,e,s){return G.patch(`/api3/purchases/${t}/discount_data.json`,{discount:e,discountUntil:s})},upload_stock(){return G.post("/api2/do_sp_import")},login(t,e){return G.post("/api3/login",{user:{email:t,password:e}})},sendDocToCashier(t){return G.post("/api2/send_doc_to_cashier",{doc_id:t})},uploadProductImage(t,e,s){let o=new FormData;return o.append("file",t),G.post(`/inventory_products/${e}/upload`,o,{headers:{"Content-Type":"multipart/form-data"},progress:s})}},K={data(){return{product:null,stock:[],operations:[],loading:!1,saving:!1,currentImage:void 0,uploadUrl:"",stockHeaders:[{text:"Размер",align:"start",sortable:!1,value:"size"},{text:"Штрихкод",align:"start",sortable:!1,value:"barcode"},{text:"Остаток",align:"start",sortable:!1,value:"stock"},{text:"Цена закуп",align:"start",sortable:!1,value:"buy_price"},{text:"Цена СП",align:"start",sortable:!1,value:"sp_price"},{text:"Цена роз",align:"start",sortable:!1,value:"retail_price"},{text:"РРЦ",align:"start",sortable:!1,value:"rrp"},{text:"",align:"start",sortable:!1,value:"actions"}],operationsHeader:[{text:"Дата",align:"start",sortable:!1,value:"created_at"},{text:"ID",align:"start",sortable:!1,value:"inventory_document_id"},{text:"Документ",align:"start",sortable:!1,value:"doc_name"},{text:"Размер",align:"start",sortable:!1,value:"size"},{text:"Количество",align:"start",sortable:!1,value:"q"},{text:"Остаток",align:"start",sortable:!1,value:"remain"},{text:"Цена СП",align:"start",sortable:!1,value:"sp_price"}]}},created(){this.loadProduct(),this.load},methods:{loadProduct(){this.loading=!0;const t=this.$route.params.id;J.getProduct(t).then((t=>{this.loading=!1,this.product=t.data.product,this.stock=t.data.product.stock.map((t=>({size:t.size,stock:t.q,barcode:t.barcode,buy_price:t.buy_price,sp_price:t.sp_price,retail_price:t.retail_price,rrp:t.rrp}))),console.log(t.data)})).catch((t=>{this.loading=!1,console.log(t)}))},save(){console.log(this.product),console.log(this.stock),this.saving=!0,this.product.stock=[],this.stock.forEach((t=>{this.product.stock.push(t)})),J.saveProduct(this.product).then((()=>{this.saving=!1})).catch((t=>{this.saving=!1,console.log(t)}))},createCopy(){this.product.sku+=" copy",J.copyProduct(this.product,this.product.sku).then((t=>{this.saving=!1,this.$router.push({path:`/product/${t.data.id}`})})).catch((t=>{this.saving=!1,this.$store.dispatch("showModalAction",{text:t,trace:"",title:"Ошибка"}),console.log(t)}))},showOperations(){J.getProductOperations(this.product.id).then((t=>{this.operations=t.data.data.map((t=>this.displayOperationLine(t))),console.log(t.data)})).catch((t=>{this.$store.dispatch("showModalAction",{text:t,trace:"",title:"Ошибка"}),console.log(t)}))},displayOperationLine(t){return{id:t.id,created_at:t.created_at,inventory_document_id:t.inventory_document_id,doc_name:t.inventory_document.name,q:"absolute"==t.line_type?`=${t.amount_change}`:t.amount_change,sp_price:t.sp_price,size:t.size,remain:t.remain}},openInventoryDocument(t,e){this.$router.push({path:`/inventory_document/${e.item.inventory_document_id}`})},cancel(){this.$router.go(-1)},deleteSize(t){const e=this.stock.indexOf(t);this.stock.splice(e,1)},addSize(){this.stock.push({size:"Новый размер",stock:0,buy_price:0,sp_price:0,retail_price:0})},selectImage(t){this.currentImage=t},upload(){this.currentImage&&J.uploadProductImage(this.currentImage,this.product.id,(t=>{console.log(t)})).then((()=>{this.currentImage=null,this.uploadUrl=""}))},uploadProgress(t){console.log(t.loaded)}},watch:{$route:"loadProduct"}},X=K,Y=(0,b.A)(X,R,H,!1,null,null,null),tt=Y.exports,et=s(9417),st=function(){var t=this,e=t._self._c;return e(O.A,{staticClass:"elevation-1",attrs:{headers:t.headers,items:t.products,"items-per-page":50,page:t.currentPage,"server-items-length":t.totalProducts,options:t.options,dense:"","footer-props":{showFirstLastPage:!0,itemsPerPageOptions:[50,100,500,-1]}},on:{"update:options":function(e){t.options=e},"dblclick:row":t.openProduct},scopedSlots:t._u([{key:"item.pic",fn:function({item:s}){return[e(q.A,{attrs:{origin:"center center",transition:"scale-transition"},scopedSlots:t._u([{key:"activator",fn:function({on:o,attrs:n}){return[e(V.A,t._g(t._b({attrs:{src:s.small_pic}},"v-img",n,!1),o))]}}],null,!0)},[e(h.A,[e(V.A,{attrs:{src:s.pic}})],1)],1)]}},{key:"top",fn:function(){return[e(E.A,[e(j.A,{attrs:{cols:"8"}},[e(B.A,{staticClass:"mx-4",attrs:{label:"Поиск",clearable:""},model:{value:t.search,callback:function(e){t.search=e},expression:"search"}})],1),e(j.A,{attrs:{cols:"2"}},[e(et.A,{attrs:{items:t.folders,"item-text":"name","return-object":"true",dense:""},on:{change:t.folderSelected},model:{value:t.folder,callback:function(e){t.folder=e},expression:"folder"}})],1),e(j.A,{attrs:{cols:"2"}},[e(a.A,{on:{click:t.createProduct}},[t._v("Создать")])],1)],1)]},proxy:!0}])})},ot=[],nt=s(2543),rt={data(){return{headers:[{text:"Картинка",align:"start",sortable:!1,value:"pic"},{text:"Артикул",align:"start",sortable:!1,value:"sku"},{text:"Название",value:"name"},{text:"Остатки",value:"stock"}],products:[],totalProducts:0,currentPage:1,options:{},loading:!0,collectionId:null,search:null,searchInProgress:!1,folders:[],folder:null}},watch:{options:{handler(){this.$store.dispatch("setProductsOptions",this.options),this.loadProducts()},deep:!0},search:{handler(){this.doSearch()}}},methods:{openProduct(t,e){console.log(e),this.$router.push({path:`/product/${e.item.id}`})},async createProduct(){const t=await J.createProduct();let e=t.data;this.$router.push({path:`/product/${e.id}`})},folderSelected(){this.collectionId=this.folder.id,this.loadProducts()},loadProducts(){this.loading=!0;const{page:t,itemsPerPage:e}=this.$store.getters.getProductsOptions;let s=t-1;J.getAllProducts(this.collectionId,s,e).then((t=>{this.loading=!1,this.products=t.data.products.map(this.getDisplayProduct),this.totalProducts=t.data.count,this.folders=t.data.folders,this.folders.unshift({id:0,name:"Все товары"}),null==this.folder&&(this.folder=this.folders[0]),console.log(t.data)})).catch((t=>{this.loading=!1,console.log(t)}))},refreshList(){this.loadProducts()},getDisplayProduct(t){return console.log(t),{id:t.id,sku:t.sku,name:t.name,folder:t.folder,price:t.price,stock:t.stock?t.stock.map((t=>`${t.size}@${t.q}#${t.sp_price}`)).join(","):"",pic:t.pic,small_pic:t.small_pic}},doSearch:function(){this.debouncedSearch()},productSearch:function(t){console.log(t),this.$nextTick((()=>{this.productSearch=null,this.addSearch=null}))},searchOnServer(){null!=this.search&&""!=this.search.trim()?(this.searchInProgress=!0,J.searchProductOnServer(this.search,!0).then((t=>{this.loading=!1,this.products=t.data.products.map(this.getDisplayProduct),this.totalProducts=t.data.total})).finally((()=>{this.searchInProgress=!1}))):this.refreshList()}},created(){this.debouncedSearch=(0,nt.debounce)(this.searchOnServer,500)},mounted(){this.options=this.$store.getters.getProductsOptions,this.collectionId=this.$route.params.id}},it=rt,at=(0,b.A)(it,st,ot,!1,null,null,null),ct=at.exports,lt=s(7155),ut=s(1770),dt=s(2987),pt=s(1075),ht=s(6315),mt=s(2721),_t=s(8152),vt=s(4271),gt=function(){var t=this,e=t._self._c;return e(c.A,[e(E.A,[e(j.A,[e(q.A,{attrs:{"offset-y":""},scopedSlots:t._u([{key:"activator",fn:function({on:s,attrs:o}){return[e(a.A,t._g(t._b({attrs:{color:"primary",dark:""}},"v-btn",o,!1),s),[t._v("Создать")])]}}])},[e(dt.A,[e(pt.A,{on:{click:function(e){return t.createDocument("receipt")}}},[e(mt.pr,[e(mt.UZ,[t._v("Поступление")])],1)],1),e(pt.A,{on:{click:function(e){return t.createDocument("sale")}}},[e(mt.pr,[e(mt.UZ,[t._v("Продажа")])],1)],1),e(pt.A,{on:{click:function(e){return t.createDocument("adjust")}}},[e(mt.pr,[e(mt.UZ,[t._v("Сверка")])],1)],1),e(pt.A,{on:{click:function(e){return t.createDocument("writeoff")}}},[e(mt.pr,[e(mt.UZ,[t._v("Списание")])],1)],1),e(pt.A,{on:{click:function(e){return t.createDocument("revalue")}}},[e(mt.pr,[e(mt.UZ,[t._v("Переоценка")])],1)],1)],1)],1)],1),e(j.A,[e(lt.A,{attrs:{label:"Не понижать цены при проведении"},model:{value:t.only_raise_prices,callback:function(e){t.only_raise_prices=e},expression:"only_raise_prices"}})],1)],1),e(E.A,[e(j.A,[e(O.A,{staticClass:"elevation-1",attrs:{headers:t.headers,items:t.inventoryDocuments,"items-per-page":50,page:t.currentPage,"server-items-length":t.totalInventoryDocuments,options:t.options,dense:"","footer-props":{showFirstLastPage:!0,itemsPerPageOptions:[50,100,500,-1]}},on:{"update:options":function(e){t.options=e},"dblclick:row":t.openInventoryDocument},scopedSlots:t._u([t._l(t.filters,(function(s,o,n){return{key:`header.${o}`,fn:function({header:s}){return[e("div",{key:o,staticStyle:{display:"inline-block",padding:"16px 0"}},[t._v(" "+t._s(s.text)+" ")]),e("div",{key:o+"1",staticStyle:{float:"right","margin-top":"8px"}},[e(q.A,{staticStyle:{position:"absolute",right:"0"},attrs:{"close-on-content-click":!1,"nudge-width":200,"offset-y":"",transition:"slide-y-transition",left:"",fixed:""},scopedSlots:t._u([{key:"activator",fn:function({on:o,attrs:n}){return[e(a.A,t._g(t._b({attrs:{color:"indigo",icon:""}},"v-btn",n,!1),o),[e(M.A,{attrs:{small:"",color:t.activeFilters[s.value]&&t.activeFilters[s.value].length<t.filters[s.value].length?"red":"default"}},[t._v("mdi-filter-variant")])],1)]}}],null,!0)},[e(dt.A,{staticClass:"pa-0",attrs:{flat:"",dense:""}},[e(_t.A,{staticClass:"py-2",attrs:{multiple:""},model:{value:t.activeFilters[s.value],callback:function(e){t.$set(t.activeFilters,s.value,e)},expression:"activeFilters[header.value]"}},[t._l(t.filters[s.value],(function(s){return[e(pt.A,{key:`${s}`,attrs:{value:s,ripple:!1},scopedSlots:t._u([{key:"default",fn:function({active:o}){return[e(ht.A,[e(lt.A,{attrs:{"input-value":o,"true-value":s,color:"primary",ripple:!1,dense:""}})],1),e(mt.pr,[e(mt.UZ,{domProps:{textContent:t._s(s)}})],1)]}}],null,!0)})]}))],2),e(ut.A),e(E.A,{attrs:{"no-gutters":""}},[e(j.A,{attrs:{cols:"6"}},[e(a.A,{attrs:{text:"",block:"",color:"success"},on:{click:function(e){return t.toggleAll(s.value)}}},[t._v("Toggle all")])],1),e(j.A,{attrs:{cols:"6"}},[e(a.A,{attrs:{text:"",block:"",color:"warning"},on:{click:function(e){return t.clearAll(s.value)}}},[t._v("Clear all")])],1)],1)],1)],1)],1)]}}})),{key:"item.actions",fn:function({item:s}){return["Нет"==s.posted?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.postItem(s)}}},[t._v("mdi-check-bold")]):t._e(),"Да"==s.posted?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.unpostItem(s)}}},[t._v("mdi-cancel")]):t._e(),"Нет"==s.posted?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.deleteItem(s)}}},[t._v("mdi-delete")]):t._e()]}},{key:"no-data",fn:function(){return[e(a.A,{attrs:{color:"primary"},on:{click:t.initialize}},[t._v("Reset")])]},proxy:!0}],null,!0)})],1)],1),e(_.A,{attrs:{"max-width":"700",scrollable:""},model:{value:t.purchaseSelectDialog,callback:function(e){t.purchaseSelectDialog=e},expression:"purchaseSelectDialog"}},[e(h.A,[e(m.ri,{staticClass:"headline"},[t._v("Выберите закупку для создания новых товаров")]),e(m.OQ,[e(vt.A,{attrs:{items:t.purchases,"item-text":"name","item-value":"id"},model:{value:t.selectedPurchase,callback:function(e){t.selectedPurchase=e},expression:"selectedPurchase"}})],1),e(m.SL,[e(g.A),e(a.A,{attrs:{color:"green darken-1",flat:"flat"},on:{click:t.postReceipt}},[t._v("Провести")]),e(a.A,{attrs:{color:"blue darken-1",text:""},on:{click:function(e){t.purchaseSelectDialog=!1}}},[t._v("Отмена")])],1)],1)],1)],1)},ft=[],yt=s(866),At={data(){const t={sale:"Продажа",import:"Импорт",direct_entry:"Ввод остатков",writeoff:"Списание",receipt:"Поступление",adjust:"Сверерка",revalue:"Переоценка"};return{activeFilters:{},filters:{posted:[],doc_type_text:[]},inventoryDocuments:[],totalInventoryDocuments:0,currentPage:1,options:{},loading:!0,purchaseSelectDialog:!1,purchases:[],postItemValue:null,selectedPurchase:null,docTypes:t,only_raise_prices:!0,docTypesInv:yt(t)}},computed:{headers(){return[{text:"ID",value:"id"},{text:"Название",align:"start",sortable:!0,value:"name"},{text:"Тип",align:"start",sortable:!0,value:"doc_type_text"},{text:"Дата создания",value:"date",sortable:!0},{text:"Проведен",value:"posted",sortable:!0,filter:t=>!this.activeFilters.posted||this.activeFilters.posted.includes(t)},{text:"Дата проведения",value:"posted_at",sortable:!0},{text:"Строк",value:"lineCount"},{text:"Сумма",value:"sum"},{text:"Команды",value:"actions",sortable:!1}]}},watch:{options:{handler(){this.options.activeFilters=this.activeFilters,this.$store.dispatch("setProductsOptions",this.options),this.loadInventoryDocuments()},deep:!0},activeFilters:{handler(){this.options.activeFilters=this.activeFilters,this.$store.dispatch("setProductsOptions",this.options),this.loadInventoryDocuments()},deep:!0}},methods:{initialize(){},initFilters(){this.filters={posted:["Да","Нет"],doc_type_text:Object.values(this.docTypes)},this.activeFilters=Object.assign({},this.filters);const t=this.$store.getters.getProductsOptions;t.activeFilters&&(this.activeFilters=t.activeFilters)},toggleAll(t){this.activeFilters[t]=this.filters[t]},clearAll(t){this.activeFilters[t]=[]},postItem(t){if(console.log(t),"receipt"==t.doc_type||"adjust"==t.doc_type||"import"==t.doc_type)return this.postItemValue=t,void(this.purchaseSelectDialog=!0);this.loading=!0,J.postInventoryDocument(t.id,null,this.only_raise_prices).then((()=>{this.loading=!1,this.loadInventoryDocuments(),this.purchaseSelectDialog=!1}))},postReceipt(){this.loading=!0,J.postInventoryDocument(this.postItemValue.id,this.selectedPurchase,this.only_raise_prices).then((()=>{this.loading=!1,this.loadInventoryDocuments(),this.purchaseSelectDialog=!1}))},unpostItem(t){console.log(t),this.loading=!0,J.unpostInventoryDocument(t.id).then((()=>{this.loading=!1,this.loadInventoryDocuments()}))},deleteItem(t){console.log(t),this.loading=!0,J.deleteInventoryDocument(t.id).then((()=>{this.loading=!1,this.loadInventoryDocuments()}))},openInventoryDocument(t,e){console.log(e),this.$router.push({path:`/inventory_document/${e.item.id}`})},createDocument(t){J.createInventoryDocument(t).then((()=>{this.loadInventoryDocuments()}))},loadInventoryDocuments(){this.loading=!0;const{sortBy:t,sortDesc:e,page:s,itemsPerPage:o,activeFilters:n}=this.$store.getters.getProductsOptions;this.activeFilters=n;let r=s-1;const i={posted:this.activeFilters.posted,doc_type:this.activeFilters.doc_type_text.map((t=>this.docTypesInv[t]))};J.getAllInventoryDocuments(r,o,t,e,i).then((t=>{null!=t&&(this.loading=!1,this.inventoryDocuments=t.data.inventoryDocuments.map(this.getDisplayInventoryDocument),this.totalInventoryDocuments=t.data.count,console.log(t.data))}))},refreshList(){this.loadInventoryDocuments()},getDisplayInventoryDocument(t){return{id:t.id,name:t.name,date:t.created_at,posted:t.posted?"Да":"Нет",posted_at:t.posted_at,lineCount:t.lineCount,doc_type:t.doc_type,doc_type_text:this.docTypes[t.doc_type],sum:t.sum}},loadPurchaseList(){J.getAllSpPp(0,-1).then((t=>{this.purchases=t.data.purchases})).catch((t=>{console.log(t)}))}},created(){},mounted(){this.options=this.$store.getters.getProductsOptions,void 0==this.options.sortBy&&(this.options.sortBy=["id"],this.options.sortDesc=[!0]),this.loadPurchaseList(),this.initFilters()}},kt=At,bt=(0,b.A)(kt,gt,ft,!1,null,null,null),xt=bt.exports,Pt=s(1906),Dt=s(3021),Ct=function(){var t=this,e=t._self._c;return e("div",{staticClass:"inventory-document"},[t.loading?e("div",[t._v("Загрузка...")]):t._e(),t.document&&!t.loading?e(c.A,{attrs:{fluid:"",dense:"",lass:"fill-height"}},[e(E.A,{attrs:{dense:"",justify:"space-between"}},[e(j.A,[e("h2",[t._v(" "+t._s(t.document.name)+" "+t._s(t.document.id)+" от "+t._s(t.document.created_at)+" "),t.document.megaorder_id?e(a.A,{attrs:{icon:"",href:"https://www.100sp.ru/org/megaorder/"+t.document.megaorder_id,target:"_blank"}},[e(M.A,[t._v("mdi-open-in-new")])],1):t._e()],1)]),e(j.A,{attrs:{cols:"1"}},[e(a.A,{attrs:{elevation:"3",icon:"",small:"",color:"primary"},on:{click:function(e){t.showControls=!t.showControls}}},[t.showControls?e(M.A,[t._v("mdi-close")]):t._e(),t.showControls?t._e():e(M.A,[t._v("mdi-plus")])],1)],1)],1),e(E.A,{attrs:{dense:""}},[e(j.A,[e(Pt.Qo,[e(c.A,{directives:[{name:"show",rawName:"v-show",value:t.showControls,expression:"showControls"}],attrs:{dense:""}},["adjust"!=t.document.doc_type?e(E.A,{attrs:{dense:""}},[e(j.A,[t._v(" Пересчет цен: задать ")]),e(j.A,[e(vt.A,{attrs:{dense:"",items:t.destPrice},model:{value:t.destPriceValue,callback:function(e){t.destPriceValue=e},expression:"destPriceValue"}})],1),e(j.A,[t._v(" равной ")]),e(j.A,[e(vt.A,{attrs:{dense:"",items:t.srcPrice},model:{value:t.srcPriceValue,callback:function(e){t.srcPriceValue=e},expression:"srcPriceValue"}})],1),e(j.A,[t._v(" * ")]),e(j.A,[e(B.A,{attrs:{dense:""},model:{value:t.recalcPercent,callback:function(e){t.recalcPercent=e},expression:"recalcPercent"}})],1),e(j.A,[e(a.A,{attrs:{dense:"",color:"primary"},on:{click:t.doRecalc}},[t._v("Пересчитать")])],1)],1):t._e(),"adjust"!=t.document.doc_type?e(E.A,[e(j.A,[t._v(" Заполнить из файла: ")]),e(j.A,[e(F.A,{on:{change:t.fileChanged}})],1),e(j.A,[e(a.A,{attrs:{dense:"",color:"primary"},on:{click:t.fillDocumentWithFile}},[t._v("Заполнить")])],1)],1):t._e(),"adjust"!=t.document.doc_type&&"revalue"!=t.document.doc_type||null!=t.documentLines&&0!=t.documentLines.length?t._e():e(E.A,{attrs:{dense:""}},[e(j.A,[t._v(" Заполнить из закупки: ")]),e(j.A,[e(vt.A,{attrs:{dense:"",items:t.purchaseList},model:{value:t.selectedPurchase,callback:function(e){t.selectedPurchase=e},expression:"selectedPurchase"}})],1),e(j.A,[e(a.A,{attrs:{dense:"",color:"primary"},on:{click:t.fillAdjust}},[t._v("Заполнить")])],1)],1)],1)],1)],1)],1),e(E.A,[e(j.A,[e(O.A,{ref:"documents",staticClass:"elevation-1",attrs:{headers:t.inventoryDocumentHeaders,items:t.documentLines,"item-key":"vId",dense:"","fixed-header":"",height:"60vh",search:t.search,"items-per-page":100,"footer-props":{showFirstLastPage:!0,itemsPerPageOptions:[50,100,500,-1]},"item-class":t.itemRowBackground},on:{"current-items":t.getFiltered,"dblclick:row":t.openProduct},scopedSlots:t._u([{key:"top",fn:function(){return["receipt"!=t.document.doc_type&&"writeoff"!=t.document.doc_type&&"sale"!=t.document.doc_type&&"revalue"!=t.document.doc_type?e(B.A,{staticClass:"mx-4",attrs:{label:"Поиск",clearable:""},model:{value:t.search,callback:function(e){t.search=e},expression:"search"}}):t._e(),"receipt"==t.document.doc_type||"writeoff"==t.document.doc_type||"sale"==t.document.doc_type||"revalue"==t.document.doc_type?e(et.A,{staticClass:"mx-4",attrs:{label:"Поиск",loading:t.searchInProgress,items:t.searchItems,"search-input":t.addSearch,clearable:"","cache-items":"","hide-no-data":"","hide-details":""},on:{"update:searchInput":function(e){t.addSearch=e},"update:search-input":function(e){t.addSearch=e}},model:{value:t.productSearch,callback:function(e){t.productSearch=e},expression:"productSearch"}}):t._e()]},proxy:!0},{key:"item.checked",fn:function({item:s}){return[e(lt.A,{on:{change:function(e){return t.onLineCheck(s)}},model:{value:s.checked,callback:function(e){t.$set(s,"checked",e)},expression:"item.checked"}})]}},{key:"item.pic",fn:function({item:s}){return[e(q.A,{attrs:{origin:"center center",transition:"scale-transition"},scopedSlots:t._u([{key:"activator",fn:function({on:o,attrs:n}){return[e(V.A,t._g(t._b({attrs:{src:s.small_pic}},"v-img",n,!1),o))]}}],null,!0)},[e(h.A,[e(V.A,{attrs:{src:s.pic}})],1)],1)]}},{key:"item.new_amount",fn:function(s){return[s.item.checked?t._e():e(T.A,{attrs:{"return-value":s.item.new_amount},on:{"update:returnValue":function(e){return t.$set(s.item,"new_amount",e)},"update:return-value":function(e){return t.$set(s.item,"new_amount",e)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.new_amount,callback:function(e){t.$set(s.item,"new_amount",e)},expression:"props.item.new_amount"}}):t._e()]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.new_amount)+" ")]),s.item.checked?e("span",[t._v(" "+t._s(s.item.new_amount)+" ")]):t._e()]}},{key:"item.transit_info",fn:function(s){return[t._l(s.item.transit_info,(function(o){return e("div",{key:o.id},[e("a",{attrs:{href:`https://100sp.ru/megaorder/${o.megaorder_id}`}},[t._v(t._s(o.megaorder_id))]),t._v(": "+t._s(o.amount)+" "),e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.deleteTransitDataOne(s.item,o.id)}}},[t._v(" mdi-delete ")])],1)})),0==t.document.posted&&s.item.transit_info&&s.item.transit_info.length>0?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.deleteTransitData(s.item)}}},[t._v(" mdi-delete ")]):t._e()]}},{key:"item.buy_price",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.buy_price},on:{"update:returnValue":function(e){return t.$set(s.item,"buy_price",e)},"update:return-value":function(e){return t.$set(s.item,"buy_price",e)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.buy_price,callback:function(e){t.$set(s.item,"buy_price",e)},expression:"props.item.buy_price"}}):t._e()]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.buy_price)+" ")])]}},{key:"item.sp_price",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.sp_price},on:{"update:returnValue":function(e){return t.$set(s.item,"sp_price",e)},"update:return-value":function(e){return t.$set(s.item,"sp_price",e)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.sp_price,callback:function(e){t.$set(s.item,"sp_price",e)},expression:"props.item.sp_price"}}):t._e()]},proxy:!0}],null,!0)},[e("span",{staticClass:"text--disabled caption"},[t._v(t._s(s.item.sp_price)+" -> ")]),t._v(" "+t._s(s.item.sp_price)+" ")])]}},{key:"item.retail_price",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.retail_price},on:{"update:returnValue":function(e){return t.$set(s.item,"retail_price",e)},"update:return-value":function(e){return t.$set(s.item,"retail_price",e)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.retail_price,callback:function(e){t.$set(s.item,"retail_price",e)},expression:"props.item.retail_price"}}):t._e()]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.retail_price)+" ")])]}},{key:"item.rrp",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.rrp},on:{"update:returnValue":function(e){return t.$set(s.item,"rrp",e)},"update:return-value":function(e){return t.$set(s.item,"rrp",e)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.rrp,callback:function(e){t.$set(s.item,"rrp",e)},expression:"props.item.rrp"}}):t._e()]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.rrp)+" ")])]}},{key:"item.size",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.size},on:{"update:returnValue":function(e){return t.$set(s.item,"size",e)},"update:return-value":function(e){return t.$set(s.item,"size",e)},save:function(e){return t.saveSize(s.item)}},scopedSlots:t._u([{key:"input",fn:function(){return["Да"!=t.document.posted?e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.size,callback:function(e){t.$set(s.item,"size",e)},expression:"props.item.size"}}):t._e()]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.size)+" ")])]}},{key:"no-results",fn:function(){return[e(h.A,[t._v(" В наличии не найдено "),e(_.A,{attrs:{"max-width":"900px"},scopedSlots:t._u([{key:"activator",fn:function({on:s,attrs:o}){return[e(a.A,t._g(t._b({attrs:{color:"primary",dark:""}},"v-btn",o,!1),s),[t._v(" Добавить в документ ")])]}}],null,!1,3306325978),model:{value:t.productSelectDialog,callback:function(e){t.productSelectDialog=e},expression:"productSelectDialog"}},[e(h.A,[e(m.OQ,[e(O.A,{staticClass:"elevation-1",attrs:{items:t.foundProducts,headers:t.addProductHeaders,dense:"","items-per-page":15},on:{"dblclick:row":t.addProductToDocument},scopedSlots:t._u([{key:"item.small_pic",fn:function({item:t}){return[e(V.A,{attrs:{src:t.small_pic}})]}}],null,!1,769197825)})],1),e(m.SL,[e(g.A),e(a.A,{attrs:{color:"blue darken-1",text:""},on:{click:function(e){t.productSelectDialog=!1}}},[t._v(" Закрыть ")])],1)],1)],1)],1)]},proxy:!0},{key:"item.del",fn:function({item:s}){return["Да"!=t.document.posted?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.deleteItem(s)}}},[t._v(" mdi-delete ")]):t._e(),"Да"!=t.document.posted?e(M.A,{staticClass:"mr-2",attrs:{small:""},on:{click:function(e){return t.copyItem(s)}}},[t._v(" mdi-content-copy ")]):t._e()]}},{key:"item.q",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.q},on:{save:function(e){return t.saveQ(s.item)}},scopedSlots:t._u([{key:"input",fn:function(){return[e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.q,callback:function(e){t.$set(s.item,"q",e)},expression:"props.item.q"}})]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.q)+" ")])]}}],null,!1,829627014)})],1)],1),e(E.A,[e(j.A,{staticClass:"text-right"},[e("download-excel",{attrs:{data:t.documentLines}},[t._v(" Скачать Excel ")])],1),e(j.A,{staticClass:"text-right"},["sale"==t.document.doc_type?e(a.A,{staticClass:"mr-3",attrs:{color:"normal"},on:{click:t.sendToCashier}},[t._v(" Отправить в кассу "),e(M.A,{attrs:{right:""}},[t._v(" mdi-close ")])],1):t._e()],1),e(j.A,{staticClass:"text-right"},[e(a.A,{staticClass:"mr-3",attrs:{color:"normal"},on:{click:t.cancel}},[t._v(" Закрыть "),e(M.A,{attrs:{right:""}},[t._v(" mdi-close ")])],1),e(a.A,{attrs:{color:"primary",loading:t.saving},on:{click:t.saveDocument}},[t._v(" Сохранить "),e(M.A,{attrs:{right:""}},[t._v(" mdi-content-save ")])],1)],1)],1)],1):t._e(),e(Dt.A,{attrs:{timeout:4e3},scopedSlots:t._u([{key:"action",fn:function({attrs:s}){return[e(a.A,t._b({attrs:{color:"blue",text:""},on:{click:function(e){t.snackbar=!1}}},"v-btn",s,!1),[t._v(" Закрыть ")])]}}]),model:{value:t.notifySnackBar,callback:function(e){t.notifySnackBar=e},expression:"notifySnackBar"}},[t._v(" "+t._s(t.notifyText)+" ")]),e("Confirm",{ref:"confirm"})],1)},wt=[],$t={data(){return{documentChanged:!1,showControls:!1,notifySnackBar:!1,notifyText:"",document:null,documentLines:null,stock:[],loading:!1,saving:!1,docId:null,recalc:!1,search:null,uploadFile:null,productSelectDialog:null,foundProducts:[],addSearch:null,searchInProgress:!1,productSearch:null,searchItems:[],srcPrice:[{text:"Розничная цена",value:"retail_price"},{text:"Цена ПП 100сп",value:"sp_price"},{text:"Закупочная цена",value:"buy_price"}],destPrice:[{text:"Розничная цена",value:"retail_price"},{text:"Цена ПП 100сп",value:"sp_price"}],srcPriceValue:null,destPriceValue:null,recalcPercent:1,purchaseList:null,selectedPurchase:null,inventoryDocumentHeaders:null,transitData:null,addProductHeaders:[{text:"Картинка",value:"small_pic"},{text:"ID",value:"id"},{text:"Артикул",value:"sku"},{text:"Название",value:"name"},{text:"Штрихкод",value:"barcode"}],inventoryDocumentHeadersBasic:[{text:"id",align:"start",sortable:!1,value:"id"},{text:"Картинка",align:"start",sortable:!1,value:"pic"},{text:"Артикул",align:"start",sortable:!1,value:"sku"},{text:"Арт. пост.",align:"start",sortable:!1,value:"supplier_sku"},{text:"Название",align:"start",sortable:!1,value:"name"},{text:"Штрихкод",align:"start",sortable:!1,value:"barcode"},{text:"Размер",align:"start",sortable:!1,value:"size",filterable:!1},{text:"На складе",align:"start",sortable:!1,value:"in_stock",filterable:!1},{text:"Количество",align:"start",sortable:!1,value:"q",filterable:!1},{text:"Ожидают",align:"start",sortable:!1,value:"transit_info",filterable:!1},{text:"Цена закуп",align:"start",sortable:!1,filterable:!1,value:"buy_price"},{text:"Цена ПП",align:"start",sortable:!1,value:"sp_price",filterable:!1},{text:"Цена розничная",align:"start",sortable:!1,value:"retail_price",filterable:!1},{text:"РРЦ",align:"start",sortable:!1,value:"rrp",filterable:!1},{text:"Команды",value:"del",sortable:!1}],inventoryDocumentHeadersAdjust:[{text:"Картинка",align:"start",sortable:!1,value:"pic"},{text:"Артикул",align:"start",sortable:!1,value:"sku"},{text:"Название",align:"start",sortable:!1,value:"name"},{text:"Штрихкод",align:"start",sortable:!1,value:"barcode"},{text:"Размер",align:"start",sortable:!1,value:"size",filterable:!1},{text:"Числится",align:"start",sortable:!1,value:"old_amount",filterable:!1},{text:"В наличии",align:"start",sortable:!1,filterable:!1,value:"new_amount"},{text:"Удалить",value:"del",sortable:!1,filterable:!1},{text:"Проверено",value:"checked",sortable:!0}]}},created(){this.loadDocument(),this.debouncedSearch=(0,nt.debounce)(this.searchAndAdd,500)},methods:{onLineCheck(t){J.updateInventoryLine(t.id,t.new_amount,t.checked),console.log(t)},loadDocument(){this.loading=!0,this.docId=this.$route.params.id,J.getTransit().then((t=>{this.transitData=t.data.data})),J.getInventoryDocument(this.docId).then((t=>{this.document=t.data.document,this.inventoryDocumentHeaders="adjust"==this.document.doc_type?this.inventoryDocumentHeadersAdjust:this.inventoryDocumentHeadersBasic,this.documentLines=t.data.lines.map(this.getDisplayInventoryDocumentLine),this.$nextTick((()=>{console.log("Loading false"),this.loading=!1,console.log("Changed: false"),this.documentChanged=!1})),console.log(this.documentLines)})).catch((t=>{this.loading=!1,console.log(t)})),null==this.purchaseList&&J.getAllSpPp(0,100).then((t=>{this.purchaseList=t.data.purchases.map((t=>({text:t.name,value:t.id})))}))},countAmount(t){return this.documentLines.filter((e=>e.inventory_product?.supplier_sku==t)).reduce(((t,e)=>t+1*e.q),0)},countAllocatedTransitAmount(t){return this.documentLines.filter((e=>e.inventory_product?.supplier_sku==t)).reduce(((t,e)=>t+e.transit_info?.reduce(((t,e)=>t+1*e.amount),0)),0)},countTotalTransitAmount(t){return this.transitSkus.filter((e=>e.art==t)).reduce(((t,e)=>t+1*e.amount),0)},getDisplayInventoryDocumentLine(t,e){return null==e&&(e=this.documentLines.length),{id:t.id,vId:e,sku:t.inventory_product?t.inventory_product.sku:"НЕ НАЙДЕН",supplier_sku:t.inventory_product?t.inventory_product.supplier_sku:"НЕ НАЙДЕН",name:t.inventory_product?t.inventory_product.name:t.name,size:t.size,in_stock:t.stock?t.stock.q:0,stock:t.stock,q:`${"absolute"==t.line_type?"=":t.amount_change>0?"+":""}${t.amount_change}`,retail_price:t.retail_price,sp_price:t.sp_price,buy_price:t.buy_price,rrp:t.rrp,old_amount:t.old_amount,new_amount:t.new_amount,barcode:t.barcode,checked:t.checked,small_pic:t.small_pic,transit_info:t.transit_info,pic:t.pic,product_id:t.product?t.product.id:null,inventory_product:t.inventory_product,inventory_product_id:t.inventory_product?t.inventory_product.id:null,inventory_document_id:t.inventory_document_id,line_type:t.line_type}},calcLinePrices(t){return"receipt"!=this.document.doc_type||(Math.ceil(1.55*t.buy_price)>t.sp_price&&(t.sp_price=Math.ceil(1.55*t.buy_price)),Math.ceil(1.59*t.buy_price)>t.retail_price&&(t.retail_price=Math.ceil(1.59*t.buy_price))),t},recalcLocal(t){let e=0;return"buy_price"==this.srcPriceValue?e=Math.ceil(t.buy_price*this.recalcPercent):"sp_price"==this.srcPriceValue?e=Math.ceil(t.sp_price*this.recalcPercent):"retail_price"==this.srcPriceValue&&(e=Math.ceil(t.retail_price*this.recalcPercent)),"sp_price"==this.destPriceValue?t.sp_price=e:"retail_price"==this.destPriceValue&&(t.retail_price=e),t},itemRowBackground(t){const e=this.countAmount(t.supplier_sku),s=this.countTotalTransitAmount(t.supplier_sku)-this.countAllocatedTransitAmount(t.supplier_sku),o=e+this.countAllocatedTransitAmount(t.supplier_sku);return t.checked?"green lighten-4":e>=-this.countAllocatedTransitAmount(t.supplier_sku)&&o>=s?"":"red lighten-4"},getFiltered(){},openProduct(t,e){"adjust"!=this.document.doc_type&&this.documentChanged&&this.saveDocument(),this.$router.push({path:`/product/${e.item.inventory_product_id}`})},fillAdjust(){this.loading=!0,"adjust"==this.document.doc_type?J.fillAdjust(this.docId,this.selectedPurchase).then((()=>{this.loading=!1,this.loadDocument()})):J.fillRevalue(this.docId,this.selectedPurchase).then((()=>{this.loading=!1,this.loadDocument()}))},doRecalc(){this.documentLines=this.documentLines.map(this.recalcLocal),this.recalc=!0},saveRecalc(){0!=this.recalc&&(this.loading=!0,J.recalcInventoryDocument(this.docId,this.srcPriceValue,this.destPriceValue,this.recalcPercent).then((()=>{this.loading=!1,this.loadDocument()})))},saveAdjust(){this.loading=!0,J.saveAdjust(this.docId,this.documentLines.map(this.adjustLines)).then((()=>{this.loading=!1,this.loadDocument()}))},adjustLines(t){return{id:t.id,new_amount:t.new_amount,checked:t.checked}},validatePricesNotNull(t){return t.buy_price<=0||t.sp_price<=0||t.retail_price<=0},validateReceipt(){if("receipt"==this.document.doc_type){const t=this.documentLines.filter(this.validatePricesNotNull);if(t.length>0){const e=t.map((t=>t.sku)).join("\n");return this.$store.dispatch("showModalAction",{text:"Найдены строки с нулевой ценой",trace:e,title:"Ошибка"}),!1}}return!0},saveReceipt(){this.validateReceipt()&&(this.loading=!0,J.saveInventoryDocument(this.docId,this.documentLines).then((t=>{this.loading=!1,"ok"==t.data.result&&(this.documentLines=t.data.lines.map(this.getDisplayInventoryDocumentLine)),this.$nextTick((()=>{console.log("Changed: false; after load"),this.documentChanged=!1}))})))},saveDocument(){console.log("save"),"adjust"==this.document.doc_type?this.saveAdjust():this.saveReceipt(),this.documentChanged=!1,console.log("Changed: false")},fileChanged(t){this.uploadFile=t},fillDocumentWithFile(){this.loading=!0;let t=new FormData;t.append("file",this.uploadFile),J.fillDocumentWithFile(this.docId,t).then((()=>{this.loading=!1,this.loadDocument()}))},searchProductOnServer(){J.searchProductOnServer(this.search).then((t=>{console.log(t.data.products),this.foundProducts=t.data.products}))},addProductToDocument(t,e){console.log(e),this.productSelectDialog=!1,J.createInventoryDocumentLine(this.docId,e.item).then((t=>{this.documentLines.push(this.getDisplayInventoryDocumentLine(t.data.line)),console.log(this.documentLines)}))},save(){},cancel(){this.documentChanged?this.$refs.confirm.open("Подтверждение","Документ был изменен, закрыть без сохранения?",{color:"red"}).then((t=>{t&&this.$router.go(-1)})):this.$router.go(-1)},addProductItemToDocument(t){if(null!=t){if(t.stock&&t.stock.length>0)t.stock.forEach((e=>{console.log(e);let s=1;"revalue"==this.document.doc_type&&(s=0),"writeoff"!=this.document.doc_type&&"sale"!=this.document.doc_type||(s=-1),this.documentLines.push(this.calcLinePrices({id:0,inventory_product_id:t.id,pic:t.pic,small_pic:t.small_pic,sku:t.sku,inventory_product:t,in_stock:e.q,size:e.size,q:s,transit_info:t.transit_info,retail_price:e.retail_price,sp_price:e.sp_price,buy_price:e.buy_price,barcode:t.barcode,name:t.name,line_type:"relative",vId:this.documentLines.length}))}));else{let e=1;"revalue"==this.document.doc_type&&(e=0),"writeoff"!=this.document.doc_type&&"sale"!=this.document.doc_type||(e=-1),this.documentLines.push(this.calcLinePrices({id:0,inventory_product_id:t.id,pic:t.pic,small_pic:t.small_pic,sku:t.sku,inventory_product:t,in_stock:0,size:"-",q:e,retail_price:0,sp_price:0,buy_price:0,barcode:"",name:t.name,line_type:"relative",vId:this.documentLines.length}))}this.$nextTick((()=>this.$vuetify.goTo(1e6,{container:".v-data-table__wrapper"}))),setTimeout((()=>this.$vuetify.goTo(1e6,{container:".v-data-table__wrapper"})),500),this.notifyText="Добавлена строка",this.notifySnackBar=!0}},searchAndAdd(){if(null==this.addSearch||""==this.addSearch.trim())return;const t=this.documentLines.find((t=>t.barcode==this.addSearch||t.sku==this.addSearch));if(t)return t.q++,this.notifyText="Добавлено к товару",this.notifySnackBar=!0,void(this.addSearch="");this.searchInProgress=!0,J.searchProductOnServer(this.addSearch).then((t=>{if(console.log(t.data.products),1==t.data.products.length){this.addSearch="",console.log(t.data.products);const e=t.data.products[0];this.addProductItemToDocument(e),this.searchItems=[]}else t.data.products.length>1?this.searchItems=t.data.products.map((t=>({value:t,text:`${t.sku} ${t.name} (${t.barcode})`}))):(this.notifyText="Товар не найден",this.notifySnackBar=!0)})).finally((()=>{this.searchInProgress=!1}))},deleteItem(t){console.log(t);const e=this.documentLines.indexOf(t);this.documentLines.splice(e,1)},copyItem(t){console.log(t);const e=this.documentLines.indexOf(t),s=Object.assign({},t);s.size="Новый размер",s.in_stock=0,s.id=0,this.documentLines.splice(e+1,0,s)},saveQ(t){"writeoff"!=this.document.doc_type&&"sale"!=this.document.doc_type||1*t.q>0&&(t.q=1*-t.q)},saveSize(t){t.size=t.size.trim()},sendToCashier(){J.sendDocToCashier(this.document.id)},deleteTransitData:function(t){console.log(t),t.transit_info=[]},deleteTransitDataOne:function(t,e){console.log(t),t.transit_info=t.transit_info.filter((t=>t.id!=e))}},watch:{$route:"loadDocument",documentLines:{handler:function(){this.loading||(this.documentChanged=!0,console.log("Changed: true"))},deep:!0},productSelectDialog(t){t&&this.searchProductOnServer()},addSearch:function(){this.debouncedSearch()},productSearch:function(t){null!=t&&(console.log(t),this.addProductItemToDocument(t),this.$nextTick((()=>{this.productSearch=null,this.addSearch=null})))}},computed:{transitSkus:function(){let t=[];return t=this.transitData.filter((t=>this.skusInDocument.includes(t.art))),t},skusInDocument:function(){return this.documentLines.map((t=>t.supplier_sku))}}},St=$t,It=(0,b.A)(St,Ct,wt,!1,null,null,null),Lt=It.exports,jt=s(8797),Ot=function(){var t=this,e=t._self._c;return e("div",[e(E.A,[e(j.A,[e(a.A,{attrs:{dense:"",color:"primary"},on:{click:t.upload}},[t._v("Загрузить остатки на 100сп")])],1)],1),e(O.A,{staticClass:"elevation-1",attrs:{headers:t.headers,items:t.purchases,"items-per-page":15,page:t.currentPage,"server-items-length":t.totalPurchases,options:t.options,dense:""},on:{"update:options":function(e){t.options=e},"dblclick:row":t.openPurchase},scopedSlots:t._u([{key:"item.discount",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.discount},on:{"update:returnValue":function(e){return t.$set(s.item,"discount",e)},"update:return-value":function(e){return t.$set(s.item,"discount",e)},save:function(e){return t.saveDiscount(s.item)}},scopedSlots:t._u([{key:"input",fn:function(){return[e(B.A,{attrs:{label:"Edit","single-line":""},model:{value:s.item.discount,callback:function(e){t.$set(s.item,"discount",e)},expression:"props.item.discount"}})]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.discount)+" ")])]}},{key:"item.discount_until",fn:function(s){return[e(T.A,{attrs:{"return-value":s.item.discount_until},on:{"update:returnValue":function(e){return t.$set(s.item,"discount_until",e)},"update:return-value":function(e){return t.$set(s.item,"discount_until",e)},save:function(e){return t.saveDiscount(s.item)}},scopedSlots:t._u([{key:"input",fn:function(){return[e(jt.A,{staticClass:"mt-4",attrs:{"first-day-of-week":"1"},model:{value:s.item.discount_until,callback:function(e){t.$set(s.item,"discount_until",e)},expression:"props.item.discount_until"}})]},proxy:!0}],null,!0)},[t._v(" "+t._s(s.item.discount_until)+" ")])]}}])}),e(E.A,[e(j.A,[e("div",t._l(t.uploadMessages,(function(s,o){return e("p",{key:s+o},[t._v(" "+t._s(s)+" ")])})),0)])],1),e(Dt.A,{attrs:{timeout:4e3},scopedSlots:t._u([{key:"action",fn:function({attrs:s}){return[e(a.A,t._b({attrs:{color:"blue",text:""},on:{click:function(e){t.snackbar=!1}}},"v-btn",s,!1),[t._v(" Закрыть ")])]}}]),model:{value:t.notifySnackBar,callback:function(e){t.notifySnackBar=e},expression:"notifySnackBar"}},[t._v(" "+t._s(t.notifyText)+" ")])],1)},Tt=[],Ft={data(){return{headers:[{text:"ID",value:"id",sortable:!0},{text:"SP Pid",align:"start",sortable:!0,value:"sp_pid"},{text:"Название",value:"name"},{text:"Скидка %",value:"discount"},{text:"Дата оконачния скидки",value:"discount_until"}],purchases:[],totalPurchases:0,currentPage:1,options:{},loading:!0,notifyText:"",notifySnackBar:!1,uploadMessages:[]}},watch:{options:{handler(){this.loadPurchaseList()}},deep:!0},channels:{WebNotificationsChannel:{connected(){console.log("Connected")},rejected(){},received(t){t.body&&(this.uploadMessages.push(t.body),console.log(t))},disconnected(){}}},methods:{openPurchase(t,e){console.log(e),this.$router.push({path:`/pp/${e.item.id}`})},loadPurchaseList(){this.loading=!0;const{page:t,itemsPerPage:e}=this.options;let s=t-1;J.getAllSpPp(s,e).then((t=>{this.loading=!1,this.purchases=t.data.purchases.map(this.getDisplayPurchase),this.totalPurchases=t.data.count,console.log(t.data)})).catch((t=>{this.loading=!1,console.log(t)}))},refreshList(){this.loadPurchaseList()},getDisplayPurchase(t){return{id:t.id,sp_pid:t.sp_pid,name:t.name,discount:t.discount,discount_until:t.discount_until}},upload(){this.uploadMessages=["Импорт начат"],J.upload_stock().then((t=>{"ok"!=t.data.result&&this.$store.dispatch("showModalAction",{text:"Есть непроведенные документы",title:"Ошибка"})}))},saveDiscount(t){console.log(t),J.updateDiscountData(t.id,t.discount,t.discount_until)}},mounted(){this.$cable.subscribe({channel:"WebNotificationsChannel",room:"public"}),this.loadPurchaseList()}},zt=Ft,Mt=(0,b.A)(zt,Ot,Tt,!1,null,null,null),Vt=Mt.exports,qt=function(){var t=this,e=t._self._c;return e(c.A,[e(E.A,[e(j.A,[e(a.A,{attrs:{href:t.exportDlLink}},[t._v("Загрузить CSV для импорта на 100сп")])],1)],1),e(E.A,[e(j.A,[e(O.A,{staticClass:"elevation-1",attrs:{headers:t.headers,"disable-pagination":"",items:t.collections,"server-items-length":t.totalCollections,options:t.options,dense:""},on:{"update:options":function(e){t.options=e},"dblclick:row":t.openCollection}})],1)],1)],1)},Et=[],Bt={data(){return{headers:[{text:"ID",value:"id",sortable:!0},{text:"Название",value:"name"}],collections:[],totalCollections:0,options:{},loading:!0,purchaseId:null,exportDlLink:null}},watch:{options:{handler(){this.loadCollectionList()}},deep:!0},methods:{openCollection(t,e){console.log(e),this.$router.push({path:`/collection/${e.item.id}`})},loadCollectionList(){this.loading=!0,this.purchaseId=this.$route.params.id,this.exportDlLink=`http://spup.primavon.ru/purchases/${this.purchaseId}/getcsv?use_stock=true`,J.getCollections(this.purchaseId).then((t=>{this.loading=!1,this.collections=t.data.collections.map(this.getDisplayCollection),this.totalCollections=t.data.count,console.log(t.data)})).catch((t=>{this.loading=!1,console.log(t)}))},refreshList(){this.loadCollectionList()},getDisplayCollection(t){return{id:t.id,name:t.name}}},mounted(){this.loadCollectionList()}},Ut=Bt,Rt=(0,b.A)(Ut,qt,Et,!1,null,null,null),Ht=Rt.exports,Nt=function(){var t=this,e=t._self._c;return e("div",[e(E.A,[e(j.A)],1),e(O.A,{staticClass:"elevation-1",attrs:{headers:t.headers,items:t.purchases,"items-per-page":500,dense:"","footer-props":{showFirstLastPage:!0,itemsPerPageOptions:[50,100,500,-1]}},on:{"dblclick:row":t.openPurchase}})],1)},Qt=[],Wt={data(){return{headers:[{text:"Название",align:"start",sortable:!1,value:"name"}],purchases:[],loading:!0,search:null}},watch:{},methods:{openPurchase(t,e){console.log(e),this.$router.push({path:`/purchase/${e.item.id}`})},loadPurchaseList(){this.loading=!0,J.getAllPurchases().then((t=>{this.loading=!1,this.purchases=t.data.purchases.map(this.getDisplayPurchase),console.log(t.data)})).catch((t=>{this.loading=!1,console.log(t)}))},refreshList(){this.loadPurchaseList()},getDisplayPurchase(t){return{id:t.id,name:t.name}}},created(){},mounted(){this.loadPurchaseList()}},Zt=Wt,Gt=(0,b.A)(Zt,Nt,Qt,!1,null,null,null),Jt=Gt.exports,Kt=s(7264),Xt=s(1879),Yt=s(9837),te=s(3882),ee=s(4163),se=s(2822),oe=s(8461),ne=s(4519),re=s(9366),ie=s(5443),ae=s(4624),ce=s(8838),le=s(5568),ue=function(){var t=this,e=t._self._c;return t.purchase?e("div",[e(E.A,[e(j.A,[e("h1",[t._v(t._s(t.purchase.name))]),e("p",[t._v(t._s(t.purchase.message))]),e("p",[t._v("Дата загрузки данных: "+t._s(t.purchase.last_product_modify))]),t._v(" "+t._s(t.selectedCollections)+" "+t._s(t.selectedProducts)+" ")])],1),e(ce.A,[e(ie.A,{attrs:{href:"#download"}},[t._v(" Загрузка из источника ")]),e(ae.A,{attrs:{value:"download"}},[e(h.A,{staticClass:"pa-4",attrs:{flat:""}},[e(a.A,[t._v("Загрузка из источника")])],1)],1),e(ie.A,{attrs:{href:"#upload"}},[t._v(" Загрузка на 100сп ")]),e(ae.A,{attrs:{value:"upload"}}),e(ie.A,{attrs:{href:"#sendorder"}},[t._v(" Отправка заказа ")]),e(ae.A,{attrs:{value:"sendorder"}}),e(ie.A,{attrs:{href:"#invoice"}},[t._v(" Работа со счетом ")]),e(ae.A,{attrs:{value:"invoice"}}),e(ie.A,{attrs:{href:"#purchase"}},[t._v(" Работа с закупкой ")]),e(ae.A,{attrs:{value:"purchase"}})],1),e(re.A,{attrs:{accordion:""}},t._l(t.collections,(function(s,o){return e(se.A,{key:o},[e(ne.A,[e(E.A,[e(j.A,{attrs:{cols:"1"}},[e(lt.A,{attrs:{value:s.id},on:{click:function(e){return t.collectionClick(e,s)},change:function(e){return t.collectionCheckboxUpdated(s)}},model:{value:t.selectedCollections,callback:function(e){t.selectedCollections=e},expression:"selectedCollections"}})],1),e(j.A,{attrs:{cols:"1"}},[e(Xt.A,{attrs:{height:"100","hide-delimiters":"","show-arrows-on-hover":""}},t._l(s.small_pics,(function(t,s){return e(Yt.A,{key:s,attrs:{src:t,contain:"","max-height":"100px","max-width":"100px"}})})),1)],1),e(j.A,{attrs:{cols:"2"}},[t._v(" "+t._s(s.name)+" ")]),e(j.A,{attrs:{cols:"1"}},[t._v(" "+t._s(s.grp)+" ")]),e(j.A,{attrs:{cols:"1"}},[t._v(" "+t._s(s.product_count)+" ")]),e(j.A,{attrs:{cols:"1"}},[t._v(" "+t._s(s.cat_id)+" "+t._s(s.cat_name)+" ")]),e(j.A,{attrs:{cols:"2"}},[e(ee.A,{attrs:{"active-class":"primary--text",column:""}},t._l(s.tags2,(function(s){return e(te.A,{key:s,staticClass:"ma-2",attrs:{close:""}},[t._v(" "+t._s(s)+" ")])})),1)],1)],1)],1),e(oe.A,[e(E.A,{staticClass:"grey lighten-1 font-weight-bold",attrs:{dense:""}},[e(j.A,{attrs:{cols:"1"}}),e(j.A,{attrs:{cols:"3"}},[t._v("Название")]),e(j.A,{attrs:{cols:"1"}},[t._v("Цена (РРЦ)")]),e(j.A,{attrs:{cols:"4"}},[t._v("Категория")])],1),e(le.A,{attrs:{items:s.products,"item-height":70,height:"600"},scopedSlots:t._u([{key:"default",fn:function({item:s,index:o}){return[e(E.A,{class:"grey lighten-"+(4+o%2),attrs:{dense:""}},[e(j.A,{attrs:{cols:"1"}},[e(lt.A,{attrs:{value:s.id},model:{value:t.selectedProducts,callback:function(e){t.selectedProducts=e},expression:"selectedProducts"}})],1),e(j.A,{class:s.disabled?"text-decoration-line-through":"",attrs:{cols:"3"}},[t._v(t._s(s.name))]),e(j.A,{attrs:{cols:"1"}},[t._v(t._s(s.price)+" ("+t._s(s.rrp)+")")]),e(j.A,{attrs:{cols:"4"}},[t._v(t._s(s.category)+" "+t._s(t.spCategories[s.category].path))])],1)]}}],null,!0)})],1)],1)})),1),e(Kt.A,{attrs:{color:"primary",fixed:""}},[e(a.A,[e("span",[t._v("Recents")]),e(M.A,[t._v("mdi-history")])],1),e(a.A,[e("span",[t._v("Favorites")]),e(M.A,[t._v("mdi-heart")])],1),e(a.A,[e("span",[t._v("Nearby")]),e(M.A,[t._v("mdi-map-marker")])],1)],1)],1):t._e()},de=[],pe={data(){return{selectedCollections:[],selectedProducts:[],expanded:[],headers:[{text:"Select",align:"left",value:"data-table-select",class:"checkbox",cellClass:"checkbox"},{text:"Название",align:"start",sortable:!1,value:"name"},{text:"Товары",align:"start",sortable:!1,value:"product_count"}],headersChild:[{text:"Название",align:"left",value:"name"}],purchase:null,collections:[],loading:!0,search:null,spCategories:null}},watch:{selectedParent(t){this.selectedChild=[],t.length>0&&t.forEach((t=>{t.products.forEach((t=>{this.selectedChild.push(t)}))}))}},methods:{loadPurchase(){this.loading=!0;const t=this.$route.params.id;J.getPurchase(t).then((t=>{this.loading=!1,this.purchase=t.data.purchase,this.collections=t.data.purchase.collections.map(this.getDisplayCollection)})).catch((t=>{this.loading=!1,console.log(t)}))},refreshList(){this.loadPurchase()},getDisplayCollection(t){return{id:t.id,name:t.name,grp:t.grp,cat_id:1*t.coltype,cat_name:this.spCategories[1*t.coltype].path,tags2:t.tags2,products:t.products,small_pics:t.small_pics,product_count:`${t.products.reduce((function(t,e){return t+(!1===e.disabled)}),0)} (${t.products.length})`}},collectionClick(t){t.cancelBubble=!0},collectionCheckboxUpdated(t){if(this.selectedCollections.includes(t.id))for(let e=0;e<t.products.length;e++)this.selectedProducts.includes(t.products[e].id)||this.selectedProducts.push(t.products[e].id);else{const e=t.products.map((t=>t.id));this.selectedProducts=this.selectedProducts.filter((t=>!e.includes(t)))}},async loadSpCategories(){if(null==this.spCategories&&(this.spCategories=this.$store.getters.getSpCategories,null==this.spCategories)){let t=await J.getAllCategories();this.spCategories={},t.data.categories.forEach((t=>this.spCategories[t.id]=t)),this.$store.dispatch("setSpCategories",this.spCategories)}}},created(){},mounted(){this.loadSpCategories(),this.loadPurchase()}},he=pe,me=(0,b.A)(he,ue,de,!1,null,null,null),_e=me.exports,ve=function(){var t=this,e=t._self._c;return e("div",[t._v(" Login "),e(z.A,[e(c.A,[e(B.A,{attrs:{label:"Email",required:""},model:{value:t.login,callback:function(e){t.login=e},expression:"login"}}),e(B.A,{attrs:{type:"password",label:"Пароль"},model:{value:t.password,callback:function(e){t.password=e},expression:"password"}}),e("div",[t._v(t._s(t.error))]),e(a.A,{attrs:{primary:""},on:{click:function(e){return e.stopPropagation(),t.doLogin.apply(null,arguments)}}},[t._v("Войти")])],1)],1)],1)},ge=[],fe={data(){return{login:"",password:"",error:""}},watch:{},methods:{async doLogin(){console.log(this.login),console.log(this.password);try{let t=await J.login(this.login,this.password);console.log(t.data),t.data.data.id?(localStorage.Auth_token=t.headers.authorization,this.$router.push({path:"/inventory_documents"})):this.error=t.data}catch(t){this.error=t}}},created(){},mounted(){}},ye=fe,Ae=(0,b.A)(ye,ve,ge,!1,null,null,null),ke=Ae.exports,be=s(3613),xe=s.n(be),Pe=s(7043),De=function(){var t=this,e=t._self._c;return e(_.A,{style:{zIndex:t.options.zIndex},attrs:{"max-width":t.options.width},on:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"esc",27,e.key,["Esc","Escape"])?null:t.cancel.apply(null,arguments)}},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[e(h.A,[e(Pe.A,{attrs:{color:t.options.color,dark:"",dense:"",flat:""}},[e(u.sw,{staticClass:"white--text"},[t._v(t._s(t.title))])],1),e(m.OQ,{directives:[{name:"show",rawName:"v-show",value:!!t.message,expression:"!!message"}],staticClass:"pa-4"},[t._v(t._s(t.message))]),e(m.SL,{staticClass:"pt-0"},[e(g.A),e(a.A,{attrs:{color:"primary darken-1",text:""},nativeOn:{click:function(e){return t.agree.apply(null,arguments)}}},[t._v("Да")]),e(a.A,{attrs:{color:"grey",text:""},nativeOn:{click:function(e){return t.cancel.apply(null,arguments)}}},[t._v("Нет")])],1)],1)],1)},Ce=[],we={data:()=>({dialog:!1,resolve:null,reject:null,message:null,title:null,options:{color:"primary",width:290,zIndex:200}}),computed:{show:{get(){return this.dialog},set(t){this.dialog=t,!1===t&&this.cancel()}}},methods:{open(t,e,s){return this.dialog=!0,this.title=t,this.message=e,this.options=Object.assign(this.options,s),new Promise(((t,e)=>{this.resolve=t,this.reject=e}))},agree(){this.resolve(!0),this.dialog=!1},cancel(){this.resolve(!1),this.dialog=!1}}},$e=we,Se=(0,b.A)($e,De,Ce,!1,null,null,null),Ie=Se.exports,Le=s(1706);o.Ay.component("Confirm",Ie),o.Ay.component("downloadExcel",Le.A),o.Ay.use(n.A),o.Ay.use(L.Ay),o.Ay.use(Q.Ay),o.Ay.use(xe(),{debug:!0,debugLevel:"error",connectionUrl:"wss://spup.primavon.ru/cable?jwt="+localStorage.getItem("Auth_token"),connectImmediately:!0}),o.Ay.config.productionTip=!1;const je=new L.Ay({routes:[{path:"/purchases",component:Jt},{path:"/purchase/:id",component:_e},{path:"/products",component:ct},{path:"/login",component:ke,name:"Login"},{path:"/products/page/:page/perpage/:perpage",component:ct},{path:"/product/:id",component:tt},{path:"/inventory_documents",component:xt},{path:"/inventory_document/:id",component:Lt},{path:"/pp",component:Vt},{path:"/pp/:id",component:Ht},{path:"/collection/:id",component:ct}]});je.beforeEach(((t,e,s)=>{"Login"===t.name||localStorage.Auth_token?s():s({name:"Login"})})),new o.Ay({vuetify:I,router:je,store:W,render:t=>t($)}).$mount("#app")}},e={};function s(o){var n=e[o];if(void 0!==n)return n.exports;var r=e[o]={id:o,loaded:!1,exports:{}};return t[o].call(r.exports,r,r.exports,s),r.loaded=!0,r.exports}s.m=t,function(){var t=[];s.O=function(e,o,n,r){if(!o){var i=1/0;for(u=0;u<t.length;u++){o=t[u][0],n=t[u][1],r=t[u][2];for(var a=!0,c=0;c<o.length;c++)(!1&r||i>=r)&&Object.keys(s.O).every((function(t){return s.O[t](o[c])}))?o.splice(c--,1):(a=!1,r<i&&(i=r));if(a){t.splice(u--,1);var l=n();void 0!==l&&(e=l)}}return e}r=r||0;for(var u=t.length;u>0&&t[u-1][2]>r;u--)t[u]=t[u-1];t[u]=[o,n,r]}}(),function(){s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,{a:e}),e}}(),function(){s.d=function(t,e){for(var o in e)s.o(e,o)&&!s.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){s.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t}}(),function(){var t={524:0};s.O.j=function(e){return 0===t[e]};var e=function(e,o){var n,r,i=o[0],a=o[1],c=o[2],l=0;if(i.some((function(e){return 0!==t[e]}))){for(n in a)s.o(a,n)&&(s.m[n]=a[n]);if(c)var u=c(s)}for(e&&e(o);l<i.length;l++)r=i[l],s.o(t,r)&&t[r]&&t[r][0](),t[r]=0;return s.O(u)},o=self["webpackChunkinventory"]=self["webpackChunkinventory"]||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))}();var o=s.O(void 0,[504],(function(){return s(9899)}));o=s.O(o)})();
//# sourceMappingURL=app.1bd8d285.js.map