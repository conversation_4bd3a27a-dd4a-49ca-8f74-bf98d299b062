class SpApi
  include SpAuthorize

  attr_reader :agent

  API_KEY = '19d47a1175b3518be54cdb7a0a34592dc79dc663069c42d58314cc21374f752e'

  def initialize
    @agent = Mechanize.new
    authorize
    Rails.logger.info "SpApi.new"
    @ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*' }
    super
  end

  def rename_collection(cid, new_name)
    d = { cid: cid, type: 'name', data: new_name }
    p = @agent.post 'https://www.100sp.ru/org/collection/updateAttribute', d, @ajax_headers
    JSON.parse(p.body)
  end
  def mark_not_delivered(meagorder_id, oids)

    data = [['mark_as_not_delivered', 'Недопоставка'], ['order_undelivered_comment', 'Недопоставка от поставщика']]
    oids.each { |oid| data << ["orderIds[#{oid}]", 1] }
    @agent.post("https://www.100sp.ru/org/megaorder/#{meagorder_id}", data)
  end

  def load_megaorder(megaorder_id)
    page = @agent.get "https://www.100sp.ru/org/megaorder/#{megaorder_id}"
    ret = {}
    page.search('.sub-order').each do |suborder|
      statuses  = suborder.search('.order-status-text').map { |s| s.text.strip }
      order_id = suborder.at('label').attr('for').gsub('cb:','')
      ret[order_id] = {confirm: statuses[1], process: statuses[2]}
    end
    ret
  end

  def confirm_orders(order_ids, pid, comment: nil)
    log = Logger.new('log/sp-confirm-orders.txt')
    log.info pid
    log.info comment
    log.info order_ids

    return if Rails.env.development?

    if comment
      order_ids.each do |order_id|
        add_order_comment(comment, order_id)
      end
    end

    data = order_ids.map { |id| ["cb_order[#{id}]", 6] }
    data << ['pid', pid]
    data << ['returnUrl', "/org/purchase/processOrders/confirmOrders?pid=#{pid}"]
    data << ['articul', '']
    data << ['sourceFilter', '']

    res = @agent.post 'https://www.100sp.ru/org/purchase/processOrders/confirmed', data, @ajax_headers
    puts res.body
    res.body
  end

  def process_sp_col(cid, group_name)
    page = @agent.get("https://www.100sp.ru/org/collection/edit/#{cid}")

    real_col_name = page.search('#collection-edit .editForm  h3:not(.page-header) input').first.attr('value').to_s.gsub("\u00A0", "").strip.gsub(/ +/, ' ').gsub('Опубликована', '').strip

    @collections = {} unless @collections
    @products = {} unless @products
    @collections[real_col_name] = { cid: cid.to_i, goods: [], group: group_name} unless @collections[real_col_name]

    col_name = real_col_name.gsub(/ - [0-9]+$/, '').strip

    page.search('li.goods-item').each do |li|
      sku = li.search('input[data-type=articul]').first.attr('value').to_s.strip
      next if sku.blank?

      gid = li.attr('data-gid').to_s
      next if gid == '0'

      has_orders = false
      has_orders = true if li.at('a.orders')

      @products[sku] = { col_name: col_name, cid: cid, gid: gid, sku: sku, old_col_name: real_col_name, has_orders: has_orders }
      @collections[real_col_name][:goods] << { sku: sku, gid: gid, cid: cid }
    end

    sleep(0.1 + rand(0.0..0.4))
    #
  end

  def load_sp_collections(pid)
    ret = {}
    page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

    page.search(".purchase-collection-list > .rows  > div").each do |d|
      cid = d.attr('id').to_s
      next if cid.to_i == 0

      name = d.at('input[data-role=name]').attr('value').to_s.strip
      ret[name] = cid
    end

  end

  def read_sp_purchase(pid)
    @sp_authorized unless @sp_authorized

    page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

    load_groups_from_sp(page)

    page.search(".purchase-collection-list > .rows  > div").each do |d|
      cid = d.attr('id').to_s
      next if cid.to_i == 0

      group_name = d.at('select.select-collection-group option[selected]').text
      group_id = d.at('select.select-collection-group option[selected]').attr('value').to_i
      group_name = nil if group_id == 0

      puts cid
      process_sp_col(cid, group_name)
    end
  end

  def sync_goods_collections(purchase, sp_pid)
    @sp_authorized unless @sp_authorized
    read_sp_purchase(sp_pid)
    @new_collections = {}
    @products.each do |sku, d|
      p = Product.where(art: sku, purchase_id: purchase.id).first
      if p
        if p.disabled
          new_col = 'Выключено'
        else
          new_col = p.collections.first.name
        end
        @new_collections[new_col] = [] unless @new_collections[new_col]
        @new_collections[new_col] << @products[p.art] if @products[p.art]
        @new_collections[new_col].last[:new_col_name] = new_col
        @new_collections[new_col].last[:coltype] = p.category

        unless @collections[new_col]
          cid = create_collection(sp_pid, new_col, p.category)
          @collections[new_col] = { cid: cid.to_i, goods: [], group: p.collections.first.grp } unless @collections[new_col]
          @collections[new_col][:goods] << { sku: sku, gid: d[:gid] }
          hide_collection(cid) if new_col == 'Выключено'
        end
      end
    end

    move = {}
    @new_collections.each do |col_name, products|
      per_col_limit = 100
      per_col_limit = 500 if col_name == 'Выключено'

      col_count = (products.count.to_f / per_col_limit.to_f).ceil
      per_col = (products.count.to_f / col_count.to_f).ceil
      products.each_with_index do |p, i|
        col_name_with_suffix = col_name
        if products.count > per_col_limit + 20
          suffix = (i / per_col.to_f).floor
          col_name_with_suffix = "#{col_name} - #{suffix + 1}" if suffix > 0
        end

        p[:new_col_name] = col_name_with_suffix
        unless @collections[col_name_with_suffix]
          cid = create_collection(sp_pid, col_name_with_suffix, p[:coltype])
          puts "Created #{col_name_with_suffix}, cid #{cid}"
          hide_collection(cid) if col_name == 'Выключено'
          @collections[col_name_with_suffix] = { cid: cid.to_i, goods: [] } unless @collections[col_name_with_suffix]
        end

        new_cid = @collections[col_name_with_suffix][:cid].to_i
        if p[:cid].to_i != new_cid
          move[new_cid] = [] unless move[new_cid]
          move[new_cid] << p[:gid]

          puts "Move #{p[:gid]} from #{p[:old_col_name]} to #{col_name_with_suffix}"
        end
      end
    end

    puts move

    move.each do |cid, gids|
      if gids.count > 0
        move_goods_to_collection(gids,cid)
      end
    end
  end

  def hide_collection(cid)
    #@agent.get  "https://www.100sp.ru/org/collection/toggleHide/#{cid}"
    puts "hide #{cid}"
  end

  def hide_empty_collections(pid)
    page = @agent.get "https://www.100sp.ru/org/collection/list/#{pid}"
    page.search(".purchase-collection-list > .rows  > div").each_with_index do |d, i|
      cid = d.attr('id').to_s
      goods = 0
      if /товары: (\d+)/ =~ d.text
        goods = $1.to_i
      end
      hide_collection(cid) if goods == 0
      sleep(0.2)
    end
  end

  def change_purchase_status(pid, status)
    statuses = { processing: 3 }
    s = statuses[status]
    raise "Invalid purchase status: #{status}" if s.nil?

    @agent.post 'https://www.100sp.ru/org/purchase/changeStatus', { pid: pid, status: s, newsText: '', '5fae48d3ab46b': 'Изменить' }, { 'Referer' => "https://www.100sp.ru/purchase/#{pid}" }

  rescue Mechanize::ResponseCodeError => e
    if e.response_code == '404'
      raise e.page.body
    end
  end

  def delete_disabled_goods(purchase, pid, disabled_hours: 10000000)
    page = @agent.get("http://www.100sp.ru/purchase/#{pid}")

    page.search("//div[contains(@class,'purchase-mini-collections')]/div/@id").each do |cid|
      next if cid.to_s == 'hits' or cid.to_s == 'sale'
      delete_disabled_goods_col('https://www.100sp.ru/org/collection/edit/' + cid, purchase,disabled_hours: disabled_hours)
    end
  end

  # post https://www.100sp.ru/org/good/hideGoods?toArchive=1
  # gids[]: 974431967
  # 
  def move_goods_from_deleted(pid)

  end

  def move_goods_from_hidden_cols(pid)
    authorize unless @sp_authorized
    page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

    new_cid = nil
    del = []
    page.search(".purchase-collection-list > .rows  > div").each_with_index do |d, i|
      cid = d.attr('id').to_s
      next if cid.to_i == 0

      name = d.at('input[data-role=name]').attr('value').to_s
      if name == 'Удалено'
        new_cid = cid
      end

      next if name.downcase.start_with? 'удалено'

      puts cid
      if d.attr('class').to_s.include? 'collectionHided'
        process_sp_col(cid)
      end
    end

    unless new_cid
      new_cid = create_collection(pid, 'Удалено', 0)
    end

    gids = @products.values.map { |p| p[:gid] }
    move_goods_to_collection(gids, new_cid)
  end

  def delete_disabled_goods_col(href, purchase, disabled_hours: 10000000)
    page = @agent.get(href)

    params = []
    page.search('ul#goods > li.muted').each do |li|
      gid = li.attr('data-gid')
      puts gid
      next if gid == '0'
      next if li.at('a.orders')

      sku = li.search('input.goods-articul').attr('value').to_s
      p = Product.where(art: sku, purchase_id: purchase.id).first

      params << ['gids[]', gid] if p && (Time.now - p.downloaded_at) / 60 / 60 > disabled_hours
    end

    unless params.empty?
      puts params
      page2 = @agent.post('https://www.100sp.ru/org/good/deleteGoods', params, @ajax_headers)
      puts page2.body
    end
  end

  def delete_empty_collections(pid)
    authorize unless @sp_authorized
    page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

    del = []
    page.search(".purchase-collection-list > .rows  > div").each_with_index do |d, i|
      cid = d.attr('id').to_s
      next if cid.to_i == 0

      puts cid

      if /товары: (\d+)/ =~ d.text
        if $1.to_i == 0
          del << cid
        end
      end
    end

    puts ''
    puts del

    unless del.empty?
      @agent.post "https://www.100sp.ru/org/collection/delete/?cid=#{del.join(',')}&return=%2Forg%2Fcollection%2Flist%2F#{pid}", { 'delete_collection': 'Удалить коллекции' }
    end
  end

  def create_collection(pid, name, cat)
    @sp_authorized unless @sp_authorized
    data = {
      'Collections[pid]': pid,
      'Collections[name]': name,
      'Collections[categories]': cat, #???
      'Collections[category_id]': cat,
      'Collections[description]': '',
      'Collections[goods_default_sorting]': '',
      'Collections[fee]': '',
      'collectionGroupId': '',
      'Collections[status]': 1,
      'YiiApp_models_CollectionsAttributes[showDiscountBadge]': 0,
      'yt0': ''
    }
    @agent.redirect_ok = false
    page = @agent.post 'https://www.100sp.ru/org/collection/create', data, { 'Referer' => "https://www.100sp.ru/purchase/#{pid}" }
    if /([0-9]+)$/ =~ page.header['location']
      $1.to_i
    else
      nil
    end
  end

  def move_goods_to_collection(gids, cid)
    @sp_authorized unless @sp_authorized

    data = [['newGoodCollection', cid]]
    gids.each { |g| data << ['gids[]', g] }
    puts data
    res = @agent.post "https://www.100sp.ru/org/good/changeGoodsCollection", data, @ajax_headers
    puts res.body
  end

  def load_pp_orders(purchase, pid)
    Rails.logger.info "load_pp_orders"
    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', { purchase: pid }, nil, { 'x-api-key' => API_KEY }
    data = JSON.parse(res.body)
    megaorders = {}
    if data['result']
      data['data'].each do |line|
        next unless line['status'] == 'подтвержден'
        #next unless line['payment_sum'].to_f>0
        next unless line['megaorder_id'].to_i > 0

        line['quantity'] = 1

        megaorder_id = line['megaorder_id'].to_i
        #Rails.logger.info megaorder_id

        megaorders[megaorder_id] = [] if megaorders[megaorder_id].nil?
        existing_line = megaorders[megaorder_id].find { |l| l['articul'] == line['articul'] and l['size'] == line['size'] }
        if existing_line
          existing_line['quantity'] += 1
        else
          megaorders[megaorder_id] << line
        end
      end

      megaorders.keys.sort.each do |megaorder_id|
        lines = megaorders[megaorder_id]

        doc = InventoryDocument.find_by_megaorder_id megaorder_id
        next if doc

        doc = InventoryDocument.create(
          megaorder_id: megaorder_id,
          user_id: purchase.user_id,
          doc_type: InventoryDocument.doc_types[:sale],
          name: "Продажа #{megaorder_id} от #{lines.first['created']}",
          comment: "Пользователь #{lines.first['user_name']}",
          posted: false
        )

        Rails.logger.info doc
        lines.each do |l|

          sku = l['articul'].gsub(/-pr[0-9.]+$/, '')

          prod = InventoryProduct.find_by_sku(sku)
          if prod
            prod_id = prod.id
          else
            prod2 = Product.find_by_sku(sku)
            if prod2
              m = MigrateInventory.new
              prod = m.process_product(prod2)
              prod_id = prod.id
            else
              prod_name = "Товар #{l['gid']} #{sku} #{l['name']} не найден в базе"
            end
          end

          line = InventoryDocumentLine.create(
            inventory_product_id: prod_id,
            name: prod_name,
            size: l['size'],
            amount_change: -l['quantity'],
            inventory_document: doc,
            retail_price: l['price'],
            line_type: InventoryDocumentLine.line_types[:relative]
          )
        end

        doc.post
      end
    end

  end

  def load_orders(purchase, pid, skip_low_rating: false)
    skip_orders = []

    Rails.logger.info skip_low_rating

    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', { purchase: pid }, nil, { 'x-api-key' => API_KEY }
    data = JSON.parse(res.body)
    ret = []
    if data['result']
      data['data'].each do |line|
        Rails.logger.info(line)
        next unless line['status'] == 'ожидает подтверждения' or line['status'] == 'не подтвержден'


        if skip_low_rating and line['user_rating'].to_f < 2 and line['payment_sum'].to_i == 0
          #  skip_orders<<line['megaorder_id']
          add_order_comment('не отправлен', line['oid'])
          next
        end

        Rails.logger.info("add")

        ret << line
      end

    end
    ret
  end

  def load_full_report(pid)
    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', { purchase: pid }, nil, { 'x-api-key' => API_KEY }
    data = JSON.parse(res.body)
    if data['result']
      return data['data']
    end
    []
  end

  def process_src_col(pid, cid, global_fee: 0, keep_names: false)
    ret = {}

    max_retries = 3
    retry_count = 0
    fee = global_fee

    begin

      page = @agent.get("https://www.100sp.ru/ajax/collectionModal?pid=#{pid}&cid=#{cid}&action=%2Forg%2Fcollection%2Fedit%2F#{cid}")
      t = page.search('select#Collections_fee option[selected]').first.attr('value').to_s
      fee = t.to_i if t != ''

      page = @agent.get("https://www.100sp.ru/org/collection/edit/#{cid}")
    rescue Mechanize::ResponseCodeError => e
      if e.response_code == '502' && retry_count < max_retries
        retry_count += 1
        wait_time = retry_count * 5 # Increase wait time with each retry
        puts "Encountered 502 error. Retrying in #{wait_time} seconds... (Attempt #{retry_count}/#{max_retries})"
        sleep(wait_time)
        retry
      else
        puts "Request failed after #{max_retries} retries. Exiting."
        raise
      end
    end

    if keep_names
      col_name = page.search('#collection-edit .editForm  h3:not(.page-header) input').first.attr('value').to_s.strip.gsub('Опубликована', '').strip
    else
      col_name = page.search('#collection-edit .editForm  h3:not(.page-header) input').first.attr('value').to_s.strip.gsub(/ - [0-9]+$/, '').gsub(/ +/, ' ').gsub('Опубликована', '').strip
    end

    puts "'#{col_name}'"

    cat_id = 0
    if page.at('.collectionEditPanel .infoBlock a')
      cat_id = page.at('.collectionEditPanel .infoBlock a').attr('href').to_s.gsub(/\D/, '')
    end

    col_pic = nil
    col_pic = page.search('.ImageEditorWidget a').first.attr('data-imgurl').to_s if page.search('.ImageEditorWidget a').first

    puts cat_id

    ret[:cat_id] = cat_id
    ret[:name] = col_name
    ret[:col_pic] = col_pic
    ret[:cid] = cid.to_i
    ret[:fee]  = fee
    ret[:goods] = []

    page.search('li.goods-item').each_with_index do |li, pos|
      gid = li.attr('data-gid').to_s

      sku = li.search('input[data-type=articul]').first.attr('value').to_s
      next if sku == ''

      name=li.search('input.goods-name').attr('value').to_s

      good_cat_id = li.attr('data-category').to_s

      good_cat_id = cat_id if good_cat_id.to_s == ''

      brand_name = nil

      if li.at('select[data-type=brand-tag] option[selected]')
        brand_name = li.at('select[data-type=brand-tag] option[selected]').text.to_s
      end

      if brand_name.nil? and li.at('button.goods-brand-tag--link') and li.at('button.goods-brand-tag--link').text.strip != 'Укажите бренд'
        brand_name = li.at('button.goods-brand-tag--link').text.strip
      end

      disabled = false
      if li.attr('class').to_s.include? 'muted'
        disabled = true
      end

      sizes = li.search('input.goods-sizes').attr('value').to_s.strip
      desc = li.search('textarea.goods-description').text
      price = li.search('input.goods-price').attr('value').to_s.to_f
      rrp = li.search('input.goods-recommendedPrice').first.attr('value').to_s.to_f if li.search('input.goods-recommendedPrice').first
      has_orders = false
      has_orders = true if li.at('a.orders')
      source = li.search('input.goods-source').attr('value').to_s.to_f

      width = li.attr('data-width').to_s.to_f
      height =  li.attr('data-height').to_s.to_f
      depth =  li.attr('data-depth').to_s.to_f
      netto =  li.attr('data-netto').to_s.to_f
      weightPreset = li.attr('data-preset').to_s

      pics=JSON.parse(li.attr('data-pics').to_s).map {|el| el['originalurl']}

      ret[:goods] << { gid: gid, sku: sku, name: name, cat_id: good_cat_id, brand_name: brand_name, pos: pos, disabled: disabled,
                           sizes: sizes, desc: desc, price: price, rrp: rrp, has_orders: has_orders, source: source,
                            width: width, height: height, depth: depth, netto: netto, weightPreset: weightPreset, pics: pics
      }
    end

    sleep(0.3 + rand(0.0..0.4))
    ret
  end

  def create_group(pid, name, priority)
    data = {pid: pid, name: name, priority: priority}
    res = @agent.post 'https://www.100sp.ru/org/collectionGroup/createGroup', data
    d = JSON.parse(res.body)
    if d['ok']
      @groups[name] = d['data']['id']
      return d['data']['id']
    end
    nil
  end

  def load_groups_from_sp(page)
    @groups = {}
    page.search('ol#groups-list-container > li > a' ).each do |a|
      name = a.text.strip
      id = a.attr('href').to_s.gsub(/\D/, '')
      @groups[name] = id
    end
  end

  def update_groups
    #pid: 782280
    #collectionGroups[0][id]: 518314
    #collectionGroups[0][pid]: 782280
    #collectionGroups[0][priority]: 0
    #collectionGroups[0][name]: Группа 1
    #collectionGroups[0][json][disabledHeader]: false

    #collectionGroupsReference[0][collection_group_id]: 518316 / 0
    #collectionGroupsReference[0][cid]: 10451235
    #collectionGroupsReference[0][priority]: 73

    # post https://www.100sp.ru/org/collectionGroup/update
    #
  end
  def read_source(pid, keep_names: false)
    page = @agent.get "https://www.100sp.ru/org/purchase/edit?pid=#{pid}"
    fee = page.search('select#PurchaseEditForm_fee option[selected]').attr('value').to_s.strip.to_i

    page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

    collections = {}
    page.search(".purchase-collection-list > .rows  > div").each_with_index do |d, i|
      cid = d.attr('id').to_s
      next if cid.to_i == 0

      puts cid
      col = process_src_col(pid, cid, global_fee: fee, keep_names: keep_names)
      col[:pos] = i

      collections[col[:name]] = col
    end

    collections
  end

  def load_inventory_folders_from_sp(pid, agent, user)
    @agent = agent
    authorize
    cols = read_source(pid)

    by_art = {}

    cols.each do |col_name, col|
      c = InventoryFolder.find_or_create_by(user_id: user.id, name: col_name)
      c.sp_pid = pid
      c.save

      col[:goods].each { |d|
        by_art[d[:sku]] = { col: c, cat_id: d[:cat_id], brand_name: d[:brand_name], pos: d[:pos], disabled: d[:disabled] }
      }
    end

    InventoryFolder.where(user_id: user.id, sp_pid: pid).each do |col|
      col.inventory_products.each do |prod|
        if by_art[prod.sku]
          prod_data = by_art[prod.sku]

          prod.inventory_folder = prod_data[:col]
          prod.save
        end
      end
    end
  end

  def get_inventory_export_data(sp_pid, user)

    purchase = Purchase.find_by_sp_pid sp_pid

    WebNotificationsChannel.broadcast_to(user, body: 'Загрузка заказов') if user
    puts 'Загрузка заказов'
    LoadOrdersFromSp.run
    WebNotificationsChannel.broadcast_to(user, body: 'Загрузка заказов завершена') if user
    puts 'Загрузка заказов завершена'

    @agent = Mechanize.new

    WebNotificationsChannel.broadcast_to(user, body: "Загрузка данных из закупки #{sp_pid}") if user
    puts "Загрузка данных из закупки #{sp_pid}"
    authorize
    load_inventory_folders_from_sp(sp_pid, @agent, user)
    WebNotificationsChannel.broadcast_to(user, body: "Загрузка данных из закупки #{sp_pid} завершена") if user
    puts "Загрузка данных из закупки #{sp_pid} завершена"

    ret = []

    ret << ['Коллекция', 'Артикул', 'Название', 'Подробнее', 'Цена', 'РРЦ', 'Размеры', 'UUID', 'Источник товара', 'Категория', 'Бренд', 'Группа коллекции', 'Картинка', 'Картинка 1', 'Картинка 2', 'Картинка 3', 'Картинка 4', 'Картинка 5', 'Картинка 6']

    InventoryFolder.where(sp_pid: sp_pid, user_id: user.id).order('name').each do |col|
      products = col.inventory_products

      products.each_with_index do |prod|

        ## sale24
        #next if prod.sku=='SAMS102252' or prod.sku=='SAMS141803'
        next if prod.stock.nil?
        next if prod.stock.all? { |s| s['q'] <= 0 }

        desc = ActionView::Base.full_sanitizer.sanitize(prod.desc)
        desc = '' if desc.nil?

        desc.gsub!(/[\r\n]+/, '. ')
        desc.gsub!(/ +/, ' ')
        desc.gsub!(/\.+/, '.')
        desc.strip!

        name = ActionView::Base.full_sanitizer.sanitize(prod.name)
        name = '' if name.nil?

        name.gsub!(/[\r\n]+/, '. ')
        name.gsub!(/ +/, ' ')
        name.gsub!(/\.+/, '.')
        name.strip!
        name.gsub!(/[.,]+$/, '.')

        cat = prod.cat.to_i

        col_name = col.name.gsub(/^!/, '')
        col_name.gsub!(/ *- *\d$/, '')

        brand = prod.brand.to_s

        line = [col_name.strip, prod.sku.strip, name, desc, '0', '', '-@0', '', '', cat, brand, '']

        urls = prod.get_image_urls
        line += urls

        price_mul = 1
        price_mul = 1 - purchase.discount.to_f / 100 if purchase.discount
        price_mul = 1 if purchase.discount_until and DateTime.now > purchase.discount_until

#        price_mul = 0.9 if DateTime.now < DateTime.new(2024, 06, 16)

        if prod.stock
          line[1] = prod.sku
          prices = {}
          rrp = {}
          prod.stock.each do |d|
            s = d['size']
            sp_price = d['sp_price'].to_s.gsub(/\.0$/, '')
            ###SALE DISCOUNT!
            sp_price=(sp_price.to_f*price_mul).floor

            q = d['q']
            q = 0 if q < 0
            next if q == 0
            prices[sp_price] = [] if prices[sp_price].nil?
            prices[sp_price] << "#{s}@#{q}"
            rrp[sp_price] = d['rrp'].to_i

            ###SALE DISCOUNT!
            rrp[sp_price] = (sp_price.to_f * 1.5).round(-1) if d['rrp'].to_i < sp_price.to_f * 1.1
            #rrp[sp_price]=(sp_price.to_f/0.9*1.5).round(-1) if d['rrp'].to_i<sp_price.to_f*1.1

            rrp[sp_price] = rrp[sp_price].to_i + 1 if rrp[sp_price].to_i - sp_price.to_i <= 2
          end
          prices.each do |p, s|
            new_line = line.clone
            new_line[4] = p
            new_line[5] = rrp[p].to_s
            new_line[5] = '' if p.to_f >= new_line[5].to_f
            new_line[5].gsub!(/\.0$/, '')
            new_line[6] = s.map { |s| s.gsub(',', '.') }.join(',')

            new_line[1] += "-pr-#{p}" if prices.count > 1

            # TODO: products with different prices for different sizes
            #            if prices.count>1 and not s.start_with? '-@'
            #            end

            ret << new_line
          end
        end

      end
    end

    ret

  end

  def import_data(sp_pid, user)
    WebNotificationsChannel.broadcast_to(
      user,
      body: 'Загрузка начата'
    )
    puts "Загрузка начата"

    data = get_inventory_export_data(sp_pid, user)

    headers = data[0]

    zero_price_arts = []

    out = "\xEF\xBB\xBF" + CSV.generate(:col_sep => ';') { |csv|
      data.each do |line|
        d = Hash[headers.zip line]
        zero_price_arts << d['Артикул'] if d['Цена'] != 'Цена' and d['Цена'].to_i == 0

        csv << line
      end
    }
    if zero_price_arts.length > 0 and not Rails.env.development?
      WebNotificationsChannel.broadcast_to(
        user,
        body: "Нулевая цена, артикулы #{zero_price_arts.join(', ')}"
      )
    end

    if Rails.env.development?
      File.open("e:/imp.csv", "wb") { |f| f.write(out) }
      return
    end

    File.open("/tmp/imp_#{sp_pid}.csv", "wb") { |f| f.write(out) }
    #return

    #@agent.log=Logger.new("e:\\log1.txt")

    Tempfile.create(['import_data', '.csv']) do |file|
      file.write(out)
      file.rewind

      params = {
        purchase: Rails.env.development? ? 885479 : sp_pid,
        duplicate: 'articul',
        'overwrite[price]': 'price',
        'overwrite[recommendedPrice]': 'recommendedPrice',
        'overwrite[name]': 'name',
        'overwrite[description]': 'description',
        'overwrite[brandTag]': 'brandTag',
        photos: 'new',
        missing: 'drop_remains',
        #hidden: 'publish',
        remains: 'overwrite',
        collections: 'leave',
        email: '<EMAIL>',
        file: file
      }

      @agent.open_timeout = 600
      @agent.read_timeout = 600

      WebNotificationsChannel.broadcast_to(user, body: "Импорт на 100sp начат") if user

      res = @agent.post "https://www.100sp.ru/org/purchase/importExport/apiImport", params, { 'x-api-key' => API_KEY }
      res = JSON.parse(res.body)
      Rails.logger.info res

      if res['result']
        body = 'Загрузка закончена успешно'
      else
        body = "Загрузка закончена с ошибками: #{res['messages'].join(',')} #{res['errors'].join(',')}"
      end
      WebNotificationsChannel.broadcast_to(
        user,
        body: body
      )

    end

  end

  def get_stock(gid)
    page = @agent.get("https://www.100sp.ru/good/#{gid}")

    ret = {}
    page.search('div.sizesOrdersWidget td.size').each do |td|
      size = td.at('span.size-name').text
      td2 = td.at('[data-field=oneSizeCountSet]')
      stock = td2.attr('data-value').to_i
      size_id = td2.attr('data-sizid').to_i

      ret[size] = {stock: stock, size_id: size_id, gid: gid}
    end
    ret
  rescue Mechanize::ResponseCodeError => e
    {}
  end

  def update_stock(gid, size_id, stock)
    data = { 'data[countIncrement]' => stock, 'action' => 'changeCount', 'sizid' => size_id, 'gid' => gid, 'type' => 'oneSizeCountSet' }
    res = @agent.post "https://www.100sp.ru/org/good/updateAttribute", data, @ajax_headers
    puts res.body
    sleep(0.13)
    true
  end
  def add_order_comment(comment, order_id)
    data = { 'text' => comment, 'ajax' => 'true', 'selector' => ".order#{order_id} .org_comment" }
    res = @agent.post "https://www.100sp.ru/orgComment/addOrgComment?id=#{order_id}&type=2", data, @ajax_headers
    puts res.body
    sleep(0.13)
  end

  def add_megaorder_comment(comment, order_id)
    data = { 'text' => comment, 'ajax' => 'true', 'selector' => ".order#{order_id} .org_comment" }
    res = @agent.post "https://www.100sp.ru/orgComment/addOrgComment?id=#{order_id}&type=3", data, @ajax_headers
    puts res.body
    sleep(0.13)
  end

  def confirm_payment(megaorder_id, sum)
    authorize unless @sp_authorized

    page=@agent.get "https://www.100sp.ru/paymentHistory/index?connectedId=#{megaorder_id}&connectedType=megaorder"
    page.search('table#payment-history-table tbody tr').each do |tr|
      site_sum = tr.at('td[data-label="Сумма"]').text.gsub(/\s/, '').to_i
      status = tr.at('td[data-label="Статус"]').text.strip

      if tr.at('a[data-paymentid]')
        payment_id = tr.at('a[data-paymentid]').attr('data-paymentid').to_s

        return true if status == 'подтвержден' and site_sum.to_i == sum.to_i

        if status == 'оплачен' and site_sum.to_i == sum.to_i
          begin
            @agent.get "https://www.100sp.ru/payment/setPaymentStatus?status=confirmed&id=#{payment_id}"
            return true
          rescue Mechanize::ResponseCodeError => e
            return if e.response_code == '404'
          end
        end
      end
    end
    false
  end

  def get_purchase_data(pid)
    authorize unless @sp_authorized
    page=@agent.get "https://www.100sp.ru/org/purchase/edit?pid=#{pid}"
    short_description = page.at('textarea#PurchaseEditForm_annotation').text.strip
    description = page.at('textarea#PurchaseEditForm_description_good').text.strip
    { short_description: short_description, description: description }
  end

  def load_purchase_list(state: nil)
    authorize unless @sp_authorized

    ret = []

    page = @agent.get 'https://www.100sp.ru/org/purchase/index?mode=active'

    page.search('tr.purchases-table__row').each do |tr|
      s = tr.at('td.purchases-table-col--status').text.strip
      next if state && s != state
      pid = tr.attr('data-purchase-id').to_i
      delivery_days = tr.at('div.purchase-badge-infinity span.content')&.text&.strip&.to_i
      name = tr.at('a.purchase_name').text.strip
      ret << { pid: pid, name: name, state: s, delivery_days: delivery_days }
    end

    ret
  end
  #private

end

