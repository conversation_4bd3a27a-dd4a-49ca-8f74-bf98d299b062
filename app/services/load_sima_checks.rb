# frozen_string_literal: true

class LoadSimaChecks

  def get_shipping_cost(sid)
    d = { items: [{ sid: sid, qty: 1 }], settlement_id: 27503886 }.to_json
    res = @agent.post("https://www.sima-land.ru/api/v3/delivery-calc/", d,
                      { 'Accept' => 'application/json', 'Content-Type' => 'application/json' })

    JSON.parse(res.body)[sid.to_s]['cost']
  end

  def process_check_items(items)
    items.each do |item|
      c = SimaCheckItem.find_or_create_by(item_id: item['id'])
      c.sima_check_id = item['check_id']
      c.amount = item['qty'].to_i
      c.price = (item['price'].to_f/c.amount).ceil(2)
      c.sid = item['item_sid']
      c.name = item['item_name']
      c.sima_item_id = item['item_id']
      c.shipping_cost = get_shipping_cost(item['item_sid']) unless c.shipping_cost
      c.save
    end

  end
  def process_check(check_id)
    next_link = "https://www.sima-land.ru/api/v3/check-item/?check_id=#{check_id}"
    while next_link do
      res = @agent.get next_link, nil, nil, { 'x-api-key' => @token }
      d = JSON.parse(res.body)
      process_check_items(d['items'])
      next_link = nil
      next_link = d['_links']['next']['href'] if d['_links']['next']
    end
  end

  def process_checks(checks)
    checks.each do |check|
      next if check['initial_date'].nil? || DateTime.parse(check['initial_date']) < 6.month.ago
      c = SimaCheck.find_or_create_by(check_id: check['id'])
      c.check_name = check['invoice_number']
      c.goods_price = check['subtotal']
      c.shipping_price = check['delivery_sum']
      c.check_date = check['initial_date']
      c.save
      process_check(check['id'])
    end
  end
  def run
    @token = Simaland::SimalandParser::JWT_TOKEN
    @agent = Mechanize.new

    next_link='https://www.sima-land.ru/api/v3/check/'
    while next_link do
      res = @agent.get next_link, nil, nil, { 'x-api-key' => @token }
      d = JSON.parse(res.body)
      process_checks(d['items'])

      next_link = nil
      next_link = d['_links']['next']['href'] if d['_links']['next']
    end
  end
end
