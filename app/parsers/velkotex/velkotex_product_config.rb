#encoding:utf-8
require_relative '../framework/site/product_config'
module Velkotex
  class VelkotexProductConfig < Framework::Site::ProductConfig

    def get_colors(product_page, articul)
      colors = product_page.search('#set_col li').map { |li| {
          'id' => li.attr('data-color_id').to_s,
          'name' => li.search('a').first.attr('title').to_s,
          'size_id' => li.search('a').first.attr('data-numb').to_s,
      } }

      colors.delete_if{|c| c['id'].blank?}

      colors.each do |color|
        size_table = product_page.search(".size_item#tabsS-#{color['size_id']}")
        color['sizes'] = size_table.search('tr[data-name=product]').map { |tr|
          size_name = tr.search('td').take(2).map { |td| prepare_string td.search('span').text }
          price = tr.search('.curr_price').first.text.gsub(' ', '').gsub(',', '.').to_i
          {
              'name' => "#{size_name[0]} (#{size_name[1]})",
              'price' => price
          }
        }
        color['sizes'].delete_if{|size| size['price'] == 0}
        puts color['id']
        color['pics'] = product_page.search("[data-color_id=#{color['id']}] .jcarousel a[data-zoom-image]").map{|a| a.attr('data-zoom-image').to_s}
      end

      colors.delete_if { |color| color['sizes'].length == 0 or color['pics'].length == 0 }

      colors.each do |color|
        @get_images = ->(pp){ color['pics'] }
        color['pics'] = save_images(product_page,"#{articul}~#{color['id']}" )
      end

      @get_images = ->(pp){[]}
      colors
    end

    def create_new_product(product_page, link=nil)
      @purchase.check_stop

      product = parse_product(product_page, link)

      is_exist_old_product = Product.joins(:collection).exists?(collections: {purchase_id: @purchase.id}, art: product.articul)
      return if is_exist_old_product

      colors = get_colors(product_page, product.articul)
      result_json = {}

      colors.each do |color|
        color['sizes'].group_by{|s| s['price']}.each do |group|
          clone_product = product.clone
          clone_product.images = color['pics']
          clone_product.sizes = group[1].map{|s| s['name']}
          clone_product.price = group[0]

          clone_product.description = "Цвет: #{color['name']}. #{clone_product.description}"
          clone_product.name = "#{clone_product.name} Цвет: #{color['name']}"

          add_product(clone_product)

          merge_json_sizes(clone_product, color['name'], result_json)

        end
      end

      add_product_new(product, result_json)
    end

    def merge_json_sizes(clone_product, color_name, result_json)
      json = get_json(clone_product, color_name)

      # бежим по результату и мержим значения размеров
      json.each do |color, color_json|
        unless result_json.include? color
          result_json[color] = color_json
          next
        end

        color_json['sizes'].each{|size_name, size_json|
          result_json[color]['sizes'][size_name] = size_json unless result_json[color]['sizes'].include? size_name
        }
      end
    end
  end
end