<template>
  <q-page padding class="q-gutter-md">
    <div class="text-h6">Разбор закупок</div>
    <div class="row">
      <div class="col-9 q-pa-md">
        <div class="row" v-if="$q.platform.is.mobile">
          <div class="col-12">
          </div>
        </div>
        <div class="row">
          <div class="col-8">
            <q-input dense outlined v-model="barcode" label="Штрихкод" @update:model-value="barcodeChanged"/>
          </div>
          <div class="col-2 q-ml-sm">
            <q-btn label="ШК" color="primary" @click="search"/>
          </div>
        </div>

        <div v-if="foundOrder">
          <transition
            enter-active-class="animated fadeIn"
            leave-active-class="animated fadeOut"
            appear
            mode="out-in"
          >
            <div v-if="foundOrder.order">
              <div :key="foundOrder.order.id">
                <div class="q-pa-md">
                  Мегазаказ: <a
                  :href="`https://www.100sp.ru/org/megaorder/${foundOrder.order.megaorder_id}`">{{
                    foundOrder.order.megaorder_id
                  }}</a>,
                  user: {{ foundOrder.order.user }},
                  запупка: {{ foundOrder.purchase?.name }}, товар: {{ foundOrder.order.sku }} //
                  {{ foundOrder.order.product_name }}
                </div>
                <div v-if="foundOrder.left_orders.length==0 && foundOrder.other_orders.length==0"
                     class="text-green text-weight-bold">
                  Нет других заказов, это все позиции
                </div>
              </div>
            </div>
          </transition>

          <div v-if="foundOrder.order">

            <div v-if="foundOrder.left_orders?.length>0">
              <br/><br/>
              Позиции заказа:
              <div v-for="o in foundOrder.left_orders" v-bind:key="o.id" :class="foundOrder.order.sku == o.sku ? 'text-weight-bold':''">

                {{ o.sku }} // {{ o.product_name }} // {{ o.box?.box_name }}
              </div>
            </div>
          </div>


          <div v-if="foundOrder.box">
            Коробка <span class="text-weight-bold"> {{ foundOrder.box.box_name }} </span>, получена {{ foundOrder.box.received_at }}

            <q-table :rows="foundOrder.items_in_box" dense :columns="boxItemsColumns" row-key="id" hide-header
                     :pagination="{rowsPerPage:0}" hide-bottom
            >
              <template v-slot:body-cell-orders="props">
                <q-td :props="props">
                  <span v-for="(q, megaorder_id, index) in props.row.orders" v-bind:key="megaorder_id">

                <a :href="`https://www.100sp.ru/org/megaorder/${megaorder_id}`" target="_blank"> {{megaorder_id }} ({{ q }})</a>
                    {{ index < Object.values(props.row.orders).length-1 ? ', ' : '' }}
              </span>
                </q-td>
              </template>
            </q-table>

          </div>
        </div>

      </div>

    </div>


  </q-page>
</template>

<script setup>

import {
  defineComponent,
  getCurrentInstance,
  onMounted,
  onActivated, nextTick,
} from "vue";
import {ref, watch} from "vue";
import {api} from "boot/axios";
import {useQuasar} from "quasar";
import {exportFile} from "quasar";
//import {QrcodeStream, QrcodeDropZone, QrcodeCapture} from 'vue-qrcode-reader'
//            <qrcode-stream @detect="onDetect" :formats="['ean_13']"></qrcode-stream>


const $q = useQuasar();
const app = getCurrentInstance();
const purchase = ref(null);
const message = ref("");
const pid = ref("");
const barcode = ref("");
const orders = ref([]);
const users = ref({});
const barcodeDetector = ref(null);
const video = ref(null)
const slots = ref([]);
const foundOrder = ref(null);

//const codes2 = ref([]);


onMounted(() => {
  //loadSlots();
});

async function loadOrderData() {
  $q.loading.show();
  let res = await api.get("/api3/pack_orders/load_order_data")
  $q.loading.hide();
}

const boxItemsColumns = [
  {name: "sku", field: "sku", label: "Артикул", align: "left"},
  {name: "name", field: "name", label: "Название", align: "left"},
  {name: "quantity", field: "quantity", label: "Количество", align: "left"},
  {name: "orders", label: "Заказы", align: "left", field: 'orders'},
]

async function loadPurchase() {
  $q.loading.show();

  let res = await api.post(`/api3/purchases/pack`, {pid: pid.value});
  //purchase.value = res.data.purchase;
  orders.value = res.data.report;
  for (let o of res.data.report) {
    if (o.status == "подтвержден") {
      if (!users.value[o.user_name]) users.value[o.user_name] = [];
      users.value[o.user_name].push(o);
    }
  }
  $q.loading.hide();
}

async function loadSlots() {
  let res = await api.get("/api3/pack_orders/slots");
  slots.value = res.data;
}

const onDetect = (codes) => {
  console.log(codes);
  //codes2.value = codes;
  if (codes.length > 0) {
    //barcode.value = codes[0].code;
    barcodeChanged(codes[0].rawValue);
  }
}

async function freeSlotConfirm(id) {
  $q.dialog({
    title: "Подтверждение",
    message:
      "Очистить ячейку?",
    cancel: true,
    persistent: true,
  }).onOk(() => {
    freeSlot(id);
  });
}

async function freeSlot(id) {
  let res = await api.post("/api3/pack_orders/mark_slot_free", {slot_id: id});
  if (res.data.error) {
    $q.notify({
      message: res.data.error,
      color: 'negative',
      position: 'top',
      timeout: 1000,
    });
    return;
  }
  slots.value = res.data;
}

async function putItem() {
  let res = await api.post("/api3/pack_orders/put_item", {
    order_id: foundOrder.value.order.id,
    slot_id: foundOrder.value.slot.id
  });
  if (res.data.error) {
    $q.notify({
      message: res.data.error,
      color: 'negative',
      position: 'top',
      timeout: 1000,
    });
    return;
  }
  slots.value = res.data;
}

async function search() {
  barcodeChanged(barcode.value);
}

function addSlots() {
  $q.dialog({
    title: "Подтверждение",
    message:
      "Добавить 10 ячеек?",
    cancel: true,
    persistent: true,
  }).onOk(async () => {
    let res = await api.post("/api3/pack_orders/add_slots");
    slots.value = res.data;
  });
}

async function renameSlot(slot, val) {
  let res = await api.post("/api3/pack_orders/rename_slot", {slot_id: slot.id, name: val});
  slots.value = res.data;

}

const barcodeChanged = async (value) => {
  if (value.length === 13) {
    nextTick(() => {
      barcode.value = ''
    });

    const {data} = await api.get(`/api3/pack_orders/find_order/${value}`);
    console.log(data);

    if (data.error) {
      $q.notify({
        message: data.error,
        color: 'negative',
        position: 'top',
        timeout: 1000,
      });
      $q.loading.hide();
      return;
    }

    foundOrder.value = data;

    //orders.value = data.orders
  }
}

</script>
