class AnalyticsController < ApplicationController
  #before_action :authenticate_user! unless Rails.env.development?

  def initialize
    super
    @db = get_db_connection
    @orders = @db[:orders]
  end

  def get_db_connection
    if Rails.env.development?
      Mongo::Client.new('mongodb://192.168.99.241:27017/sp_reports')
    else
      Mongo::Client.new('mongodb://127.0.0.1:27017/sp_reports')
    end

  end

  def get_all_order_by_hour
    @orders.aggregate([
                    { '$project': {
                      'created'=> { '$dateToString' => { 'format' => "%Y-%m-%d %H:00",'timezone'=>'+10', 'date' => "$created" } },
                      'price' => 1,
                      'user_id' => 1

                    } },
                    { "$group" => {
                      "_id" => { "created" => "$created"},
                      "total" => { "$sum" => "$price" },
                      "count" => { "$sum" => 1 },
                      'uniqueCount'=> {'$addToSet'=> "$user_id"}

                    } },
                    {'$project'=>{'userCount'=>{'$size'=>'$uniqueCount'},'total'=>1,'_id'=>1}},
                    { '$addFields'=> { 'created' => "$_id.created"}},
                    { '$project'=> {'_id' => false }},
                    { '$sort' => { "created" => 1 } }

                  ], { allow_disk_use: true })
  end

  def get_all_order_by_day(mid:nil)
    query=[
      { '$project': {
        'created'=> { '$dateToString' => { 'format' => "%Y-%m-%d",'timezone'=>'+10', 'date' => "$created" } },
        'price' => 1,
        'user_id' => 1

      } },
      { "$group" => {
        "_id" => { "created" => "$created"},
        "total" => { "$sum" => "$price" },
        "count" => { "$sum" => 1 },
        'uniqueCount'=> {'$addToSet'=> "$user_id"}
      } },
      {'$project'=>{'userCount'=>{'$size'=>'$uniqueCount'},'total'=>1,'_id'=>1}},
      { '$addFields'=> { 'created' => "$_id.created"}},
      { '$project'=> {'_id' => false }},
      { '$sort' => { "created" => 1 } }

    ]

    query.unshift({'$match'=>{'mega_id'=>mid.to_i}}) if mid

    @orders.aggregate(query, { allow_disk_use: true })
  end

  def get_all_order_by_month

    @orders.aggregate([
                        { '$project': {
                          'created'=> { '$dateToString' => { 'format' => "%Y-%m",'timezone'=>'+10', 'date' => "$created" } },
                          'price' => 1,
                          'user_id' => 1

                        } },
                        { "$group" => {
                          "_id" => { "created" => "$created"},
                          "total" => { "$sum" => "$price" },
                          "count" => { "$sum" => 1 },
                          'uniqueCount'=> {'$addToSet'=> "$user_id"}
                        } },
                        {'$project'=>{'userCount'=>{'$size'=>'$uniqueCount'},'total'=>1,'_id'=>1}},
                        { '$addFields'=> { 'created' => "$_id.created"}},
                        { '$project'=> {'_id' => false }},
                        { '$sort' => { "created" => 1 } }

                      ], { allow_disk_use: true })
  end

  def get_all_order_by_minute(pid=nil)
    query=[
      { '$project': {
        "createdRound": {
          "$toDate": {
            "$add": [
              { "$toLong": "$created" },
              {'$subtract':[
                1000 * 60 * 15,
                { "$mod": [ { "$toLong": "$created" }, 1000 * 60 * 15 ] }
              ]}

            ]
          }},
        'price' => 1,
        'user_id' => 1,
      } },
      { "$group" => {
        "_id" => { "created" => "$createdRound"},
        "total" => { "$sum" => "$price" },
        "count" => { "$sum" => 1 },
        'uniqueCount'=> {'$addToSet'=> "$user_id"}
      } },
      {'$project'=>{
        'userCount'=>{'$size'=>'$uniqueCount'},
        'total'=>1,
        'created'=>{ '$dateToString' => { 'format' => "%Y-%m-%d %H:%M",'timezone'=>'+10', 'date' => "$_id.created" } },
      }},
      {'$addFields'=>{'total'=>{'$toDouble'=>'$total'}}},
      { '$sort' => { "_id" => 1 } },
    { '$project'=> {'_id' => false }},

    ]
    query.unshift({'$match'=>{'purchaseId'=>pid.to_i}}) if pid
    data=@orders.aggregate(query, { allow_disk_use: true }).to_a

    expenses=[]
    if pid
      expenses=@db[:expenses].find({
                                     pid:pid.to_i,
                                     '$and':[
                                       {type:{'$ne':'Оплата покупки'}},
                                       {type:{'$ne':'Копия покупки'}}
                                     ]
                                   }).sort({created:1}).to_a
      expenses=expenses.map{|el|
        el['sum']=el['sum']>25?2:1
        el
      }
    end

    data=data.map {|el| {created: el['created'], count: el['userCount'], total: el['total']}}
    {orders: data, expenses: expenses}
  end

  def data
    if params[:pid]
      data=get_all_order_by_minute(params[:pid])
    elsif params[:mid]
      data=get_all_order_by_day(mid:params[:mid])
    else
      @data = get_all_order_by_month.to_a[1..-2]
    end

    render :json => {result:'ok',data:data}
  end

  def index
    @mega_purchases = @db[:mega_purchases].find().to_a
    @data = get_all_order_by_month.to_a[1..-2].map {|el| {created: el['created'], count: el['userCount'], total: el['total']}}

    @purchases=@orders.aggregate([
                                   {'$group'=>
                                      {
                                        '_id'=>{purchaseId:'$purchaseId',purchase:'$purchase',megaId:'$mega_id'},
                                        'created'=>{'$first'=>"$created"}
                                      }
                                   }
                                 ])
                      .map {|v| v['_id'].to_h.merge({'created'=>v['created'].to_s})}
                      .sort_by {|el| el['purchaseId']}

    respond_to do |format|
      format.html { render :index, :layout => false }
    end
  end

end
