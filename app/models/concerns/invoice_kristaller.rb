#encoding: utf-8

module InvoiceKristaller
  extend ActiveSupport::Concern

  def unprocessed_invoice?
    true
  end

  def read_invoice(file)
    ret=[]
    workbook = Spreadsheet.open(file)
    sheet = workbook.worksheet(0)

    code_num=get_col_number(sheet,'код')
    name_num=get_col_number(sheet,'наименование')
    q_num=get_col_number(sheet,'Кол-во')
    price_num=get_col_number(sheet,"Цена за ед. со скидкой")

    raise "Bad file" if not code_num or not name_num or not q_num

    in_data=false
    sheet.each do |row|
      next if row.nil?
      next if row[code_num].blank?


      if row[code_num]=='Код'
        in_data=true
        next
      end

      next unless in_data

      art=row[code_num].to_s.strip.gsub(/\.0$/,'')
      name=row[name_num]
      q=row[q_num].to_i

      price='0'
      price=row[price_num].to_s if price_num

      ret<<{'Артикул'=>art,'Название'=>name,'Количество'=>q, 'Цена закуп'=>price}
    end
    ret
  end

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    site_art=fields['Артикул'].split[1]

    line['Артикул'].upcase==site_art
  end

end