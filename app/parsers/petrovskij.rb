#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Petrovskij < Downloader
  include InvoicePetrovskij

  def initialize
    @dlparams={}

    @purchase_tags=['ДМ Fix','ДМ','ДМ текстиль']

    @skip_brands=[
        'Dünya Plastik',
        'LDN',
        'NECO',
        'Nika',
        '<PERSON><PERSON><PERSON><PERSON>',
        'Stasi<PERSON>',
        '<PERSON><PERSON>ont<PERSON>',
        'Альтернатива (Башпласт)',
        'Бытпласт',
        'Завод металлических изделий',
        'Золотое (Керамика)',
        'Камская посуда (чугун)',
        'Кубаньфарфор',
        'М-Пластика',
        'Мартика',
        'Мультипласт',
        'Нытва',
        'Павловский завод им. Кирова',
        'Полимербыт',
        'Псковский гончар',
        'Эльфпласт',
        'TORNADICA'
    ]

    super
  end


  def process_cat_data(page,cat)
    product_data={}
    page.body.scan(/JCCatalogItem\((.*?)\);\s+<\/script>/).each {|t|
      d=JSON.parse(t[0].gsub("'",'"'))
      product_id=d['PRODUCT']['ID']

      next if d['PRODUCT']['ITEM_PRICES'].empty?

      next if d['PRODUCT']['PICT']['NO_PHOTO']

      product_data[product_id]={}
      product_data[product_id]['name']=HTMLEntities.new.decode(d['PRODUCT']['NAME'])
      product_data[product_id]['price']=d['PRODUCT']['ITEM_PRICES'].first['PRICE']
      product_data[product_id]['pics']=[]
      product_data[product_id]['pics']<<d['PRODUCT']['PICT']['SRC']
      d['PRODUCT']['MORE_PHOTO'].each{ |p|
        product_data[product_id]['pics']<<p['SRC']
      }
    }

    products=0

    page.search('.nbCatalogSection__items .nbCatalogItem').each {|div|
      product_href=div.search('a.nbCatalogItem__name').first.attr('href')
      product_id=product_href.split('/')[-1]


      next if product_data[product_id].nil?
      next if @skip_product_id.include? product_id

      code=div.search('dd.nbCatalogItem__propValue._code').first.text
      art=''
      art=div.search('dd.nbCatalogItem__propValue._cml2_article').first.text if div.search('dd.nbCatalogItem__propValue._cml2_article').first
      min_order=div.search('span.product-item-amount-ratio-value').text.to_s

      price=product_data[product_id]['price']

      cat2=cat
      if cat.include? 'иксированн' or price.to_f<35
        @fix_price<<product_id
        return if price.to_f<5

        if price.to_f<12
          price=19
          cat2='Все по 19 рублей'
        elsif price.to_f<24
          price=39
          cat2='Все по 39 рублей'
        elsif price.to_f<35
          price=59
          cat2='Все по 59 рублей'
        elsif price.to_f<53
          price=79
          cat2='Все по 79 рублей'
        elsif price.to_f<=75
          price=109
          cat2='Все по 109 рублей'
        else
          next
        end
      else
        next if @fix_price.include? product_id
        unless cat2.include? 'Домашний текстиль'
          price=(price.to_f*1.37).ceil
          price=(price*1.07).ceil if price<500
        end
        rrp=(price.to_f*1.5).ceil
      end

      next if price.to_f<3

      name=product_data[product_id]['name']

      next if @skip_brands.any? {|sb| name.downcase.include? sb.downcase}

      desc=name
      if name.length>200
        name=name[0..199]
      end

      pics=[]
      product_data[product_id]['pics'].each_with_index {|url,i|
        pics<<savePic(url,"#{product_id}~#{i}",true)
      }


      cattype="4686"
      if cat2.include? 'ЧАСЫ' then cattype='994' end

      cat2.gsub!('ТЕКСТИЛЬ СОБСТВЕННОГО ПРОИЗВОДСТВА-','')
      cat2.gsub!('Домашний текстиль, СОБСТВЕННОЕ ПРОИЗВОДСТВО-','')

      col=addCollection(cat2,cattype)

      sizes=[]
      if min_order!='1' then sizes<<"Упаковка #{min_order} шт. Цена за 1" end

      prod=addProduct(col,product_id+' '+code+' '+art,name,price.to_f.ceil,desc,sizes,pics,nil,0,nil,rrp:rrp)
      if (cat2.include? 'Все по ') or prod.collections.first.name.include? 'Все по '
        prod.collections=[col]
        prod.save
      end
      products+=1
    }
    products
  end

  def process_cat(url)
    @log.info(url)

    #next unless url.include? '/catalog/uborka/shvabry-oknomoyki-zapaski-derzhateli-dlya-shvabr/'
    begin
      page = @agent.get(url)
    rescue Mechanize::ResponseCodeError => e
      return if e.response_code == '404'
    end

    puts page.search('span.nbBreadcrumb__name').to_s
    cat=page.search('span.nbBreadcrumb__name')[2..-1].map(&:text).join('-')

    cat.gsub!(', СОБСТВЕННОЕ ПРОИЗВОДСТВО','')

    c=cat.downcase

    #return unless c.include? 'новы' or c.include? 'доск'

    process_cat_data(page,cat)

    a=page.search('.nbCatalogSection__pager._top ul.nbPagination__container a')[-2]
    if a
      h=a.attr('href').to_s
      if /(\d+)$/=~h
        last_page=$1.to_i
        (2..last_page).each {|p|
          puts "#{url}?PAGEN_2=#{p}"

          page2=@agent.get "#{url}?PAGEN_2=#{p}"
          sleep(0.3+rand(0.3))
          process_cat_data(page2,cat)
        }
      end
    end

  end

  def fill_skip_product_id

    @agent.cookie_jar.clear!
    cookie = Mechanize::Cookie.new :domain => '.p-td.ru', :name => 'NB_jsNbcountItem', :value => '1000', :path => '/', :expires => (Date.today + 1).to_s
    @agent.cookie_jar << cookie

    page=@agent.get 'https://p-td.ru/brands/'
    page.search('.body__pageContent  li.nbCatlogSectionList__item').each {|li|
      n=li.search('.nbCatlogSectionList__itemName').text.strip
      if @skip_brands.include? n
        p2=@agent.get li.search('a.nbCatlogSectionList__link').first.attr('href').to_s
        p2.search('.nbCatalogSection__items .nbCatalogItem').each {|div|
          product_href=div.search('a.nbCatalogItem__name').first.attr('href')
          product_id=product_href.split('/')[-1]
          @skip_product_id<<product_id
        }
      end
    }
    @agent.cookie_jar.clear!
  end

  def run
    page = @agent.get('https://p-td.ru/catalog/')


    @fix_price=[]

    @skip_product_id=[]

       process_cat('https://p-td.ru/catalog/do30/')

    page.search('a.catalogMenu__link._lvl_2').each do |a|
      next if a.attr('href').to_s.include? '/do30'
      process_cat(a.attr('href').to_s)  #&dm_flag=Y
    end

    page.search('a.catalogMenu__link._lvl_1:not(._parent)').each do |a|
      next if a.attr('href').to_s.include? '/do30'
      process_cat(a.attr('href').to_s)  #&dm_flag=Y
    end

  end
  #handle_asynchronously :run

end