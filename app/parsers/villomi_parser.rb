class VillomiParser < Downloader
  @dlparams={'login'=>:text,'password'=>:password}

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}

    @prices=Hash.new

    super
  end

  def processPage(url)
    tries ||= 2

    @log.info(url)
    page = @agent.get(url)
    puts url

    return if page.search('span.rewardin').empty?

    name=page.search('#content h1[itemprop=name]').first.text.strip
    code=page.search('input[name=product_id]').attr('value').to_s

    art=page.search('#content span[itemprop=model]').text.strip
    price=page.search('#content meta[itemprop=price]').attr('content').to_s.to_f.ceil

    cat=page.search('ul.breadcrumb a')[1..2].map {|a| a.text.strip}.join '-'

    props=Hash.new
    page.search('#tab-description table.table.table-bordered tbody tr').each { |tr|
      tds=tr.search('td')
      next if tds[0].text=='Производитель'

      props[tds[0].text]=tds[1].text
    }
    desc=props.map {|k,v| "#{k}: #{v}"}.join ', '

    page.search('#tab-description table').remove
    desc2=page.search('#tab-description').text.strip
    desc+='. '+desc2

    pic_urls=page.search('#additional a.thumbnail').map{|a| a.attr('href').to_s}
    pics=[]
    pic_urls.each_with_index {|url,i|
      pics<<"#{code}~#{i}"
      savePic(url,"#{code}~#{i}",false)
    }

    sizes=[]
    sizes=[36,37,38,39,40,41] if cat.include? 'Женская' and page.search('.col-sm-6 label.control-label').text=='Размеры под заказ'
    sizes=[40,41,42,43,44,45] if cat.include? 'Мужская'

    s=page.search('span.im_option')
    if s.count>0
      sizes=s.map(&:text)
    end



    cat_type='53'

    cat_type='318' if cat.include? 'Балетки'
    cat_type='314' if cat.include? 'Босоножки'
    cat_type='1980' if cat.include? 'Сабо'
    cat_type='317' if cat.include? 'Шлепанцы'
    cat_type='313' if cat.include? 'Туфли'
    cat_type='1249' if cat.include? 'Кеды'
    cat_type='315' if cat.include? 'Мокасины'
    cat_type='92' if cat.include? 'Кроссовки'
    cat_type='1420' if cat.include? 'Ботильоны'
    cat_type='90' if cat.include? 'Полусапожки'
    cat_type='89' if cat.include? 'Сапоги'

    if cat.include? 'Мужская'
      cat_type='367'
      cat_type='372' if cat.include? 'Спортивная'
      cat_type='369' if cat.include? 'Туфли'
    end

    cat_type='108160' if cat.include? 'Визитницы'
    cat_type='1179' if cat.include? 'Зажим'
    cat_type='1179' if cat.include? 'Кошельки'
    cat_type='1179' if cat.include? 'Портмоне'
    cat_type='107956' if cat.include? 'Клатчи'

    col=addCollection(cat,cat_type)
    addProduct(col,art+' '+code,name,price,desc,sizes,pics)


  rescue Mechanize::Error => e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def run
    page=@agent.get 'https://www.vm-villomi.ua/login/'

    login_form=page.form_with(action:'https://www.vm-villomi.ua/login/')
    login_form['email']=@login
    login_form['password']=@password
    page = @agent.submit(login_form, login_form.buttons[0])


    urls=['https://www.vm-villomi.ua/zhenskaya-obuv/?limit=5000',
          'https://www.vm-villomi.ua/index.php?route=product/category&path=223&limit=5000',
          'https://www.vm-villomi.ua/evropa/?limit=5000',
          'https://www.vm-villomi.ua/aksesuar/?limit=5000',
          'https://www.vm-villomi.ua/index.php?route=product/category&path=155&limit=5000'
    ]
    urls.each {|url|
      page2=@agent.get url
      page2.search('#content .product-thumb .image a').each {|a|
        processPage(a.attr('href')).to_s
      }
    }

  end

end
