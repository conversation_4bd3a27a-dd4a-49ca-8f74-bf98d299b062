$( document ).ready(function() {

    $(function () {
        $('[data-toggle="tooltip"]').tooltip()

    })

    var $tooltip = $('#fullsize');

    var clicked_checkbox=0;
    var clicked_prod_checkbox=0;
    var clicked_prod_col=0;
    var set_check=true;
    var set_prod_check=true;
    var moveColIds=[]
    var moveProdIds=[]
    var colRenameCid=null
    var colRenameCids=[]
    var deleteCid=null
/*
    $('img').on('mouseenter', function() {
        var img = this,
            $img = $(img),
            offset = $img.offset();

        $tooltip
            .css({
                'top': offset.top,
                'left': offset.left
            })
            .append($img.clone())
            .removeClass('hidden');
    });
    $tooltip.on('mouseleave', function() {
        $tooltip.empty().addClass('hidden');
    });
*/

    $('input.colcheck').on('click', function (e) {
        var i=this.id.replace('colcheck_','')*1;
        console.log(i);
        if (e.shiftKey)
        {
            console.log('Shift');
            a=clicked_checkbox;
            b=i;

            if (i<clicked_checkbox)
            {
                b=clicked_checkbox;
                a=i;
            }

            for(x=a;x<=b;x++)
            {
                console.log('Set');
                $('input#colcheck_'+x).prop('checked',set_check);
            }

        }
        else
        {
            clicked_checkbox=i;
            set_check=$(this).prop('checked');
        }
        updateProductCount();
    });

    $('input.prodcheck').on('click', handleProdClick);

    $('#add_file').on('click', function (e) {
        $('#file_tpl').clone().removeAttr('id').insertBefore('#add_file_block')
    });

        $('#add_invoice_file').on('click', function (e) {
        $('#invoice_file_tpl').clone().removeAttr('id').insertBefore('#add_invoice_file_block')
    });

    $('#tag_select').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
        const tag=$("#tag_select option:selected").text();
        if (clickedIndex!=null)
            $('#tag_select').selectpicker('val',0);
        else
            return

        let moveIds=[]
        $('input.colcheck:checked').each(function (el) {
            cid=$(this).val()
            colName=$("a[href='#collapse"+cid+"'").html().trim()
            moveIds.push(cid)
        })

        pid=e.currentTarget.dataset.pid

        if (moveIds.length>0) {
            $.post("/purchases/"+pid+"/tag_collections",{'tag':tag,'collections[]':moveIds},function(data) {
                location.reload();
            })

        }

    })

    $('#select_collections').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
        const tag=$("#select_collections option:selected").text();
        const val=$('#select_collections').val()
        if (clickedIndex!=null)
            $('#select_collections').selectpicker('val',-1);
        else
            return

        if (val==-2) {
            checkAllCollections()
        } else if (val==-3) {
            clearSelection()
        } else {
            checkTaggedCollections(tag)
        }


    })

    $("#col_select").on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {

        let moveProds=[]
        let moveCols=[]
        moveColIds=[]
        moveProdIds=[]

        const newCid=$("#col_select option:selected").val();
        if (newCid=="-1") return

        const moveTo=$("#col_select option:selected").text();
        $('input.colcheck:checked').each(function (el) {
            cid=$(this).val()
            colName=$("a[href='#collapse"+cid+"'").html().trim()
            moveCols.push(colName)
            moveColIds.push(cid)
        })

        if (moveCols.length>0) {
            $('#col_move_confirm .modal-body p').html('Переместить все товары коллекций</br>'+moveCols.join("<br/>")+"<br/>в коллекцю <strong>"+moveTo+"</strong>?")
            $('#col_move_confirm').modal('show');
        } else {

            $('input.prodcheck:checked').each(function (el) {
                pid=this.dataset.id
                fromCid=this.dataset.col
                prodName=this.dataset.name
                moveProds.push(prodName)
                moveProdIds.push(pid+'|'+fromCid)
            })

            $('#col_move_confirm .modal-body p').html('Переместить товары</br>'+moveProds.join("<br/>")+"<br/>в коллекцю <strong>"+moveTo+"</strong>?")
            $('#col_move_confirm').modal('show');
        }
    });

    $("body").on("click","a.accordion-toggle", function (event) {
        const target=event.currentTarget.hash
        $(target).collapse('toggle')

        var href = this.hash;
        var depId = href.replace("#collapse","");

        if ($('#collapse'+depId).html()!="") return

        var collapse_element = event.target;

        var tpl=collapse_element.dataset.itemTpl
        if (tpl==null) tpl="products"


        if ($('#collapse'+depId+'.in').length>0) return

        let noPics=''
        if (collapse_element.dataset.noPics=="true") noPics="?no_pics=1"
        $.get('/collections/' + depId+'/items/'+tpl+noPics, function(data) {
            $('#collapse'+depId).html(data);
            $('input.prodcheck').on('click', handleProdClick);
            $('[data-toggle="tooltip"]').tooltip()
            $('.image_chooser img').on('click',function (ev) {moveImage(ev)})
        });
    })


    $("body").on("click","span.badge a", function (e) {
        const tag=e.currentTarget.dataset.tag
        const cid=e.currentTarget.dataset.col

        $.post("/collections/"+cid+"/removetag",{tag:tag},function(data) {
            $(e.currentTarget).parent().remove()
        })
        return false
    })

    $('button#move_products').on("click",function (e) {
        const cid=$("#col_select option:selected").val();
        if (cid=="-1") return

        if (moveColIds.length>0) {
            $.post("/collections/"+cid+"/move_collections",{'collections[]':moveColIds},function(data) {
                $("#accordion").html(data)
                $('#col_move_confirm').modal('hide');
            })
        } else if (moveProdIds.length>0) {
            $.post("/collections/"+cid+"/move_products",{'products[]':moveProdIds},function(data) {
                $("#accordion").html(data)
                $('#col_move_confirm').modal('hide');
            })
        }
        //$('#col_move_confirm').modal('hide');
    })

    $('.modal').on('shown.bs.modal', function() {
        $(this).find('[autofocus]').focus();
    });

    $('body').on('click', 'a.rename-link', function (e) {

        var oldNames=[]
        colRenameCids=[]

        $('input.colcheck:checked').each(function (el) {
            const cid=$(this).val()
            const colName=$("a[href='#collapse"+cid+"'").html().trim()
            oldNames.push(colName)
            colRenameCids.push(cid)
        })

        if (colRenameCids.length==0) {
            const cid=e.currentTarget.dataset.cid
            const colName=$("a[href='#collapse"+cid+"'").html().trim()
            oldNames.push(colName)
            colRenameCids.push(cid)
        }

        $('#rename_collection_modal span#old_name').html(oldNames.join("</br>"))

        $('#rename_collection_modal input#new_name').val('')
        $('#rename_collection_modal').modal('show');
        return false
    })

    $('button#rename_collection').on("click",function (e) {
        const newName=$("input#new_collection_name").val().trim()
        const createRenameRule=$("input#create_rename_rule").prop('checked')

        if (newName!='') {
            $.post("/purchases/"+purchaseId+"/rename_collection",{'cids[]':colRenameCids,'new_name':newName,'create_rename_rule':createRenameRule },function(data) {
                $("#accordion").html(data)
                $('#rename_collection_modal').modal('hide');
                //location.reload();
            })
        }
        //$('#col_move_confirm').modal('hide');
    })

    $('button#do_create_collection').on("click",function (e) {
        e.preventDefault();
        const name=$("input#collection_name").val().trim()

        if (name!='') {
            $.post("/purchases/"+purchaseId+"/create_collection",{'name':name},function(data) {
                location.reload();
            })
        }
        //$('#col_move_confirm').modal('hide');
    })

    $('button#do_delete_collection').on("click",function (e) {
        e.preventDefault();

        $.post("/purchases/"+purchaseId+"/delete_collection",{cid:deleteCid},function(data) {
            location.reload();
        })
    })

    $('#delete_collection_modal').on('show.bs.modal', function (event) {
        deleteCid = $(event.relatedTarget).data('cid');
    });

    let editing_text=null
    let editing_text_tag=null
    let editing_link=null
    $('a.edit_purchase_data_button').on("click", (e)=>{
        e.preventDefault();
        $("textarea#text_editor_text").val(e.currentTarget.dataset.text)
        editing_text=e.currentTarget.dataset.textType
        editing_text_tag=e.currentTarget.dataset.tag
        editing_link=e.currentTarget
        $('#text_editor').modal('show')
    })

    $('button#do_save_text').on("click", ()=>{
        editing_link.dataset.text=$("textarea#text_editor_text").val()
        $.post("/purchases/"+purchaseId+"/save_purchase_info_text",{text_type:editing_text,tag:editing_text_tag, text:$("textarea#text_editor_text").val()})
        $('#text_editor').modal('hide')
    })

    //scrollersSetData()
});

function updateProductCount() {
    $('#checked_collections').html($('input.colcheck:checked').length);
    let prodCount = 0
    let activeProdCount = 0

    $('input.colcheck:checked').each(function (el) {
        prodCount+=$(this).attr('data-product-count')*1;
        activeProdCount+=$(this).attr('data-active-product-count')*1;
    })
    $('#checked_products').html(`${activeProdCount} (${prodCount})`);
}

var handleProdClick=function (e) {
    var i=e.target.dataset.counter*1;
    clicked_prod_col=e.target.dataset.col;
    console.log(i);
    if (e.shiftKey)
    {
        console.log('Shift');
        a=clicked_prod_checkbox;
        b=i;

        if (i<clicked_prod_checkbox)
        {
            b=clicked_prod_checkbox;
            a=i;
        }

        for(x=a;x<=b;x++)
        {
            console.log('Set');
            $('input[data-counter="'+x+'"][data-col="'+clicked_prod_col+'"]').prop('checked',set_prod_check);
        }

    }
    else
    {
        clicked_prod_checkbox=i;
        set_prod_check=$(e.target).prop('checked');
    }
}


function clearSelection(e) {
    $('input.colcheck').prop('checked', false);
    $('input.prodcheck').prop('checked', false);
    $('.row').show()
    updateProductCount();
    return false;

}

function checkAllCollections() {
    $('input.colcheck').prop('checked', true);
    $('.row').show()
    updateProductCount();
    return false;
}

function checkTaggedCollections(tag) {
    clearSelection()
    $('input.colcheck').each(function (i,el) {
        if (el.dataset.tags.split('|').includes(tag)) {
            $(el).prop('checked', true);
            $(el).parents('.collection').show()
        } else {
            $(el).parents('.collection').hide()
        }
    })

    updateProductCount();
    return false;
}

function moveImage(ev) {
    let el=ev.target
    let id=el.dataset.id
    let type=el.dataset.type
    let d=null

    if (type=='candidate') {
        d=$(el).parents('.image_chooser').find(".active-images")
        el.dataset.type="active"
        $.post("/move_image/"+id+"/active")
    } else if (type=='active') {
        d=$(el).parents('.image_chooser').find(".candidate-images")
        el.dataset.type="candidate"
        $.post("/move_image/"+id+"/candidate")
    }

    if (d) $(el).appendTo(d)
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            console.log('Copied to clipboard successfully.');
        }, (err) => {
            console.log('Failed to copy the text to clipboard.', err);
        });
    } else if (window.clipboardData) {
        window.clipboardData.setData("Text", text);
    }
}

function scrollersSetData() {
    $('#collection-table-606107').DataTable( {
        ajax:           '/products?collection_id=606107',
        scrollY:        200,
        deferRender:    true,
        scroller:       true
    }  );

}