#encoding:utf-8
require_relative '../framework/order_sender'

module <PERSON>ess<PERSON>
  class Dress37OrderSender < Framework::OrderSender
    def clean
      #basket_page = @agent.get('http://alisafashion.ru/personal/cart/')
      #urls = basket_page.search('.line a.remove').map{|a|a.attr('href').to_s}

      #urls.each{|url|@agent.get(url)}
    end

    def order(articul, sizes)
      puts articul, sizes
      #
      # aws_price_calc_3: 1
      #aws_price_calc_5:
      #aws_price_calc_6: 1
      #aws_price_calc_7:
      #aws_price_calc_8:

      page = @agent.get("http://dress37.ru/product/#{articul.downcase}/")

      page_sizes=Hash[page.search('tr.awspc-field-row').map {|tr| [tr.search('td').first.text.gsub('Размер','').strip,tr.search('td').last.search('input').first.attr('name').to_s]}]

      post_data=[]
      sizes.each do |s,q|
        post_data<<[page_sizes[s],q] if page_sizes.has_key?(s)
      end


      post_id=page.search('input.wpc_product_id').attr('value').to_s
      sim_id=page.search('input.wpc_simulator_id').attr('value').to_s

      ajax_headers = {
          'X-Requested-With' => 'XMLHttpRequest',
          'Accept' => 'application/json, text/javascript, */*',
          'Content-Type' => 'application/json; charset=UTF-8'
      }
      #res=@agent.post "http://dress37.ru/wp-admin/admin-ajax.php?task=price_callback&action=awspricecalculator_ajax_callback&id=#{post_id}&simulatorid=#{sim_id}&page=product",post_data,ajax_headers

      post_data<<['add-to-cart',post_id]

      res=@agent.post "http://dress37.ru/product/#{articul.downcase}/",post_data
      sleep(rand(0.0..1.5)+0.5)

    rescue Mechanize::ResponseCodeError

    end

    def add_csv_data(fields)
      articul = fields['Артикул'].to_s
      size = fields['Размер'].to_s

      @csv_data[articul] = {} unless @csv_data.include? articul

      @csv_data[articul][size]+=1 if @csv_data[articul].include? size
      @csv_data[articul][size]=1 unless @csv_data[articul].include? size
    end
  end
end