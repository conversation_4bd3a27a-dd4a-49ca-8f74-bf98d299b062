#encoding:utf-8
require_relative '../framework/parser'
require_relative 'okean_obuvi_product_config'

module Okean
  class OkeanObuviParserOld < Framework::Parser
    include InvoiceOkean

    attr_accessor :only_bold

    def initialize
      @dlparams={'only_bold' => :checkbox}
      super
    end

    def initialize_settings
      with_url('http://www.okeanobuvi.ru/').
          # with_category_links('.parent:first-child > .nav-child > li > a').
          with_category_links('.item-608 .parent:first-of-type li > a').
          with_category_links('.item-609 .parent:first-of-type li > a').
          with_category_links('.item-610 .parent:first-of-type li > a').
          with_category_links('.item-611 .parent:first-of-type li > a').
          with_category_links('.item-612 > .nav-child > .parent > .nav-child > li > a').
          with_category_links('.item-613 li > a').
          with_pagination('.pagination-next > a').
          with_category(->(category_page) {
            unless category_page.search('h1').first.nil?
              @category = prepare_string category_page.search('h1').first.text
            end
            @category
          }).
          with_product_config(->(pc) {
            pc.with_product_selector('.nm-product-media-container > a').
                with_sizes_table(->(product_page) {
                  table = product_page.search('#size-tab table').first
                  return [] if table.nil?

                  table.search('tbody > tr').map { |tr| tr.search('td').map { |td| prepare_string td.text } }
                })
                .with_sizes_link(->(product_page) {
                  link = product_page.search('#TableSizes').first
                  link.attr('href').to_s unless link.nil?
                })
                .with_create_agent(->(){
                  create_agent
                })
                .with_retail_coefficient(1.75)
                .with_category_type('53')
                .with_category_type('795', 'детские', 'балетки')
                .with_category_type('583', 'детские', 'босоножки')
                .with_category_type('584', 'детские', 'ботинки')
                .with_category_type('537', 'детские', 'дутики')
                .with_category_type('538', 'детские', 'кеды')
                .with_category_type('2172', 'детские', 'мокасины')
                .with_category_type('537', 'детские', 'полусапоги')
                .with_category_type('537', 'детские', 'резиновые')
                .with_category_type('797', 'детские', 'сабо')
                .with_category_type('797', 'детские', 'сандалии')
                .with_category_type('537', 'детские', 'сапоги')
                .with_category_type('862', 'детские', 'сланцы')
                .with_category_type('535', 'детские', 'туфли')
                .with_category_type('793', 'детские', 'угги')
                .with_category_type('801', 'детские', 'кроссовки')
                .with_category_type('318', 'женщин', 'балетки')
                .with_category_type('314', 'женщин', 'босоножки')
                .with_category_type('1420', 'женщин', 'ботильоны')
                .with_category_type('2906', 'женщин', 'валенки')
                .with_category_type('3659', 'женщин', 'дутики')
                .with_category_type('1249', 'женщин', 'кеды')
                .with_category_type('92', 'женщин', 'кроссовки')
                .with_category_type('315', 'женщин', 'мокасины')
                .with_category_type('90', 'женщин', 'полусапоги')
                .with_category_type('1980', 'женщин', 'сабо')
                .with_category_type('314', 'женщин', 'сандалии')
                .with_category_type('89', 'женщин', 'сапоги')
                .with_category_type('2295', 'женщин', 'сланцы')
                .with_category_type('2295', 'женщин', 'тапочки')
                .with_category_type('313', 'женщин', 'туфли')
                .with_category_type('939', 'женщин', 'угги')
                .with_category_type('314', 'женщин', 'босоножки')
                .with_category_type('2295', 'женщин', 'шлепанцы')
                .with_category_type('2295', 'женщин', 'сланцы')
                .with_category_type('1249', 'женщин', 'кеды')
                .with_category_type('89', 'женщин', 'сапоги')
                .with_category_type('90', 'женщин', 'полусапо')
                .with_category_type('352', 'женщин', 'ботинки')
                .with_category_type('128', 'женские', 'куртки')
                .with_category_type('1374', 'женские', 'поло')
                .with_category_type('15', 'женские', 'рубашки')
                .with_category_type('304', 'женские', 'толстовки')
                .with_category_type('23', 'купальники')
                .with_category_type('4678', 'джинсы')
                .with_category_type('545', 'одежда', 'мальчиков')
                .with_category_type('544', 'одежда', 'девочек')
                .with_category_type('114', 'сумки')
                .with_category_type('112', 'бижутерия')
                .with_category_type('1179', 'кошельки')
                .with_category_type('457', 'перчатки')
                .with_category_type('1004', 'постельное')
                .with_category_type('351', 'платки')
                .with_category_type('1004', 'одеяла')
                .with_category_type('1365', 'мужчин', 'мокасины')
                .with_category_type('1250', 'мужчин', 'кеды')
                .with_category_type('372', 'мужчин', 'кроссовки')
                .with_category_type('368', 'мужчин', 'ботинки')
                .with_category_type('368', 'мужчин', 'дутики')
                .with_category_type('1250', 'мужчин', 'кеды')
                .with_category_type('372', 'мужчин', 'кроссовки')
                .with_category_type('1365', 'мужчин', 'мокасины')
                .with_category_type('371', 'мужчин', 'сабо')
                .with_category_type('371', 'мужчин', 'сандалии')
                .with_category_type('2900', 'мужчин', 'сапоги')
                .with_category_type('1833', 'мужчин', 'сланцы')
                .with_category_type('369', 'мужчин', 'туфли')
                .with_category_type('2900', 'мужчин', 'угги')
                .with_category_type('1685', 'мужские', 'джемперы')
                .with_category_type('1685', 'мужские', 'кофты')
                .with_category_type('32', 'мужские', 'свитеры')
                .with_category_type_by_name('314', 'женщин', 'босоножки')
                .with_category_type_by_name('2295', 'женщин', 'шлепанцы')
                .with_category_type_by_name('2295', 'женщин', 'сланцы')
                .with_category_type_by_name('1249', 'женщин', 'кеды')
                .with_category_type_by_name('89', 'женщин', 'сапоги')
                .with_category_type_by_name('90', 'женщин', 'полусапо')
                .with_category_type_by_name('352', 'женщин', 'ботинки')
                .with_category_type_by_name('1365', 'мужчин', 'мокасины')
                .with_category_type_by_name('1250', 'мужчин', 'кеды')
                .with_category_type_by_name('372', 'мужчин', 'кроссовки')
                .with_category_type_by_name('368', 'мужчин', 'ботинки')
                .with_category_type_by_name('20', 'плать', 'женские')
                .with_category_type_by_name('286', 'плащ', 'женские')
                .with_category_type_by_name('301', 'свитер', 'женские')
                .with_category_type_by_name('15', 'рубашка', 'женские')
                .with_category_type_by_name('307', 'туника', 'женские')
                .with_category_type_by_name('2105', 'костюм', 'женские')
                .with_category_type_by_name('1104', 'сарафан', 'женские')
                .with_category_type_by_name('128', 'куртка', 'женские')
                .with_category_type_by_name('1541', 'пуховик', 'женские')
                .with_category_type_by_name('129', 'пальто', 'женские')
                .with_category_type_by_name('4678', 'джинсы', 'женские')
                .with_category_type_by_name('1373', 'футболка', 'женские')
                .with_category_type_by_name('1483', 'брюки', 'женские')
                .with_category_type_by_name('15', 'блузк', 'женские')
                .with_category_type_by_name('1489', 'пиджак', 'женские')
                .with_category_type_by_name('52', 'жилет', 'женские')
                .with_category_type_by_name('302', 'джемпер', 'женские')
                .with_category_type_by_name('303', 'кардиган', 'женские')
                .with_category_type_by_name('1489', 'жакет', 'женские')
                .with_category_type_by_name('26', 'комбинезон', 'женские')
                .with_category_type_by_name('1360', 'шорты', 'женские')
                .with_category_type_by_name('14', 'топ', 'женские')
                .with_category_type_by_name('1489', 'блейзер', 'женские')
                .with_category_type_by_name('19', 'юбка', 'женские')
                .with_category_type_by_name('850', 'одежда', 'женская')
                .with_category_type_by_name('915', 'пальто', 'мужские')
                .with_category_type_by_name('33', 'куртка', 'мужские')
                .with_category_type_by_name('33', 'парка', 'мужские')
                .with_category_type_by_name('957', 'ветровка', 'мужские')
                .with_category_type_by_name('916', 'пуховик', 'мужские')
                .with_category_type_by_name('2497', 'плащ', 'мужские')
                .with_category_type_by_name('1865', 'жилет', 'мужские')
                .with_category_type_by_name('4679', 'джинсы', 'мужские')
                .with_category_type_by_name('877', 'шорты', 'мужские')
                .with_category_type_by_name('30', 'футболка', 'мужские')
                .with_category_type_by_name('120', 'рубашка', 'мужские')
                .with_category_type_by_name('906', 'шарф', 'мужские')
                .with_category_type_by_name('103', 'пиджак', 'мужские')
                .with_category_type_by_name('32', 'пуловер', 'мужские')
                .with_category_type_by_name('1444', 'толстовка', 'мужские')
                .with_category_type_by_name('1685', 'кардиган', 'мужские')
                .with_category_type_by_name('120', 'сорочка', 'мужские')
                .with_category_type_by_name('1686', 'поло', 'мужские')
                .with_category_type_by_name('870', 'одежда', 'мужские')
                .with_category_type_by_name('552', 'платье', 'девочек')
                .with_category_type_by_name('962', 'спортивный', 'костюм', 'девочек')
                .with_category_type_by_name('557', 'бермуды', 'девочек')
                .with_category_type_by_name('960', 'куртка', 'девочек')
                .with_category_type_by_name('556', 'футболка', 'девочек')
                .with_category_type_by_name('556', 'майка', 'девочек')
                .with_category_type_by_name('555', 'топы', 'девочек')
                .with_category_type_by_name('566', 'рубашка', 'мальчиков')
                .with_category_type_by_name('1323', 'плавки', 'мальчиков')
                .with_category_type_by_name('569', 'шорты', 'мальчиков')
                .with_category_type_by_name('568', 'футболка', 'мальчиков')
          }, Okean::OkeanObuviProductConfig)
    end

    def process_category(link)
      link = "http://www.okeanobuvi.ru#{link}" unless link.include? 'http'
      super(link)
    end

    def get_next_link(category_page)
      link = super(category_page)
      link = "http://www.okeanobuvi.ru#{link}" if not link.nil? and not link.include? 'http'
      link
    end
  end
end
