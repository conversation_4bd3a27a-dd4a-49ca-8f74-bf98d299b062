class LoadPurchaseData
  include SpAuthorize

  def save (purchase,pid,variant:nil)
    authorize

    variant='' if variant.nil?

    page=@agent.get "https://www.100sp.ru/org/purchase/edit?pid=#{pid}"
    name=page.at('input#PurchaseEditForm_name').attr('value').to_s
    megapurchase_id=''
    if page.at('select#PurchaseEditForm_megapurchases_id option[selected]')
      megapurchase_id=page.at('select#PurchaseEditForm_megapurchases_id option[selected]').attr('value').to_s
    end

    if megapurchase_id==''
      if page.at('.control-group label:contains("Мегапокупка")')
        a=page.at('.control-group label:contains("Мегапокупка")').next_element
        megapurchase_id=a.attr('href').to_s.gsub(/[^0-9]/,'')
      end
    end
    fee=page.at('select#PurchaseEditForm_fee option[selected]').attr('value').to_s
    annotation=page.at('textarea#PurchaseEditForm_annotation').text.to_s
    desc=page.at('textarea#PurchaseEditForm_description_good').text.to_s
    rules=page.at('textarea#PurchaseEditForm_rules').text.to_s
    info=page.at('textarea#PurchaseEditForm_info_user').text.to_s
    contract_info=page.at('textarea#PurchaseEditForm_contract_info').text.to_s

    prepay=page.at('input#PurchaseEditForm_prepay_percent').attr('value').to_i if page.at('input#PurchaseEditForm_prepay_percent')
    prepay_rating='0'
    if page.at('select#PurchaseEditForm_prepay_rating_border option[selected]')
      prepay_rating=page.at('select#PurchaseEditForm_prepay_rating_border option[selected]').attr('value').to_s
    end

    prepay_negative='0'
    if page.at('select#PurchaseEditForm_prepay_negative_count_border option[selected]')
      prepay_negative=page.at('select#PurchaseEditForm_prepay_negative_count_border option[selected]').attr('value').to_s
    end

    type=1
    label=page.search('.control-group label').find{|l| l.text.to_s.strip=='Тип'}
    if label
      t=label.parent.text.to_s
      type=3 if t.include? 'Shop'

    end

    size_pic=nil
    a=page.at('a.image-editor[data-type=purchase-size-table]')
    if a and a.attr('data-imgurl')
      size_pic=a.attr('data-imgurl').to_s
    end

    size_pic2=nil
    a=page.search('a.image-editor[data-type=purchase-size-table]')
    if a[1] and a[1].attr('data-imgurl')
      size_pic2=a[1].attr('data-imgurl').to_s
    end

    page=@agent.get "https://www.100sp.ru/ajaxUploader/getImageEditorMultipleOptions?pid=#{pid}"
    pic_data=JSON.parse(page.body)
    groups={}
    pic_data['data']['collectionGroups'].each do |g|
      groups[g['id'].to_i]=g['name']
    end

    begin
      dl = Object.const_get(purchase.dlclass).new
      dl.before
    rescue => e
      puts e.message
      dl=nil
    end

    pics=[]
    pic_data['data']['images'].each_with_index do |img,i|
      url=img['originalurl']
      if dl
        p=dl.savePic(img['originalurl'],"#{purchase.id}~mainpic~#{variant}~#{i}",true,true, false, :active, nil, force_overwrite:true)
        #url=p.path.gsub('/mnt/spup','https://spup.primavon.ru')
        #TODO: hack
        url=p.path.gsub('/mnt/spup','http://spupru.primavon.ru')
        source_url=img['originalurl']
      end
      d={url:url,group:groups[img['priorityCollectionGroupId'].to_i],desc:img['description'],source_url:source_url}
      pics<<d

    end


    d={
      name:name,
      megapurchase_id:megapurchase_id,
      fee:fee,
      annotation:annotation,
      desc:desc,
      rules:rules,
      info:info,
      prepay:prepay,
      prepay_rating:prepay_rating,
      prepay_negative:prepay_negative,
      pics:pics,
      size_pic: size_pic,
      size_pic2: size_pic2,
      contract_info:contract_info,
      type:type,
      saved_from:pid,
      saved_at:DateTime.now
    }

    if variant
      purchase.purchase_data={} unless purchase.purchase_data
      if d[:megapurchase_id].to_s=='' and purchase.purchase_data and purchase.purchase_data[variant]
        d[:megapurchase_id]=purchase.purchase_data[variant]['megapurchase_id']
      end
      purchase.purchase_data[variant]=d
    else
      d[:megapurchase_id]=purchase.purchase_data['megapurchase_id'] if d[:megapurchase_id].to_s=='' and purchase.purchase_data
      purchase.purchase_data=d
    end


    purchase.save

  end

  def get_max_purchase_number(mega_id)
    page=@agent.get "https://www.100sp.ru/megapurchase/org/#{mega_id}"
    ret=1
    page.search('.connected-purchases table a[href*="/purchase/"] span.purchase-number').each do |num|
      ret=num.text.to_i if num.text.to_i>ret
    end
    ret
  end

  def upload_purchase_pictures(purchase,pid,variant)
    authorize

    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

    page=@agent.get "https://www.100sp.ru/ajaxUploader/getImageEditorMultipleOptions?pid=#{pid}"
    pic_data=JSON.parse(page.body)
    puts pic_data
    groups={}
    pic_data['data']['collectionGroups'].each do |g|
      groups[g['name']]=g['id'].to_i
    end

    puts groups

    pic_data['data']['images'].each do |img|
      d={picid: img['id'], connected_id: pid, connected_type: 'purchase'}
      @agent.post('https://www.100sp.ru/ajaxUploader/deletePicture',d,ajax_headers)
      sleep(0.7)
    end


    data=purchase.purchase_data[variant]

    data['pics'].each do |pic|
      #TODO: HACK
      pic['url'].gsub!('http://spup.primavon.ru','http://spupru.primavon.ru')
      pic['url'].gsub!('https://spup.primavon.ru','http://spupru.primavon.ru')
      pic['url'].gsub!('https://**************','http://spupru.primavon.ru')

      puts "https://www.100sp.ru/org/good/uploadPicture/#{pid}?type=purchase&isExtra=1&_url=#{pic['url']}"
      puts pic

      page=@agent.post("https://www.100sp.ru/org/good/uploadPicture/#{pid}?type=purchase&isExtra=1&_url=#{pic['url']}",
                       {img_size: 'original', url: pic['url']}, ajax_headers)
      data=JSON.parse(page.body)
      puts data
      if data['ok']
        pic_id=data['data'][0]['picid']
        group_id=groups[pic['group']]
        if group_id
          d={id:pid,type:'purchase',picture_id: pic_id,'value[description]':pic['desc'],'value[priorityCollectionGroupId]':group_id}
          @agent.post("https://www.100sp.ru/ajax/setPictureAttributes",d,ajax_headers)
        end
      end
      sleep(0.2)
    end
  end

  def upload(purchase,pid,only_pics:false,skip_pics:false,variant:nil )
    authorize

    variant='' if variant.nil?

    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

    page=@agent.get "https://www.100sp.ru/org/purchase/edit?pid=#{pid}"

    data=purchase.purchase_data
    data=purchase.purchase_data[variant] unless variant.strip.blank?
    data=purchase.purchase_data[''] if purchase.purchase_data['']

    raise "Информация о закупке не загружена" unless data

    unless only_pics
      Rails.logger.info "Uploading data"
      num=0
      num=get_max_purchase_number(data['megapurchase_id']) if data['megapurchase_id']
      num+=1

      page2=@agent.get "https://www.100sp.ru/org/purchase/paymentMethods/index?pid=#{pid}"
      form=page2.form_with(:id=>'yw0')
      #form['paymentMethods[]']=[4129,2810,6716,6971,7547,7948,9127,10289, 12043]

      form.checkboxes.each do |checkbox|
        checkbox.checked = false
      end

      form['paymentMethods[]']=[12390, 12389, 12391, 12393, 12392, 12395, 12394 ] #, 4462
      form['paymentMethods[]'].each do |id|
        form.checkbox_with(id:"cb_#{id}").check if form.checkbox_with(id:"cb_#{id}")
        form.checkbox_with(id:"#{id}").check if form.checkbox_with(id:"#{id}")
        form.checkbox_with(id:"#{id}-required").check if form.checkbox_with(id:"#{id}-required")
      end

      #form.checkbox_with(id:'paymentMethodsSpFeePercent_4129').check if form.checkbox_with(id:'paymentMethodsSpFeePercent_4129')
      #form.checkbox_with(id:'paymentMethodsSpFeePercent_7948').check if form.checkbox_with(id:'paymentMethodsSpFeePercent_7948')
      form.checkbox_with(id:'PurchaseEditForm_instant_payment_deny').uncheck if form.checkbox_with(id:'PurchaseEditForm_instant_payment_deny')
      form.checkbox_with(id:'PurchaseEditForm_waitOrdersPaymentDeny').uncheck if form.checkbox_with(id:'PurchaseEditForm_waitOrdersPaymentDeny')
      form.checkbox_with(id:'PurchaseEditForm_showRecommendedPriceCollection').check if form.checkbox_with(id:'PurchaseEditForm_showRecommendedPriceCollection')

      res=@agent.submit(form, form.buttons.first)

      form=page.form_with(:id=>'purchase-edit-form')
      form['PurchaseEditForm[purchaseNumber]']="#{num}-#{purchase.id}"
      form['PurchaseEditForm[name]']=data['name']
      form['PurchaseEditForm[megapurchases_id]']=data['megapurchase_id']
      form['PurchaseEditForm[fee]']=data['fee']
      form['PurchaseEditForm[annotation]']=data['annotation']
      form['PurchaseEditForm[description_good]']=data['desc']
      form['PurchaseEditForm[rules]']=data['rules']
      form['PurchaseEditForm[info_user]']=data['info_user']
      form['PurchaseEditForm[contract_info]']=data['contract_info']
      form['PurchaseEditForm[prepay_percent]']=data['prepay'] if data['prepay']
      form['PurchaseEditForm[prepay_rating_border]']=data['prepay_rating']
      form['PurchaseEditForm[prepay_negative_count_border]']=data['prepay_negative'] if data['prepay_negative']
      #form['prepayPaymentMethods[]']=[4129,2810,6716]

      Rails.logger.info form
      #res=@agent.submit(form, form.buttons.first)
      res=@agent.post("https://www.100sp.ru/org/purchase/save?pid=#{pid}",form.build_query,ajax_headers)
      puts res.body
      Rails.logger.info res.body

      page2=@agent.get("https://www.100sp.ru/org/purchase/distributors/index?pid=#{pid}")
      form=page2.forms_with(action:"/org/purchase/distributors/index?pid=#{pid}")[1]
      if form
        vals=page2.search('input[type=checkbox][checked][id^=cb_]').map{|i| i.attr('value').to_s}
        vals<<'17453' unless vals.include? '17453'
        form.field_with(id:"new_distributors").value=vals.join(',')
        @agent.submit(form, form.buttons.first)
      end
    end



    unless skip_pics
      upload_purchase_pictures(purchase,pid,variant)
    end

    if data['size_pic']
      url=data['size_pic']
      @agent.post("https://www.100sp.ru/ajaxUploader/uploadPicture",
                  {url: url,  id:pid, type: 'purchase-size-table'}, ajax_headers)
    end

    if data['size_pic2']
      url=data['size_pic2']
      @agent.post("https://www.100sp.ru/ajaxUploader/uploadPicture",
                  {url: url,  id:pid, type: 'purchase-size-table'}, ajax_headers)
    end

    Rails.logger.info data

    if variant
      if variant==''
          purchase.purchase_data={'':data}
        else
          purchase.purchase_data[variant]=data
      end
    else
      purchase.purchase_data=data
    end

    purchase.save
  rescue Mechanize::ResponseCodeError => exception
    if exception.response_code == '403'
      page = exception.page
    end
  end

  def check_loaded_data
    Purchase.where.not(purchase_data:nil).where("downloaded_at>'2022-01-01'").each do |p|
      vars=p.get_variants
      p.purchase_data.slice!(*vars)
      p.save
    end

    Purchase.where.not(purchase_data:nil).where("downloaded_at>'2022-01-01'").each do |p|
      next if p.purchase_data.keys.count>0
      puts "#{p.id} #{p.name}"
    end;nil

  end
end