#encoding: utf-8

require 'downloader'
require 'spreadsheet'

class Defreeze < Downloader
  attr_accessor :price_file1
  attr_accessor :price_file2

  def initialize
    @dlparams={'price_file1'=>:file,'price_file2'=>:file}


    super
  end

#encoding: utf-8

  require 'mechanize'
  require 'spreadsheet'

  def process_page(url)
    puts url
    page = @agent.get url

    cat=page.search('.breadcrumbs a')[-1].text.strip
    puts cat

    art=page.search('.tovar span')[0].text.strip.gsub('Р','P')
    puts art

    name=page.search('.tovar h1')[0].text.strip
    puts name

    cat+=' - '+name if not cat=='Распродажа' and not cat=='аксессуары'

    props=Hash.new
    page.search('ul.options li').each { |li|
      props[li.search('p').text.strip]=li.search('span').text.strip
    }

    desc=page.search('p.description').text.strip

    desc2=props.map {|key, value| "#{key}: #{value}"}.join '. '

    desc=desc2+'. '+desc

    desc.gsub!("\r",' ')
    desc.gsub!("\n",' ')
    desc.gsub!(' ',' ')
    desc.gsub!(/ +/,' ')

    colors=Hash.new
    pics=Hash.new
    page.search('ul.colors_tovar li').each { |li|
      color_id=li.attr('data-title').to_s.downcase
      color=li.search('p').text.strip
      color.gsub!(/\(.*?\)/,'')
      color.strip!

      colors[color_id]=color

      pics[color]=[]
      page.search("div.slider_big .slider_tovar_big.#{color_id} li").each { |li2|
        pics[color]<< li2.attr('data-url').to_s
      }
    }

    puts desc
    puts pics

    cat_type='128'
    cat_type='129' if cat.include? 'Пальт'

    cat_type='25' if cat.include? 'ксессуар'

    cat_type='33' if cat.include? 'муж'

    added=false
    pics.each { |color,p|
      next if @prices[art].nil?
      next if @sizes["#{art}#{color}"].nil?
      added=true
      puts "Add product art #{art} cat #{cat}, name #{name}, color #{color} price #{@prices[art]}, sizes #{@sizes["#{art}#{color}"]}, pics #{p}"
      col=addCollection(cat,cat_type)

      pics=[]
      p.each_with_index  {|url,i|
        fn=art.gsub('/','_')+'~'+color.gsub('/','_')+'~'+i.to_s
        pics<<savePic(url,fn,false)
      }


      addProduct(col,art,name+", цвет "+color,@prices[art],desc,@sizes["#{art}#{color}"],pics)
    }
    puts "Not found in price art #{art} cat #{cat}, name #{name}" if not added

    #@goods[art]={:name=>name,:cat=>cat,:desc=>desc,:pics=>pics}
  end

  def process_cat(url, process_pages=true)
    puts url


    page = @agent.get url

    page.search('.content ul.catalog > li > a').each {|a|
      process_page a.attr('href').to_s
    }

    if process_pages
      page.search('.nav a')[0..-2].each {|a|
        process_cat(a.attr('href').to_s,false)

      }
    end

    return
  end

  def read_file(f)
    book = Spreadsheet.open f

    sheet = book.worksheets[0]
    start_row=0
    sheet.rows.each_with_index do |row,i|
      next if row[1].nil? or row[2].nil? or row[4].nil?
      price=row[4]
      price=row[3] if not row[3].nil?

      if /(.*?) (De|FORSAZH|сумка)(.*?),? р\. ?(.*)/=~row[1]
        art=$1
        size=$4
        t=$3
        x=t.split(',')
        color=x[-1].strip
        color=x[-2].strip if color.include? 'мех'
        color.gsub!(%r![^a-z/. ]!,'')
        color.strip!
        size.gsub!(/\(.*?\)/,'')
        size.strip!
      end

      if /(.*?) (De|FORSAZH|сумка).*? размер (.*?), ?(.*)/=~row[1]
        art=$1
        size=$3
        color=$4
        art.gsub!(' ','')
      end

      next if color.nil?

      art.gsub!('Р','P')
      art.gsub!(' ','')

      color.downcase!
      puts art
      puts color
      puts size

      if art.include? '('
        (art,art2)=art.split('(')
        art2.gsub!(')','')
        @sizes["#{art2}#{color}"]=[] if @sizes["#{art2}#{color}"].nil?
        @sizes["#{art2}#{color}"]<< size
        @prices[art2]=price
      end

      @sizes["#{art}#{color}"]=[] if @sizes["#{art}#{color}"].nil?
      @sizes["#{art}#{color}"]<< size
      @prices[art]=price

      puts
    end
  end

  def run
    if @agent==nil then before end
    @goods=Hash.new

    @sizes=Hash.new
    @prices=Hash.new

    read_file @price_file1 if not @price_file1.nil?
    read_file @price_file2 if not @price_file2.nil?

    page = @agent.get('http://defreeze.ru/')
    page.search('ul.menu > li > a').each do |a|
      process_cat a.attr('href').to_s
    end
    process_cat 'http://defreeze.ru/catalog/dlya-zhenshchin/grand/'
    puts @goods.to_json
  end

end