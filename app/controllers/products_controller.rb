class ProductsController  < ApplicationController
  #before_action :authenticate_user!
  #skip_before_action :authenticate_user
  before_action :authenticate_user! unless Rails.env.development?
  skip_before_action :verify_authenticity_token
  before_action :set_product, only: [:show, :update, :destroy,:copy,:get_operations]

  def index
    #current_user=User.find 1
    page=params[:page].to_i || 0
    per_page=params[:per_page].to_i || 20
    per_page=200 if per_page==0

    per_page = ********* if per_page == -1

    @products=[]
    if (params[:collection_id])
      col_id=params[:collection_id].to_i
      c=Collection.where(id:col_id).includes(:products).limit(per_page).offset(page * per_page)
      raise 'Wrong collection' unless c[0].purchase.company.id==current_user.company.id
      @products=c[0].products if c[0]
      @count=@products.count
    else
      @products=Product.joins(:purchase).where(purchases: {user_id:current_user.id}).order(:id).limit(per_page).offset(page * per_page)
      @count=Purchase.joins(:products).where(user_id:current_user.id).count
    end

    res=[]
    @products.as_json.each do |p|
      prod=[p['id'],p['name']]
      #      if p['pics']
      #        p['small_pic'] = p['pics'].gsub('/mnt/spup2/images/', '/img/100/').split('~~~').first
      #        p['pic'] = p['pics'].gsub('/mnt/spup2/images/', '/img/640/').split('~~~').first
      #        p['small_pic'] = p['small_pic'].gsub('/mnt/spup/images/', '/img/100/')
      #        p['pic'] = p['pic'].gsub('/mnt/spup/images/', '/img/640/')
      #        if Rails.env.development?
      #          p['small_pic'] = 'http://spup.primavon.ru' + p['small_pic']
      #          p['pic'] = 'http://spup.primavon.ru' + p['pic']
      #        end
      #        p.delete('pics')
      #end
      res<<p
    end

    #render json: {count:@count,page:page, products: res}
    render json: {'aaData'=> res}
  end

  def show
    #current_user=User.find 1
    p=Product.find params[:id]
    json=p.as_json(except: :pics)

    if p.pics
      json['small_pic'] = p.pics.gsub('/mnt/spup2/images/', '/img/100/').split('~~~').first
      json['pic'] = p.pics.gsub('/mnt/spup2/images/', '/img/640/').split('~~~').first
      json['small_pic'] = json['small_pic'].gsub('/mnt/spup/images/', '/img/100/')
      json['pic'] = json['pic'].gsub('/mnt/spup/images/', '/img/640/')
      if Rails.env.development?
        json['small_pic'] = 'http://spup.primavon.ru' + json['small_pic']
        json['pic'] = 'http://spup.primavon.ru' + json['pic']
      end
    end

    raise 'Error' if p.purchase.user_id!=current_user.id
    render json: {product:json}
  end

  def update
    @product.name=params[:name]
    @product.sku=params[:sku]
    #@product.stock={}
    #params[:stock].each do |size,d|
    #@product.stock[size]={q:d['q'], buy_price}
    #end
    @product.save

    render json: {status: :ok,product:p}
  end

  def copy
    cols=@product.collections
    @product=@product.dup
    @product.sku=params[:new_sku]
    @product.art=params[:new_sku]
    @product.collections=cols
    @product.stock={}
    @product.save

    render json: {status: :ok,id:@product.id}
  end

  def get_operations
    ret=[]
    InventoryDocumentLine.joins(:inventory_document).where(product_id:@product.id).merge(InventoryDocument.where(posted:true)).order(:created_at).each do |l|
      ret<<l.as_json(include: :inventory_document)
    end
    render json: {status: :ok,data: ret}
  end

  def set_product
    @product = Product.find(params[:id])
  end

end