#encoding:utf-8


module Gonkopt
  class GonkoptOrderSender < Framework::OrderSender

    def sign_in
      url = "https://gonkopt.ru/index.php?route=account/login"
      page=@agent.get url
      login_form=page.forms[1]
      login_form.email="<EMAIL>"
      login_form.password="270279"

      page = @agent.submit(login_form, login_form.buttons.first)

      page.body
    end


    def process
      sign_in

      super
    end

    def order(articul, amount)
      debug_func "#{articul}, #{amount}"

      code=articul.split.first

      page=@agent.get "https://gonkopt.ru/index.php?route=product/product&product_id=#{code}"

      return if page.search('form.product__info').count==0

      if page.search('div.product__price-vip span.price-new').first
        price = page.search('div.product__price-vip span.price-new').text.gsub(/[^\d.]/, '').to_f.ceil
      else
        price = page.search('div.product__price-vip').text.gsub(/[^\d.]/, '').to_f.ceil
      end

      min_order=page.search('input[name=quantity]').first.attr('data-minimum').to_s.to_i

      if min_order>1
        return if min_order.to_f/amount.to_f>2
        if amount.to_i%min_order>0
          amount=((amount.to_i/min_order)+1)*min_order
        end

      end

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}
      data={product_id: code, quantity: amount}
      res=@agent.post('https://gonkopt.ru/index.php?route=checkout/cart/add',data,ajax_headers)

    rescue Exception => e
      add_error_csv_data('Error adding to cart', articul)
      debug_func "Error: #{e}"

      #debug_func "ADDED DATA: length #{ids.length}, total cost length #{cost.length}"
    end
  end
end
