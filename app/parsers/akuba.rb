#encoding: utf-8

require 'downloader'
#require 'json'
#require 'logger'
#require 'unicode_utils/downcase'
require 'spreadsheet'

class Akuba < Downloader
  attr_accessor :price_file
  attr_accessor :meh_price_file

  def initialize
    @dlparams={'price_file'=>:file,'meh_price_file'=>:file}

    @pics=Hash.new {|hash,key| hash[key]=[] }

    super
  end



  def getPic(url)
    page = @agent.get(url)

    page.search('.entry a').each {|a|
      href=a.attr('href').to_s
      return href if /jpg$/i=~href
    }

    page.search('img[@itemprop="image"]').each do |img|
      return img.attr('src')
    end
    return nil

  end


  def get_site_url(art)
    page=@agent.get("https://akuba.ru/?s=#{art}")
    href=page.search('#content .post h2.title a').first
    return nil if href.nil?
    page=@agent.get(href.attr('href'))

    t=page.search('select[name=Артикул] option').first

    art2=nil
    art2=t.text.strip if t

    unless art2
      t=page.search('ul.red-dots-list strong').first
      art2=t.text if t
    end

    return nil if art!=art2

    page.search('.entry a').each {|a|
      href=a.attr('href').to_s
      return href if /jpg$/i=~href
    }

    page.search('img[@itemprop="image"]').each do |img|
      return img.attr('src')
    end
    return nil
  end

  def get_pic(type,art)
    (a,c)=art.strip.split('/')
    ret=nil
    if type=='AKUBA CLASSIC'
      ret=['akuba_classic_1.jpg','akuba_classic_2.jpg']
    elsif type=='AKUBA PLATINUM'
      ret=['pl bk2.jpg','pl bk3.jpg','pl bg2.jpg','pl bg.jpg','pl gr4.jpg']
    elsif type=='AKUBA VERONA'
      ret=['verona dark bg.jpg','verona bg2.jpg','verona bk bl.jpg','verona bk rd.jpg','verona bk2.jpg']
    elsif type=='AKUBA PLATINUM+'
      ret=['plp bk4.jpg','plp gr4.jpg','plp gr3.jpg','plp bk3.jpg']
    elsif type=='AKUBA STYLE'
      ret=['style bk3.jpg','style bk7.jpg','style bk6.jpg','style bk5.jpg','style bk4.jpg']
    elsif type=='AKUBA ELITE'
      ret=['elite wh bk.jpg','elite bk rd2.jpg','elite bg ye.jpg','elite bk3.jpg','elite bk2.jpg','elite bk rd.jpg','elite bk.jpg']
    elsif type=='AKUBA VEGAS'
      ret=['vegas br.jpg','vegas2.jpg','vegas bk gr.jpg','vegas wh2.jpg']
    elsif type=='AKUBA MONTANA'
      ret=['montana bg2.jpg','montana wh.jpg','montana bk3.jpg','montana.jpg','montana bk2.jpg']
    elsif art.start_with? 'GLAM5'
      ret=['glam1.jpg','glam b br.jpg','glam b bk.jpg','glam b gr.jpg','glam b bg.jpg','glam br.jpg','glam3.jpg','glam2.jpg']
    elsif art.start_with? 'GLAM-BACK'
      ret=['glam b br.jpg','glam b bk.jpg','glam b gr.jpg','glam b bg.jpg']
    elsif art.start_with? 'GLAM-SEAT'
      ret=['glam seat br.jpg','glam seat gr.jpg','glam seat bk.jpg','glam seat bg.jpg']
    elsif art.start_with? 'GLAM'
      ret=['glam1.jpg','glam br.jpg','glam3.jpg','glam2.jpg']
    elsif art.start_with? 'FLAX5'
      ret=['flax4.jpg','flax3.jpg','flax2.jpg','flax1.jpg','flax b3.jpg','flax b2.jpg','flax b1.jpg']
    elsif art.start_with? 'FLAX'
      ret=['flax4.jpg','flax3.jpg','flax2.jpg','flax1.jpg']
    elsif art.start_with? 'JASTIN'
      ret=['jastin_gr.jpg','jastin_bl.jpg']
    end

    return ret.map {|f| "http://spup.primavon.ru/images/AkubaDl/#{f}"} if ret
  end

  def process_file(f)
    puts f
    book = Spreadsheet.open f

    sheet1 = book.worksheet 0

    colName=''

    sheet1.rows[6..-1].each_with_index do |row,i|


      puts row
      type=row[1]
        art=row[2]
        name=row[3]
        desc=row[4]
        price=row[7]

      @log.info art

      next if desc.nil? and row[1].nil?

      if art.nil? and name.nil?
        colName=desc.strip if desc and desc!=''
        colName=row[1].strip if row[1] and row[1]!=''
        next
      end

      next if art.nil?

      puts row.format(1).pattern_fg_color

      next unless row.format(1).pattern_fg_color==:border or row.format(1).pattern_fg_color==:white

      begin
        url=nil

        picurls=get_pic(type,art)

        unless picurls
          if row[3].instance_of? (Spreadsheet::Link)
            url=row[3].url.split("\x00")[0]
            picurls=[getPic(url)]
          else
            picurls=[get_site_url(art)]
          end
        end

        #next unless picurls

        pics=[]
        if picurls

          picurls.each_with_index do |picurl,k|
            next if picurl.nil?
            pics<<savePic(picurl,"#{i}~#{k}",true)
          end
        end

        col=addCollection(colName,'100001')
        addProduct(col,art.strip,"#{name.strip} #{type}",price,desc,[],pics)
      rescue Mechanize::Error => e
        @log.error e
      end

    end
  end

  def process_meh_page(url)
    page = @agent.get(url)
    name=page.search('.title h2').text
    if /((SIT|P|T|N)-[0-9A-Z ]+)/=~name
      art=$1.strip
    else
      return
    end
    art.gsub!('к','k')
    art.gsub!('-',' ')
    desc=page.search('.entry p')[0].text
    @desc[art]=desc
    pics=[]
    page.search('.entry a').each_with_index do |a,i|
      if a.attr('href').include? '.jpg'
        pics<<savePic(a.attr('href'),"#{art}~#{i}",true)
      end
    end
    @meh_pics[art]=pics

    puts art
  end

  def dl_meh_pics


    url='http://avtogigant.ru/'
    while not url.nil?
      page = @agent.get(url)
      page.search('.btitle h2 a').each do |a|
        process_meh_page(a.attr('href'))
      end
      url=nil
      url=page.search('a.nextpostslink')[0].attr('href') if page.search('a.nextpostslink').length>0

    end



  end

  def process_file_meh(f)

    @meh_pics=Hash.new {|hash,key| hash[key]=[] }
    dl_meh_pics

    puts f
    book = Spreadsheet.open f

    sheet1 = book.worksheet 0

    colName=''

    sheet1.rows[1..-1].each_with_index do |row,i|

      art=row[0]
      desc=row[1]
      price=row[2]
      stock=row[4]

      desc='' if desc.nil?

      desc+=' '+@desc[art] if not @desc[art].nil?

      puts art

      puts desc
      puts price
      puts stock
      puts @meh_pics[art]

      pic=@meh_pics[art]

      next if not stock.nil?

      @log.info art

      if art.nil? or art=='Артикул'
        colName=desc.strip
        next
      end

      if not @meh_pics.has_key? art or @meh_pics[art].nil? or @meh_pics[art].empty?
        art2=art.gsub('-',' ')
        /([A-Z]+ [0-9]+)/=~art2
        art2=$1
        pic=@meh_pics[art2]
      end

      puts colName

      col=addCollection(colName,'100001')
      puts "addProduct(#{col},#{art.strip},'',#{price},#{desc},[],#{pic})"
      addProduct(col,art.strip,'',price,desc,[],pic)
    end
  end


  def run
    if @agent==nil then before end
    @agent.agent.http.verify_mode = OpenSSL::SSL::VERIFY_NONE

    @desc=Hash.new('')

  #AvtoGigant2013
    #page = @agent.get('http://akuba.ru/bez-rubriki/prajs-list/')
    #login_form=page.forms[0]
    #login_form.post_password='128500AKUBA'

    #page = @agent.submit(login_form, login_form.buttons.first)

    #@agent.download('http://akuba.ru/wp-content/uploads/Prays-aktualnyiy.xls','/tmp/akuba.xls')

    process_file(@price_file) if not @price_file.nil?
    process_file_meh(@meh_price_file) if not @meh_price_file.nil?

  end

end