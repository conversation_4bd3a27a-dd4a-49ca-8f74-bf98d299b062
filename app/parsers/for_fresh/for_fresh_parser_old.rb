#encoding:utf-8
require_relative '../framework/parser'
require_relative '../framework/site/excel_parser'
require_relative 'for_fresh_product_config'
require 'rubyXL'

module ForFresh
  class ForFreshParserOld < Framework::Parser
    include TableUtils

    def initialize
      @purchase_tags = ['Продукты', 'Косметика']
      super
    end

    def initialize_settings
      with_product_config(->(pc) {
        pc.with_articul(->(product_page) {
          span=product_page.at('span.ci-article__value')
          return nil unless span
          span.text
        })
          .with_category(->(product_page) {

            art = product_page.search('span.ci-article__value').first.text

            catname = nil
            if product_page.search('nav.crumbs a.crumbs__link').to_a.count > 4
              catname = product_page.search('nav.crumbs a.crumbs__link span.crumbs__title').to_a.values_at(3, 4).map { |a| prepare_string a.text }.join '-'
            elsif product_page.search('nav.crumbs a.crumbs__link').to_a.count == 3
              catname = prepare_string product_page.search('nav.crumbs a.crumbs__link span.crumbs__title').to_a.last.text
            elsif product_page.search('nav.crumbs a.crumbs__link').to_a.count == 4
              catname = product_page.search('nav.crumbs a.crumbs__link span.crumbs__title').to_a.values_at(2, 3).map { |a| prepare_string a.text }.join '-'
            else
              catname = 'Другое'
            end

            #catname='!'+catname if product_page.search('nav.crumbs a').to_a[2].text=='Продукты' or product_page.search('nav.crumbs a').to_a[2].text=='Для здоровья'
            cat1 = product_page.search('nav.crumbs a').to_a[2].text
            cat2 = product_page.search('nav.crumbs a').to_a[3].text if product_page.search('nav.crumbs a').to_a[3]
            if cat1 == 'Продукты' or cat1 == 'Для здоровья' or cat2 == 'Детское питание' or cat2 == 'Товары для кормления'
              @product_config.food_cats = [] if @product_config.food_cats.nil?
              @product_config.food_cats << catname unless @product_config.food_cats.include? catname
            end

            catname = "BOTAVIKOS" if art.start_with? 'BOTA'
            catname

          })
          .with_name(->(product_page) {
            prepare_string product_page.search('h1.ci-title').text
          })
          .with_sizes(->(product_page) {
            s=prepare_string product_page.search('span[itemprop=eligibleQuantity] span[itemprop=value]').text
            s.gsub!('ml','мл')
            s.gsub!('g','г')
            s.gsub!('.','')
            [s]
          })
          .with_description(->(product_page) {
            prepare_string product_page.search('[itemprop=description]').text
          })
          .with_composition(->(product_page) {
            prepare_string product_page.search('ul.ci-compose').text
          })
          .with_images(->(product_page) {
            product_page.search('.fotorama.mainGallery > a').map { |a| a.attr('href') }.uniq
          })
          .with_image_config(->(ic) {
            ic.with_default_url('https://4fresh.ru/')
          })
          .with_category_type('4685')
          .with_category_type('107857', 'аромалампы')
          .with_category_type('107857', 'ароматизаторы')
          .with_category_type('2328', 'блеск')
          .with_category_type('107844', 'дезодоранты')
          .with_category_type('107365', 'депиляция')
          .with_category_type('107182', 'ванны')
          .with_category_type('100994', 'дезинфекции')
          .with_category_type('107182', 'душа')
          .with_category_type('107184', 'кухни')
          .with_category_type('107361', 'лица')
          .with_category_type('107186', 'мебели')
          .with_category_type('107180', 'мытья овощей')
          .with_category_type('107183', 'мытья окон')
          .with_category_type('107185', 'мытья пола')
          .with_category_type('107366', 'дневные кремы')
          .with_category_type('107353', 'органика')
          .with_category_type('107843', 'жидкое мыло')
          .with_category_type('107174', 'жидкости для стирки')
          .with_category_type('107180', 'жидкость для мытья посуды')
          .with_category_type('107367', 'защита от комаров ')
          .with_category_type('107367', 'защита от солнца')
          .with_category_type('107847', 'зубные пасты')
          .with_category_type('107849', 'интимная гигиена')
          .with_category_type('107288', 'карандаш')
          .with_category_type('107859', 'композиции эфирных масел')
          .with_category_type('107353', 'кондиционеры и бальзамы')
          .with_category_type('107176', 'кондиционеры и ополаскиватели')
          .with_category_type('107288', 'корректоры')
          .with_category_type('107859', 'косметические масла')
          .with_category_type('107291', 'косметические наборы')
          .with_category_type('107355', 'краски для волос')
          .with_category_type('107361', 'крем и молочко')
          .with_category_type('107366', 'кремы для лица')
          .with_category_type('107366', 'кремы для тела')
          .with_category_type('107366', 'кремы и лосьоны')
          .with_category_type('107354', 'маски для волос')
          .with_category_type('107361', 'маски для лица')
          .with_category_type('107361', 'маски и обертывания ')
          .with_category_type('107354', 'масла для волос ')
          .with_category_type('107361', 'масла для лица')
          .with_category_type('107843', 'мыло')
          .with_category_type('107844', 'осушители и дезодоранты')
          .with_category_type('107184', 'от накипи')
          .with_category_type('107177', 'отбеливатели')
          .with_category_type('107843', 'очищение')
          .with_category_type('107859', 'парфюмерия')
          .with_category_type('107358', 'пилинги и скрабы')
          .with_category_type('2328', 'помада')
          .with_category_type('107174', 'порошки')
          .with_category_type('2326', 'пудра')
          .with_category_type('107177', 'пятновыводители')
          .with_category_type('2326', 'румяна')
          .with_category_type('107197', 'салфетки')
          .with_category_type('107167', 'свечи')
          .with_category_type('107358', 'скрабы')
          .with_category_type('2326', 'снятие макияжа')
          .with_category_type('107290', 'специальные средства')
          .with_category_type('107290', 'специальный уход')
          .with_category_type('107365', 'средства для бритья')
          .with_category_type('107290', 'средства для будущих мам ')
          .with_category_type('107182', 'средства для ванны')
          .with_category_type('100979', 'средства для купания')
          .with_category_type('107174', 'средства для ручной стирки')
          .with_category_type('107187', 'средства для туалета')
          .with_category_type('107356', 'средства для укладки')
          .with_category_type('107365', 'средства после бритья')
          .with_category_type('107290', 'сыворотки')
          .with_category_type('107175', 'таблетки')
          .with_category_type('100993', 'универсальные чистящие средства')
          .with_category_type('107288', 'уход за бровями и ресницами')
          .with_category_type('2330', 'уход за глазами')
          .with_category_type('107362', 'уход за губами')
          .with_category_type('107366', 'уход за ногами')
          .with_category_type('107854', 'уход за ногтями')
          .with_category_type('107845', 'уход за полостью рта')
          .with_category_type('107366', 'уход за руками')
          .with_category_type('107366', 'уход за шеей и зоной декольте')
          .with_category_type('107353', 'шампуни')
          .with_category_type('107291', 'эфирные масла')
      }, ForFresh::ForFreshProductConfig)
    end

    def load_products
      result = []

      @skip_brands = [
                      'Avalon Organics',
                      'DeoNat',
                      'Ecocraft',
                      'OrganicZone',
                      'Chokocat',
                      'Freshbubble',
                      'Mi&Ko',
                      'Natura Siberica',
                      'Pure Water',
                      'Synergetic',
                      'Биобьюти',
                      'Здоровья клад',
                      'Кристалл Свежести',
                      'СпивакЪ',
                      'Фруктовая Энергия',
                      'BioMio',
                      'Kleona',
                      'Levrana',
                      'Planeta Organica',
                      'Laboratorium',
                      'Innature',
                      'SPLAT',
                      'Muumi',
                      'Beauty365'
      ]
      page = @agent.get('https://4fresh.pro/my/auth/')
      login_form = page.form_with(id: 'loginForm')

      login_form.USER_LOGIN = '473822'
      login_form.USER_PASSWORD = 'ilzjVpU'
      page = @agent.submit(login_form, login_form.buttons.first)

      page = @agent.get 'https://4fresh.pro/catalog/'
      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*' }

      page.search('.catalog-content tbody tr').each { |tr|
        cat_id = tr.attr('data-id').to_s
        cat_name = tr.search('a').first.text
        puts cat_id
        puts cat_name

        next if @skip_brands.map(&:downcase).include? cat_name.downcase

        d = { id: cat_id, show_full: 'Y', stage: 'content' } #,template:'category'
        page = @agent.post('https://4fresh.pro/ajax/catalog/list_optimize.php', d, ajax_headers)
        page.search('table.brandsItems tbody tr.saleItem').each { |tr2|
          art = tr2.search('p.article').text.to_s
          puts art

          name = tr2.search('a.product-name').first.text.to_s.strip

          if tr2.search('th.UF_CATALOG_PRICE p.price span.old-price-catalog')[0]
            price = tr2.search('th.UF_CATALOG_PRICE p.price span.old-price-catalog')[0].text.to_s.gsub(/[^0-9,.]/, '').to_f.ceil
          else
            price = tr2.search('th.UF_CATALOG_PRICE p.price')[0].text.to_s.gsub(/[^0-9,.]/, '').to_f.ceil
          end

          # price
          if price < 200
            price = (price * 1.05).ceil
          elsif price < 400
            price = (price * 1.03).ceil
          end

          rrp = tr2.search('th.UF_CATALOG_RPRICE').first.text.gsub(/[^0-9.]/, '')
          rrp = (price * 1.3).ceil if (price*1.28).ceil >= rrp.to_f

          brand = cat_name

          stock = tr2.search('th.UF_CATALOG_STOCK p').text.to_s.gsub(' шт.', '').to_i
          next if stock < 1

          size = tr2.search('th.UF_CATALOG_WEIGHT p').text.to_s.strip
          if size == '-' and art.include? '-'
            size = art.gsub(/.*?-0*/, '') + ' мл'
          end

          size = "#{size}@#{stock}"

          result << Framework::SizeableParsedProduct.new(nil, art, [], name, '', price, [size], nil, {}, rrp, brand)
        }
      }

      result

    end

    def process
      products = load_products
      @product_config.with_picture_path(@picture_path).with_agent(@agent)

      products.each do |product|
        @product_config.add_product product
      end
      @product_config.set_cat_tags
    end

    def parse_files
      result = []
      result.concat parse_file unless @price_file.blank?
      result
    end

    def create_agent
      super
      #@agent.set_proxy("**************", 8080) unless Rails.env.development?
    end

    def parse_file
      result = []
      book = RubyXL::Parser.parse @price_file
      book.each_with_index do |sheet, sheet_index|
        #next if sheet_index == 2
        sheet.sheet_data.rows.each_with_index do |row, index|
          next if row[14].nil? or not row[14].value.is_a? Numeric
          articul = row[0].value
          #name = row[5].value
          category = sheet.sheet_name + ' - ' + row[13].value
          price = row[14].value.ceil

          result << Framework::SizeableParsedProduct.new(category, articul, [], nil, '', price, [])
          @log.debug "#{articul}  #{category} #{price}"
        end
      end
      result
    end

    def pp_object
      ForFreshPp.new
    end
  end
end