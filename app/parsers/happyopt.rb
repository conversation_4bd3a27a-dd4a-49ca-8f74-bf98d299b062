#encoding: utf-8

require 'downloader'

class Happyopt < Downloader
  def initialize
    @dlparams={}

    @allowed_brands=['COCUK','FRIENDS','MAJA','MINISTARS','RENOX','SLY','TU TU','VOLTERETA']

    super
  end

  def processPage(url)
    @log.info(url)

    page = @agent.get(url)
    conv=Encoding::Converter.new('Windows-1251','UTF-8')
    body=conv.convert(page.body)
    page = Nokogiri::HTML(body)

    return if page.search('.breadcrumbs a').length<2

    name=page.search('h1')[0].text.strip

    cat=page.search('.breadcrumbs a')[1..-1].map(&:text).join '-'

    art=page.search('input[name=productID]').attr('value').to_s
    art2=page.search('span.article')[0].text.gsub('Артикул: ','')

    sizes=[]

    if /Размерный ряд:<.b>(.*)$/=~page.search('#product-detail').to_s
      t=$1
      t.gsub!(' ','')
      sizes=t.split(',')
    end

    page.search('.product_description')[0].search('div').remove

    desc=page.search('.product_description').text

    price=page.search('#product-detail .buy-box .cost .one-item span.sum').text

    pics=[]
    page.search('div.other.lightbox a').each_with_index do |a,i|
      savePic(a.attr('href'),"#{art}~#{i}",true)
      pics<<"#{art}~#{i}"
    end

    catType='2426'
    catType='544' if name.include? 'евоч'
    catType='545' if cat.include? 'альч'

    col=addCollection(cat,catType)
    addProduct(col,"#{art} #{art2}",name,price,desc,sizes,pics)
end

  def processCat(url)
    @log.info(url)

    page = @agent.get(url)

    page.search('.product-item-box').each do |div|
      next if div.search('.button-no-add').length>0

      name=div.search('.name a')[0].text.upcase
      skip=true
      @allowed_brands.each {|b| skip=false if name.include? b}
      next if skip

      processPage(div.search('.name a')[0].attr('href'))
    end


  end

  def run
    page = @agent.get('http://www.happyopt.ru/')


    page.search("#categories a.lv1-link").each do |a|
      break if a.attr('href')=='/shop/72/'
      processCat(a['href']+'?show_all=yes')
    end
  end


end