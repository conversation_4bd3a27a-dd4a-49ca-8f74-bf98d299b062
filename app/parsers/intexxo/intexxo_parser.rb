#encoding:utf-8

module Intexxo
  class IntexxoParser < Framework::Parser
    attr_accessor :gino_de_luka
    attr_accessor :sales_summer_15
    attr_accessor :kitfox
    attr_accessor :detiland
    attr_accessor :santi
    attr_accessor :mia_cara
    attr_accessor :vivien

    def initialize
      super

      @dlparams={'gino_de_luka' => :file,
                 'sales_summer_15' => :file,
                 'kitfox' => :file,
                 'detiland' => :file,
                 'santi' => :file,
                 'mia_cara' => :file,
                 'vivien' => :file}
    end

    def initialize_settings
      @agent.html_parser = Framework::Site::MechanizeParser
      excel_parser = Framework::Excel::OldExcelParser.new
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@gino_de_luka)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) {
                                    b.worksheets[0]
                                  })
                                      .with_regex(/арт\.\s*([A-Z\dАРОХТМСКВУ\-]+)/)
                                      .with_articul_regex(/(.+)арт\.\s*([A-Z\dАРОХТМСКВУ\-]+)/)
                                      .with_articul_with_size_regex(/(.+)арт\.\s*([A-Z\dАРОХТМСКВУ\-]+)\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(2)
                                      .with_code_index(0)
                                      .with_price_index(-4)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@sales_summer_15)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) {
                                    b.worksheets[0]
                                  })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(0)
                                      .with_code_index(1)
                                      .with_price_index(-1)
                                }, Intexxo::IntexxoWorksheetConfig)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) {
                                    # Все кроме первой
                                    b.worksheets.drop(1)
                                  })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(3)
                                      .with_code_index(0)
                                      .with_price_index(-1)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@kitfox)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[0] })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(3)
                                      .with_code_index(2)
                                      .with_price_index(11)
                                }, Intexxo::IntexxoWorksheetConfig)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[1] })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(3)
                                      .with_code_index(0)
                                      .with_price_index(10)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@detiland)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[0] })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ\-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(2)
                                      .with_code_index(0)
                                      .with_price_index(-1)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@santi)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[0] })
                                      .with_regex(/арт\s+([A-Z\dАРОХТМСКВУ\-]{5,16})/)
                                      .with_articul_regex(/(.+)арт\s+([A-Z\dАРОХТМСКВУ\-]{5,16})/)
                                      .with_articul_with_size_regex(/(.+)арт\s+([A-Z\dАРОХТМСКВУ\-]{5,16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(1)
                                      .with_code_index(0)
                                      .with_price_index(5)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@mia_cara)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[0] })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(1)
                                      .with_code_index(0)
                                      .with_price_index(5)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })
                          .with_file_config(->(file_config) {
                            file_config.with_file_name(@vivien)
                                .with_worksheet_config(->(wc) {
                                  wc.with_worksheets(->(b) { b.worksheets[0] })
                                      .with_regex(/\s*([A-Z\dАРОХТМСКВУ\-]{16})/)
                                      .with_articul_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ-]{16})/)
                                      .with_articul_with_size_regex(/(.+)\s*([A-Z\dАРОХТМСКВУ-]{16})\s*(.+)?\s+р\.(\s+)?([\d\-\s]+)/)
                                      .with_nomenclature_index(2)
                                      .with_code_index(0)
                                      .with_price_index(7)
                                }, Intexxo::IntexxoWorksheetConfig)
                          })

      excel_products = excel_parser.run

      with_url('http://intexco.ru/katalog')
          .with_category_links('#multi_display > ul > li > ul  li > a')
          .with_category(->(category_page){
            br1 = prepare_string category_page.search('#breadcrumb > ol > li:nth-child(3) > a').text
            br2 = prepare_string category_page.search('#breadcrumb > ol > li:nth-child(4) > a').text
            "#{br1}, #{br2}"
          })
          .with_category_links('http://intexco.ru/katalog/vyazanyy-trikotazh', true)
          .with_pagination(->(category_page){
            next_link_elem = category_page.search('#content > div.pagination > div.links > a:nth-last-child(2)')
            return nil if next_link_elem.nil? or next_link_elem.attr('href').nil?

            return nil if prepare_string(next_link_elem.text) != '>'
            next_link_elem.attr('href').to_s
          })
          .with_product_config(->(config){
            config.with_product_selector('.product-block .name > a')
                .with_articul(->(product_page){product_page.search('h1').first.text.split[-1]}, /(?<articul>[A-Z\dАРОХТМСКВУ\-]{5,16})/)
                .with_description(->(product_page){product_page.search('#tab-description p em').map {|em| em.text}.join '; '})
                .with_images(->(product_page){
                  product_page.search('#image-additional-carousel img').map{|img|img.attr('data-zoom-image').to_s}
                })
                .with_image_config(->(ic){ic.with_default_url('http://intexco.ru/')})
                .with_unique(->(product){
                  product.images.length > 0
                })
                .with_excel_products(->(){excel_products})
                .add_product(->(site_product, excel_product){
                  return if excel_product.price == 0 or excel_product.is_added

                  addProductNew(nil,site_product.category,nil,excel_product.articul,excel_product.name,site_product.description,excel_product.sizes,excel_product.price)
                  addProduct(col, excel_product.articul,excel_product.name, excel_product.price, site_product.description, excel_product.sizes, saved_images)
                })
                .with_category_type('24')
                .with_category_type('544', 'дет', 'евочк')
                .with_category_type('545', 'дет', 'альч')
                .with_category_type('2426', 'белье')
                .with_category_type('850', 'женщ')
                .with_category_type('870', 'мужч')
          },Intexxo::IntexxoProductConfig)
    end

    def get_page(link)
      return @agent.get(link)
    rescue Mechanize::ResponseCodeError => exception
      if exception.response_code == '500'
        return exception.page
      else
        raise # Some other error, re-raise
      end
    end

  end
end