#encoding:utf-8
require_relative '../framework/site/product_config'

module SinelTex
  class SinelTexProductConfig < Framework::Site::ProductConfig

    def get_page(link)
      return nil if link=='/'
      begin
        return super(link)
      rescue Mechanize::ResponseCodeError => exception
        return nil
      end
    end

    def parse_product(product_page, link=nil)
      if product_page.nil?
        @error_log_func.call "ERROR: Have error page on url: #{link}"
        return nil
      end
      super(product_page, link)
    end

    def get_json(product)
      color = product.name.split.drop(1).join(' ')
      super(product, color)
    end

    def add_product(product)
      return if product.sizes.empty?

      super(product)
    end

    def add_product_new(product, json=nil, cat_type=nil)
      existing_product = ProductNew.find_by(purchase_id: @purchase.id, sku: product.articul)
      if existing_product.nil?
        super(product, json, cat_type)
        return
      end

      colors=JSON.parse(existing_product.colors)
      get_json(product).each{|key, value| colors[key] = value}

      existing_product.colors = colors.to_json
      existing_product.save!
    end
  end
end