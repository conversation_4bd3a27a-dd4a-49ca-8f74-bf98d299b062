# frozen_string_literal: true

class LoadSimaProducts
  def initialize
    @last_id = 0
  end

  def load_goods

    agent = Mechanize.new
    tt = Time.now

    cnt = 0
    loop do
      retry_count = 2
      begin
        @log.info "gt than #{@last_id}"

        puts "#{Time.now - tt} before request"

        params = { 'per-page': 100,
                   'id-greater-than': @last_id,
                   with_adult: 1,
                   expand: 'all_categories,volume_discounts,description,photo_sizes,ext_description,materials,attrs,barcodes,complete_set_description,notices_text_all,boxtype,files,stocks'
                   }
        #'is_remote_store': 0

        res = agent.get("https://www.sima-land.ru/api/v3/item/",
                        params, nil,
                        { 'x-api-key' => Simaland::SimalandParser::JWT_TOKEN }
        ).body

      rescue Mechanize::ResponseCodeError => exception
        @log.info exception
        @log.info exception.page.body
        if exception.response_code == '404'
          break

        else
          if exception.response_code == '500' or exception.response_code == '403'
            puts '500/403'
            sleep(30)
            retry_count -= 1
            retry if retry_count >= 0
          end

          puts exception.to_s
          puts exception.backtrace

          b = exception.page.body.force_encoding('utf-8')

          File.open('/tmp/sima_error', 'w') { |file| file.write(b) }
          return
        end
      end
      puts "#{Time.now - tt} after request"

      # puts res
      return if res == '404'

      items = JSON.parse(res)
      @log.info "Items count #{items['items'].count}"

      items['items'].each do |item|
        # @log.info item
        @last_id = item['id']
        # add_product(item)
        @pool.post do
          save_product(item)
        end
      end

      cnt += 1
      puts "#{Time.now - tt} after save"
      puts "#{@pool.queue_length} queue, #{@pool.completed_task_count} completed, #{cnt} pages processed, #{@last_id} last"

      # puts last_id

      break if Rails.env.development?
      # sleep(0.02)
      break if items['items'].count == 0
    end
  end

  def save_product(item)
    data = item #.clone
    # data.slice!('id', 'modifier', 'attrs', 'name','parent_item_id')

    brand = 'Сималенд'
    brand = item['trademark']['name'].strip if item['trademark']
    brand_id = nil
    brand_id = item['trademark']['id'].to_i if item['trademark']

    brand = 'Farmstay' if brand == 'Farm Stay'
    brand = 'Mizon' if brand == 'MIZON'

    #@log.info "Brand: #{brand},#{brand_id}"
    if @skip_brands.any? { |b| b.gsub(' ', '') == brand.downcase.gsub(' ', '') } || @skip_brands.any? { |b|
      b.to_i == brand_id
    }
      @log.info "Skipping brand"
      return
    end

    @log.info "#{item['sid']} #{item['balance']}"

    p = SimaProduct.find_by(sid: item['sid'])
    if p
      p.data = data
      p.downloaded_at = Time.now
      p.disabled = false
      p.buy_price = item['price']
      p.wholesale_price = item['wholesale_price']
      p.rrp = [item['retail_price'].to_f, item['price_max'].to_f].max
      p.save
      @log.info "Updated product id: #{p.id}, sid: #{item['sid']}"
    else
      p = SimaProduct.create(sid: item['sid'],
                             data: data,
                             downloaded_at: Time.now,
                             buy_price: item['price'],
                             disabled: false,
                             wholesale_price: item['wholesale_price'],
                             rrp: [item['retail_price'].to_f, item['price_max'].to_f].max
      )
      @log.info "New product id: #{p.id}, sid #{item['sid']}"
    end

    @added << item['sid']
  end

  def get_shipping_cost(sids)
    d = { items: sids, settlement_id: 27503886 }.to_json
    res = @agent.post("https://www.sima-land.ru/api/v3/delivery-calc/", d,
                      { 'Accept' => 'application/json', 'Content-Type' => 'application/json' })

    JSON.parse(res.body)
  end

  def apply_shipping_costs
    sids = @added.map { |s| { sid: s, qty: 1 } }

    @log = Logger.new(STDOUT) unless @log

    @log.info "Start applying shipping costs"

    pool2 = Concurrent::FixedThreadPool.new(10)

    begin
      res = {}
      i = 0
      sids.each_slice(5000) do |sids_part|
        res = get_shipping_cost(sids_part)
        i += 1
        puts "Shipping costs #{i} of #{sids.count / 5000}, wating in pool: #{pool2.queue_length}}"

        res.each do |sid, d|
          pool2.post do
            p = SimaProduct.find_by_sid(sid)
            next unless p
            p.shipping_cost = d['cost']
            p.save
          end
        end

        # sleep(0.2)
      end
    rescue Exception => e
      @log.info e.to_s
      # puts e.page.body
    end

    @log.info "Loaded shipping costs"

    pool2.shutdown
    pool2.wait_for_termination

    @log.info "Shipping costs applied"
  end

  def run(last_id: 0)
    @last_id = last_id

    File.unlink("tmp/load_sima_products") if File.exist?("tmp/load_sima_products") && Rails.env.development?

    return if File.exist?("tmp/load_sima_products") && Time.now.to_i - File.mtime("tmp/load_sima_products").to_i < 60 * 60 * 8
    FileUtils.touch "tmp/load_sima_products"

    @agent = Mechanize.new

    @global_skip_brands = ['TORNADICA', 'INNAMORE', 'INCANTO', 'Omsa', 'MALEMI', 'GLAMOUR', 'Aravia Professional', 'Aravia Organic']

    @log = Logger.new("log/sima-load-products.txt")

    @token = Simaland::SimalandParser::JWT_TOKEN

    @added = Concurrent::Array.new
    # load_cat_tree_cached # unless Rails.env.development?

    @skip_brands = Brand.where(skip: true).map(&:brand_name)

    if @global_skip_brands
      @skip_brands += @global_skip_brands
    end

    @skip_brands = @skip_brands.map { |s| s.strip.downcase }.select { |s| not s.blank? }.uniq

    @log.info "Skip brands: #{@skip_brands}"

    @pool = Concurrent::FixedThreadPool.new(20) # unless Rails.env.development?

    start_download_time = Time.now
    load_goods
    @pool.shutdown
    @pool.wait_for_termination

    SimaProduct.where("downloaded_at < ?", start_download_time).update_all(disabled: true)
    SimaProduct.where(downloaded_at: nil).update_all(disabled: true)

    apply_shipping_costs

  rescue Exception => e
    puts e.backtrace
    puts e.to_s
    @log.info e.to_s
    @log.info e.backtrace
  ensure
    File.unlink("tmp/load_sima_products") if File.exist?("tmp/load_sima_products")

  end
end
