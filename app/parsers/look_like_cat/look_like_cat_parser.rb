module LookLikeCat
  class LookLikeCatParser < Downloader

    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={}

      @prices=Hash.new

      super
    end

    def process_page(url)
      #url='https://looklikecat.ru/catalog/platya/vechernie-platya/plate_barkhat/'
      tries ||= 2

      @log.info(url)
      page = @agent.get(url)

      puts url

      name=page.search('meta[itemprop=name]').attr('content').to_s
      art=page.search('meta[itemprop=model]').attr('content').to_s.split('/').first
      code=page.search('meta[itemprop=productID]').attr('content').to_s
      cat=page.search('meta[itemprop=category]').attr('content').to_s
      price=page.search('meta[itemprop=price]').attr('content').to_s.to_f.ceil

      color = nil
      color = page.at('.mainContainer li[data-name=COLOR]').attr('data-value').to_s.downcase if page.at('.mainContainer li[data-name=COLOR]')
      name = "#{name}, цвет #{color}" if color

      return if price<10

      is_sale=page.search('span.oldPriceLabel').count>1

      p2=@agent.get "https://looklikecat.ru/ajax.php?act=getPricesWindow&product_id=#{code}&product_price_code=BASE%7C%7COPT"
      price_types={}
      p2.search('.priceVariantListItemRow').each do |r|
        price_types[r.search('div')[0].text.strip]=r.search('div')[1].text.strip.gsub(/[^0-9.,]/,'')
      end

      rrp=price_types['Розничная цена']

      price_before_discount = nil
      price_before_discount=p2.at('s.discount').text.gsub(/[^0-9.,]/,'').to_f if p2.at('s.discount')
      if price_before_discount && price_before_discount/price>1.1
        price=(price*1.07).ceil
      end

      t=page.search('.mainContainer .markerContainer .marker')
      if t.length>0
        if /Уценка -(\d+)%/=~t.first.text
          discount=$1.to_f
          price=(price/(1-discount/100)).floor
        end
      end

      sizes=page.search('.mainContainer div[data-name=SIZE_CLOTHES] li[data-name=SIZE_CLOTHES]').map {|li|
        li.attr('data-value').to_s
      }

      heights=page.search('.mainContainer div[data-name=SIZE_HEIGHT] li[data-name=SIZE_HEIGHT]').map {|li|
        li.attr('data-value').to_s
      }


      if heights.length>0
        sizes2=[]
        heights.each {|h| sizes.each {|s| sizes2<<"#{h}-#{s}"}}
        sizes=sizes2
      end

      return if page.search('#detailText .changeDescription').count==0

      desc=page.search('#detailText .changeDescription').first.attr('data-first-value').to_s.gsub('<br>',"\n").gsub(/[\r\n]+/,"\n").gsub(/ +/,' ').gsub(/^\s+$/,'')

      desc = ActionView::Base.full_sanitizer.sanitize(desc)
      desc.gsub!('Успейте купить последние размеры со скидкой, товар вышел из ассортимента!','')
      desc.strip!

      desc.gsub!(/Параметры модели$/, '')
      sost = page.at('li#tab-composition').text.strip if page.at('li#tab-composition')

      desc += "\nСостав: #{sost}" if sost

      desc.gsub!(/[\r\n]{3,}/, "\n\n")

      desc.strip!

      pic_urls=page.search('#newPictureContainer a.zoom').map{|a| a.attr('data-large-picture').to_s}
      pics=[]
      pic_urls.each_with_index {|url,i|
        next if url.include? 'vimeo' or url.include? 'youtube'
        pics<<savePic(url,"#{code}~#{i}",true)
      }

      cat_type='20'

      cat_type='1100' if cat.include? 'Вечерние платья'
      cat_type='1366' if cat.include? 'Коктейльные платья'
      cat_type='1103' if cat.include? 'Летние платья'
      cat_type='1102' if cat.include? 'Офисные платья'
      cat_type='101414' if cat.include? 'Платья-рубашки'
      cat_type='1101' if cat.include? 'Трикотажные платья'

      cat_type='19' if cat.include? 'юбки'

      cat_type='48' if cat.include? 'Длинные юбки'
      cat_type='102169' if cat.include? 'Юбки расклешенные'
      cat_type='102168' if cat.include? 'Юбки-карандаши'

      cat_type='17' if cat.include? 'Брюки'
      cat_type='102179' if cat.include? 'Жакеты'
      cat_type='102180' if cat.include? 'Жилеты'
      cat_type='102185' if cat.include? 'Свитшоты'
      cat_type='102182' if cat.include? 'Кардиганы'
      cat_type='13' if cat.include? 'Водолазки'
      cat_type='102165' if cat.include? 'Блузки'
      cat_type='102183' if cat.include? 'Свитера'
      cat_type='14' if cat.include? 'Топы'
      cat_type='102189' if cat.include? 'куртки'
      cat_type='129' if cat.include? 'Пальто'

      cat_type='25' if cat.include? 'Галстук'
      cat_type='854' if cat.include? 'Кепки'
      cat_type='107968' if cat.include? 'Комплекты'
      cat_type='108150' if cat.include? 'Снуды'
      cat_type='7' if cat.include? 'Шапки'

      col=addCollection(cat,cat_type)
      p = addProduct(col,art+' '+code,name,price,desc,sizes,pics,nil,0,nil,rrp:rrp,sale: is_sale, source: url)
      unless p.weight
        p.weight = ChatGpt.new.get_product_weight(p.name)
        p.save
      end

    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href+'?SORT_TO=120')

      page.search('#catalog .productList .product a.name').each {|a|
        process_page(a.attr('href').to_s)
      }
    end

    def run
      page=@agent.get 'https://looklikecat.ru/auth/'
      login_form=page.form_with(name:'form_auth')
      login_form['USER_LOGIN']='<EMAIL>'
      login_form['USER_PASSWORD']='270279'
      page = @agent.submit(login_form, login_form.buttons[1])

      page=@agent.get 'https://looklikecat.ru/catalog/'
      page.search('#catalogSection > .sectionItems > .item > .itemContainer > .column').each {|div|
        top_href=div.search('a')[0].attr('href').to_s
        next if top_href.include? 'ves-katalog'

        #has_subcats=false
        #div.search('.sectionList .section a').each {|a|
        #process_cat(a.attr('href').to_s)
        #has_subcats=true
        #}
        process_cat(top_href)
      }
    end

    def pp_object
      LlcPp.new
    end
  end
end
