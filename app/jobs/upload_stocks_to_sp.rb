class UploadStocksToSp
  def self.run(user)
    begin
      Rails.logger.info "UploadStocksToSp.run"
      api=SpApi.new
      user.purchases.pp.each do |purchase|
        api.import_data(purchase.sp_pid,user)
        sleep(200)
      end
    rescue Exception => e
      puts e.backtrace
      puts e.to_s

      user.company.send_ws_notification({body: "Сбой: #{e.message}"})
      raise
    end
  end
end
