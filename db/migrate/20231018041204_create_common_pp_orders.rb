class CreateCommonPpOrders < ActiveRecord::Migration[6.1]
  def change
    create_table :common_pp_orders do |t|
      t.integer :sp_order_id, index: true
      t.integer :sp_megaorder_id, index: true
      t.integer :sp_gid, index: true
      t.decimal :price, precision: 8, scale: 2
      t.string  :sp_user
      t.string  :name, index: true
      t.string  :size, index: true
      t.string  :sku, index: true
      t.string  :source
      t.string  :provider

      t.timestamps
    end
  end
end
