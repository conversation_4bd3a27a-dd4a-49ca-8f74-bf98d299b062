#encoding:utf-8
require_relative '../framework/image_config'

module FenixRostov
  class FenixRostovImageConfig < Framework::ImageConfig

    def save_picture(url, name, overwrite, scale=true, local=false)
      begin
        page = @agent.get url
      rescue Exception
        return nil
      end
      return nil unless page.is_a? Mechanize::Image


      super(url, name, overwrite, scale, local)
    end
  end
end