#encoding:utf-8
require_relative '../framework/order_sender'

module PaltoEmpire
  class PaltoEmpireOrderSender < Framework::OrderSender
    def get_page(link)
      @log.debug link
      result = super(link)
      @log.debug(result.body.to_s)

      if result.search('#panel').length == 0
        remove_banhammer(link)
        result = super(link)
        @log.debug(result.body.to_s)
      end

      @log.debug @agent.cookies
      result
    end

    def remove_banhammer(link)
      @agent.cookie_jar.clear!
      page = @agent.get '/banhammer/pid'
      @log_func.call page.body
      #@agent.save_cookies(link, "BHC=#{page.body}; path=/")
      value = prepare_string page.body.to_s
      @agent.cookie_jar << Mechanize::Cookie.new(:domain => 'paltoopt.ru', :name => 'BHC', :value => value, :path => '/', :expires => (Date.today + 1).to_s)
    end

    def clean
      page = @agent.get('http://bimki.ru')

      urls = basket_page.search('[data-ca-dispatch]').map { |a| a.attr('href').to_s }

      urls.each { |url| @agent.get(url) }
    end

    def order(full_name, sizes)
      puts full_name, sizes
      search_page = get_page('https://paltoopt.ru/catalog-of-the-goods/?q='+full_name)

      links = search_page.search('.R2D2 > a')

      if links.length == 0
        add_error_csv_data('Cannot find product', articul)
        return
      end

      if links.length > 1
        add_error_csv_data('Find more than one product', articul)
      end

      link = links[0].attr('href').to_s
      product_page = get_page(link)

      articul = prepare_string product_page.search('.options > li > b').first.text

      sizes.each do |size, quantity|
        debug_func size

        size_option = product_page.search('option').to_a.find { |opt|
          text = prepare_string opt.text
          text.include? size and text.downcase.to_s.include? 'размер'
        }

        if size_option.nil?
          add_error_csv_data('Cannot find size', articul, nil, size, quantity)
          next
        end

        json = {
            'ajax_buy' => 1,
            'id' => size_option.value,
            'art' => articul
        }

        quantity.times{
          @agent.post('https://paltoopt.ru/addbasket_ajax.php', json)
        }
      end
    end

    def add_csv_data_for_new_site(fields)
      name = fields['Название'].to_s
      color = fields['Цвет'].to_s
      size = fields['Размер'].to_s

      full_name = prepare_string "#{name} #{color}"

      @csv_data[full_name][size]+=1 if @csv_data[full_name].include? size
      @csv_data[full_name][size]=1 unless @csv_data[full_name].include? size

    end
  end
end