module PolarBear
  class PolarBearParser < Downloader
    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_page(href)
      puts href

      code=href.scan(/-(\d+)\.html/)[0][0]

      page=@agent.get href

      cat=page.search('ol.breadcrumb li a')[1..2].map(&:text).join ' - '

      props=Hash.new
      page.search('.prodict-page-description p').each do |p|
        k,v=p.text.split(':')
        if k and v
          props[k.strip]=v.strip
        end
      end

      art=props['Арткул']
      art=props['Артикул'] if props['Артикул']

      props.delete('Категория')
      props.delete('Сезон')
      props.delete('Пол')
      props.delete('Арткул')
      props.delete('Артикул')

      props.delete('Категория')

      name=page.search('h1.product-name').text.gsub(art,'').strip

      price=page.search('span[itemprop=price]').attr('content').to_s.to_f.ceil


      colors=[]
      sizes=[]
      page.search('select').each do |sel|
        opts=sel.search('option').map {|o| o.text.strip}
        if opts.first=='-- Цвет --'
          colors=opts[1..-1]
        elsif opts.first=='-- Размер --'
          sizes=opts[1..-1]
        end
      end

      desc1=props.map {|k,v| "#{k}: #{v}"}.join ', '
      desc=page.search('div[itemprop=description]').text

      desc=desc1+'. '+desc if desc1
      if colors.count==1
        name+=", цвет: #{colors[0]}"
      elsif colors.count>1
        sizes=colors.product(sizes).map {|c,s| "#{c} - #{s}"}
      end

      pic_urls=page.search('.bxslider img').map {|i| i.attr('src').to_s}
      pics=[]
      pic_urls.each_with_index {|url,i|
        fn="#{code}~#{i}"

        pics<<savePic(url,fn,false)
      }
      cat_type='960'

      cat_type='547' if cat.downcase.include? 'евочк' and cat.downcase.include? 'куртк'
      cat_type='560' if cat.downcase.include? 'альч' and cat.downcase.include? 'куртк'

      col=addCollection(cat,cat_type)
      addProduct(col,"#{code} #{art}",name,price,desc,sizes,pics)
    end

    def process_good_list(href)
      puts href
      page=@agent.get href
      urls=page.search('.product-block h3.product-block-name a').map{|a| a.attr('href').to_s}.uniq
      urls.each {|url|
        code=url.scan(/-(\d+)\.html/)[0][0]
        next if @added.include? code
        @added<<code
        process_page(url)
      }
    end

    def process_cat(href)
      page=@agent.get href

      page.search('ul.pagination a.invarseColor').each do |a|
        process_good_list(a.attr('href').to_s)
      end
    end


    def run
      if @agent==nil then before end

      @added=[]

      page=@agent.get 'http://polar-bear.life/'
      login_form=page.forms[2] #_with(name:'user_forma')
      login_form['login']=@login
      login_form['password']=@password
      page = @agent.submit(login_form, login_form.buttons[0])

      page.search('ul.main-navbar-top li.catalog-menu ul a').each {|a|
        href=a.attr('href').to_s
        process_cat(href)
      }


    end
  end
end
