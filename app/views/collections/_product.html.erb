<tr class="<%='text-muted' if product.disabled %>">
  <td><input type="checkbox" class="prodcheck" id="product<%=product.id %>" data-counter="<%= product_counter %>" data-col="<%=collection.id %>" data-name="<%=product.name %>" data-id="<%=product.id %>"></td>
  <td><%=product.art%></td>
  <td><%=product.name%></td>
  <td><%=product.price%>
    <% if product.rrp %>
    (РРЦ <%= product.rrp%>)
    <%end %>
  </td>
  <td><%=product.sizes%></td>
  <td><%=product.stock%></td>
  <td>
    <%
      if product.pictures.count>0
        pic_count=0
        product.pictures.select {|i| i.active?}.each do |img|
          pic_count+=1
          if img.path.start_with? '/mnt/spup'
            src=img.path.gsub('/mnt/spup','').gsub("\t",'%09')
            %>
            <img src="http://spup.primavon.ru<%=src %>"/>
          <% else %>
            <img src="/image/<%=img.id %>"/>
          <%
          end
        end

      else
        pics=[]
        pics=product.pics.split('~~~') if product.pics
        pic_count=pics.count
        pics.each_with_index  do  |pic, i|
          break if i>3
          src=pic.gsub(Spup2::Application.config.image_path,'/images/')

    %>
      <img src="<%=src%>"/>
    <%
      end
    end
    %>
    <% if pic_count>3 %>
      <br/>
      Всего <%= pic_count %> изображений
    <%end %>
  </td>
  <%
    short_desc=product.desc
    short_desc=product.desc[0..200]+'...' if product.desc and product.desc.length>200
  %>
  <td title="<%=product.desc%>"><%=short_desc%></td>
</tr>
