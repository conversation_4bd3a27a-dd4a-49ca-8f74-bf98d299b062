import { d as defineComponent, b1 as useQuasar, i as ref, l as onMounted, _ as _export_sfc, o as openBlock, c as createElementBlock, T as createVNode, S as withCtx, W as createBaseVNode, V as QInput, b6 as Fragment, b7 as renderList, t as toDisplayString, U as withDirectives, R as createBlock, a as createTextVNode, m as QBtn, bn as withModifiers, aC as Ripple, $ as api, P as resolveComponent, aW as QCheckbox, b5 as createCommentVNode } from "./index.f4b41209.js";
import { a as QList, b as QItem, c as QItemSection, d as QItemLabel, Q as QMenu, g as QSelect } from "./QSelect.33e940a7.js";
import { t as transformCatArrayToTree, a as QTree, Q as QPageSticky } from "./sima.7038e30d.js";
import { Q as QPage } from "./QPage.c348e993.js";
import "./QField.b3da084d.js";
var SpCatSelect_vue_vue_type_style_index_0_lang = "";
const _sfc_main$1 = defineComponent({
  name: "SpCatSelect",
  emits: ["select"],
  async setup(props, { emit }) {
    const $q = useQuasar();
    const topCats = ref([]);
    const selected = ref([]);
    const level = ref(0);
    let spCategories = null;
    const menuTarget = ref("#self_id");
    const showing = ref(false);
    const filterText = ref("");
    const filterResults = ref([]);
    const catLabel = ref("");
    onMounted(() => {
      spCategories = $q.sessionStorage.getItem("sp_cat_list");
    });
    function menuShow() {
    }
    function menuHide() {
      console.log("hide");
      menuTarget.value = "#self_id";
    }
    function updateLabel(catId) {
      let cats = $q.sessionStorage.getItem("sp_cat_list");
      if (cats[catId]) {
        catLabel.value = cats[catId][2];
      }
    }
    function selectTree(catId) {
      let catTree = [];
      let c = spCategories[catId];
      let parentId = c[3];
      catTree.unshift(c[0]);
      while (parentId > 0) {
        c = spCategories[parentId];
        catTree.unshift(c[0]);
        parentId = c[3];
      }
      console.log(catTree);
      for (let i = 0; i < catTree.length; i++) {
        selectCat(spCategories[catTree[i]]);
      }
    }
    function selectCat(cat) {
      console.log(cat);
      let newCats = Object.values(spCategories).filter((c) => c[3] == cat[0]).map((c) => {
        c[4] = cat[4] + 1;
        return c;
      });
      console.log(newCats);
      selected.value[cat[4]] = cat[0];
      topCats.value.splice(cat[4] + 1);
      selected.value.splice(cat[4] + 1);
      console.log("Selected: " + selected.value);
      if (newCats.length > 0) {
        level.value = cat[4] + 1;
        topCats.value[level.value] = newCats;
      }
      console.log(topCats.value);
    }
    function saveCat(c) {
      console.log(c);
      updateLabel(c[0]);
      selectTree(c[0]);
      showing.value = false;
      emit("select", c[0]);
    }
    function filter(val) {
      const s = val.toLowerCase().trim();
      if (s == "") {
        filterResults.value = [];
      } else {
        filterResults.value = Object.values(spCategories).filter((c) => c[1].toLowerCase().includes(s));
        if (filterResults.value.length > 8) {
          filterResults.value.splice(8);
        }
      }
    }
    function openSelect(target, selectedCat) {
      console.log("menu show " + target + " " + selectedCat);
      filterText.value = "";
      filterResults.value = "";
      topCats.value = [Object.values(spCategories).filter((c) => c[3] == 0).sort((a, b) => a[1] > b[1] ? 1 : a[1] < b[1] ? -1 : 0).map((c) => {
        c[4] = 0;
        return c;
      })];
      console.log(topCats.value);
      menuTarget.value = target;
      showing.value = true;
      console.log("Open");
      if (selectedCat) {
        console.log(parseInt(selectedCat));
        selectTree(parseInt(selectedCat));
      } else {
        selected.value = [];
      }
    }
    return {
      openSelect,
      showing,
      topCats,
      selectCat,
      selected,
      level,
      saveCat,
      spCategories,
      filterText,
      filter,
      filterResults,
      menuShow,
      menuHide,
      catLabel,
      menuTarget
    };
  }
});
const _hoisted_1$1 = { id: "self_id" };
const _hoisted_2$1 = { class: "row" };
const _hoisted_3$1 = ["onClick"];
const _hoisted_4$1 = { class: "row" };
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  return openBlock(), createElementBlock("div", _hoisted_1$1, [
    createVNode(QMenu, {
      modelValue: _ctx.showing,
      "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.showing = $event),
      onBeforeShow: _ctx.menuShow,
      onHide: _ctx.menuHide,
      target: _ctx.menuTarget,
      anchor: "center right",
      self: "center left"
    }, {
      default: withCtx(() => [
        createBaseVNode("div", _hoisted_2$1, [
          createVNode(QInput, {
            label: "\u0424\u0438\u043B\u044C\u0442\u0440",
            class: "q-ml-sm",
            "onUpdate:modelValue": [
              _ctx.filter,
              _cache[0] || (_cache[0] = ($event) => _ctx.filterText = $event)
            ],
            modelValue: _ctx.filterText
          }, null, 8, ["onUpdate:modelValue", "modelValue"])
        ]),
        createBaseVNode("div", null, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.filterResults, (c) => {
            return openBlock(), createElementBlock("div", {
              key: c[0],
              style: { "font-size": "0.8em" },
              class: "q-pl-sm cursor-pointer q-hoverable",
              onClick: ($event) => _ctx.saveCat(c)
            }, toDisplayString(c[0]) + " - " + toDisplayString(c[2]), 9, _hoisted_3$1);
          }), 128))
        ]),
        createBaseVNode("div", _hoisted_4$1, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.topCats, (catList, i) => {
            return openBlock(), createElementBlock("div", {
              class: "col",
              key: i
            }, [
              createVNode(QList, {
                bordered: "",
                separator: "",
                dense: "",
                style: { "font-size": "1em" }
              }, {
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(Fragment, null, renderList(catList, (c) => {
                    return withDirectives((openBlock(), createBlock(QItem, {
                      key: c[0],
                      "active-class": "my-menu-link",
                      clickable: "",
                      onClick: ($event) => _ctx.selectCat(c),
                      active: _ctx.selected.includes(c[0])
                    }, {
                      default: withCtx(() => [
                        createVNode(QItemSection, null, {
                          default: withCtx(() => [
                            createVNode(QItemLabel, null, {
                              default: withCtx(() => [
                                createTextVNode(toDisplayString(c[1]), 1)
                              ]),
                              _: 2
                            }, 1024)
                          ]),
                          _: 2
                        }, 1024),
                        createVNode(QItemSection, {
                          side: "",
                          top: ""
                        }, {
                          default: withCtx(() => [
                            createVNode(QBtn, {
                              icon: "save",
                              dense: "",
                              flat: "",
                              size: "sm",
                              onClick: withModifiers(($event) => _ctx.saveCat(c), ["stop"])
                            }, null, 8, ["onClick"])
                          ]),
                          _: 2
                        }, 1024)
                      ]),
                      _: 2
                    }, 1032, ["onClick", "active"])), [
                      [Ripple]
                    ]);
                  }), 128))
                ]),
                _: 2
              }, 1024)
            ]);
          }), 128))
        ])
      ]),
      _: 1
    }, 8, ["modelValue", "onBeforeShow", "onHide", "target"])
  ]);
}
var SpCatSelect = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render$1]]);
const _sfc_main = defineComponent({
  name: "SimaCatMapPage",
  async setup() {
    const $q = useQuasar();
    const simaCategories = ref([]);
    const onlyEmpty = ref(false);
    const spCatValue = ref(null);
    const spCatSelectShow = ref(false);
    const catSelectRef = ref(null);
    let simaCatList = null;
    const bufferCatText = ref(null);
    const bufferCatId = ref(null);
    const purchaseList = ref(null);
    const selectedPurchase = ref(null);
    const showCatId = ref(false);
    const spCats = $q.sessionStorage.getItem("sp_cat_list");
    var lastClickedCat = null;
    onMounted(async () => {
      loadData();
      let res = await api.get("/api3/purchases?sima=true");
      purchaseList.value = res.data.purchases;
    });
    async function loadData(id) {
      console.log(id);
      $q.loading.show({
        delay: 400
      });
      try {
        let url = `/api3/sima_categories?present=1`;
        if (selectedPurchase.value) {
          url = `/api3/sima_categories?present=1&pid=${selectedPurchase.value.id}`;
        }
        let res = await api.get(url);
        simaCatList = res.data;
        simaCategories.value = transformCatArrayToTree(res.data);
      } catch (error) {
        $q.notify("\u041E\u0448\u0438\u0431\u043A\u0430:" + error);
      }
      $q.loading.hide();
    }
    function filterCats(node) {
      return onlyEmpty.value ? node.sp_cat == null : true;
    }
    function displaySpCat(cat) {
      let label = "\u041F\u0443\u0441\u0442\u043E";
      if (spCats[cat]) {
        label = spCats[cat][2];
      }
      return label;
    }
    function openSelect(sima_cat) {
      let cat = sima_cat.sp_cat;
      let sc = sima_cat;
      console.log("!!1");
      while (cat == null && sc.parent_id != null) {
        sc = simaCatList.filter((c) => c.id == sc.parent_id)[0];
        cat = sc.sp_cat;
      }
      console.log(sima_cat);
      lastClickedCat = sima_cat;
      catSelectRef.value.openSelect(`#sima-cat-${sima_cat.id}`, cat);
    }
    async function saveCat(cat) {
      lastClickedCat.sp_cat = cat;
      await api.patch(`/api3/sima_categories/${lastClickedCat.id}`, { sp_cat: cat });
    }
    function copy(sima_cat) {
      bufferCatId.value = sima_cat.sp_cat;
      bufferCatText.value = displaySpCat(sima_cat.sp_cat);
    }
    function paste(sima_cat) {
      sima_cat.sp_cat = bufferCatId.value;
      api.patch(`/api3/sima_categories/${sima_cat.id}`, { sp_cat: bufferCatId.value });
    }
    return {
      saveCat,
      simaCategories,
      onlyEmpty,
      filterCats,
      displaySpCat,
      openSelect,
      spCatValue,
      spCatSelectShow,
      catSelectRef,
      copy,
      paste,
      bufferCatText,
      bufferCatId,
      purchaseList,
      selectedPurchase,
      loadData,
      showCatId
    };
  },
  components: { SpCatSelect }
});
const _hoisted_1 = { class: "row" };
const _hoisted_2 = { class: "col" };
const _hoisted_3 = { class: "col" };
const _hoisted_4 = { class: "col" };
const _hoisted_5 = { class: "row items-center" };
const _hoisted_6 = { class: "text-weight-bold text-primary" };
const _hoisted_7 = {
  key: 0,
  class: "text-weight-thin text-light-blue-4"
};
const _hoisted_8 = ["onClick", "id"];
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_sp_cat_select = resolveComponent("sp-cat-select");
  return openBlock(), createBlock(QPage, {
    padding: "",
    class: "q-gutter-md"
  }, {
    default: withCtx(() => [
      createBaseVNode("div", _hoisted_1, [
        createBaseVNode("div", _hoisted_2, [
          createVNode(QCheckbox, {
            modelValue: _ctx.onlyEmpty,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => _ctx.onlyEmpty = $event),
            label: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0442\u043E\u043B\u044C\u043A\u043E \u043F\u0443\u0441\u0442\u044B\u0435"
          }, null, 8, ["modelValue"])
        ]),
        createBaseVNode("div", _hoisted_3, [
          createVNode(QCheckbox, {
            modelValue: _ctx.showCatId,
            "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.showCatId = $event),
            label: "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C ID \u043A\u0430\u0442\u0435\u0433\u043E\u0440\u0438\u0439"
          }, null, 8, ["modelValue"])
        ]),
        createBaseVNode("div", _hoisted_4, [
          createVNode(QSelect, {
            outlined: "",
            dense: "",
            modelValue: _ctx.selectedPurchase,
            "onUpdate:modelValue": [
              _cache[2] || (_cache[2] = ($event) => _ctx.selectedPurchase = $event),
              _ctx.loadData
            ],
            "option-label": "name",
            options: _ctx.purchaseList,
            label: "\u0417\u0430\u043A\u0443\u043F\u043A\u0430",
            clearable: ""
          }, null, 8, ["modelValue", "options", "onUpdate:modelValue"])
        ])
      ]),
      createVNode(QTree, {
        nodes: _ctx.simaCategories,
        "node-key": "id",
        "filter-method": _ctx.filterCats,
        filter: "a"
      }, {
        "default-header": withCtx((prop) => [
          createBaseVNode("div", _hoisted_5, [
            createBaseVNode("div", _hoisted_6, [
              createTextVNode(toDisplayString(prop.node.label) + " ", 1),
              _ctx.showCatId ? (openBlock(), createElementBlock("span", _hoisted_7, "(" + toDisplayString(prop.node.id) + ")", 1)) : createCommentVNode("", true)
            ]),
            createBaseVNode("div", {
              class: "q-ml-md clickable v-ripple",
              onClick: withModifiers(($event) => _ctx.openSelect(prop.node), ["stop"]),
              id: `sima-cat-${prop.node.id}`
            }, [
              createTextVNode(toDisplayString(_ctx.displaySpCat(prop.node.sp_cat)) + " ", 1),
              createVNode(QBtn, {
                icon: "content_copy",
                flat: "",
                dense: "",
                onClick: withModifiers(($event) => _ctx.copy(prop.node), ["stop"])
              }, null, 8, ["onClick"]),
              createVNode(QBtn, {
                icon: "content_paste",
                flat: "",
                dense: "",
                onClick: withModifiers(($event) => _ctx.paste(prop.node), ["stop"])
              }, null, 8, ["onClick"])
            ], 8, _hoisted_8)
          ])
        ]),
        _: 1
      }, 8, ["nodes", "filter-method"]),
      createVNode(_component_sp_cat_select, {
        category: _ctx.spCatValue,
        show: _ctx.spCatSelectShow,
        onSelect: _ctx.saveCat,
        ref: "catSelectRef"
      }, null, 8, ["category", "show", "onSelect"]),
      createVNode(QPageSticky, {
        position: "bottom",
        offset: [18, 18]
      }, {
        default: withCtx(() => [
          createTextVNode(toDisplayString(_ctx.bufferCatText), 1)
        ]),
        _: 1
      })
    ]),
    _: 1
  });
}
var SimaCatMapPage = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);
export { SimaCatMapPage as default };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
