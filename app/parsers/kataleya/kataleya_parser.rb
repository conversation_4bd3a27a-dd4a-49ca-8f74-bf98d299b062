module Kataleya
  class KataleyaParser < Downloader
    include InvoiceKataleya

    def initialize
      @dlparams={'carnaval'=>[:checkbox,'Карнавал']}
      super
    end

    def process_page(url)
      tries ||= 2

      @log.info(url)
      page = @agent.get(url)
      puts url

      return if page.search('form.variations_form').first.nil?

      cat=page.search('nav.woocommerce-breadcrumb a')[1].text.strip

      data=JSON.parse(HTMLEntities.new.decode(page.search('form.variations_form').first.attr('data-product_variations').to_s))

      return if page.search('input[name=product_id]').first.nil?

      product_id=page.search('input[name=product_id]').attr('value').to_s

      name=page.at('h1.product_title').text.strip

      art=product_id
      #sku_span=page.search('span[itemprop=productID] span.sku').first
      #art=sku_span.text.strip if sku_span

      sizes=Hash.new

      data.each {|d|
        price=d['display_price']
        attrs=d['attributes'].select{|k,v| not v.empty?}.map{|k,v| page.search("option[value='#{v}']").text }
        sizes[price]=[] unless sizes[price]
        sizes[price]<<attrs.join(' / ')

        sizes['rrp']=Hash.new if sizes['rrp'].nil? or sizes['rrp'].is_a? Array
        sizes['rrp'][price]=(price.to_f*1.7).ceil
      }

      if sizes.keys.length==2
        p1=sizes.keys.reject {|k| k=='rrp'}[0]
          if sizes[p1].count==0 or sizes[p1][0]==''
          sizes[p1]=page.search('select#pa_razmer option')[1..-1].map(&:text)
        end
      end



      desc=''
      desc_div=page.search('div#tab-description').first
      desc=desc_div.text.to_s.gsub('<br>',' ').gsub(/ +/,' ') if desc_div
      desc.strip!


      pic_urls=page.search('img[data-large_image]').map{|i| i.attr('data-large_image').to_s}

      pics=[]
      pic_urls.each_with_index {|url,i|
        pics<<savePic(url,"#{product_id}~#{i}",true)
      }

      cat_type='552'

      cat_type='546' if cat.include? 'Блузки' or name.downcase.include? 'блуз'
      cat_type='552' if name.downcase.include? 'сараф' or name.downcase.include? 'плать'
      cat_type='558' if name.downcase.include? 'юбк'
      cat_type='4680' if name.downcase.include? 'брю' and cat.downcase.include? 'форма'
      cat_type='2448' if cat.include? 'Водолазки'
      cat_type='552' if cat.include? 'Сарафан'
      cat_type='107105' if cat.include? 'Фартук'
      cat_type='558' if cat.include? 'Юбки'
      cat_type='552' if cat.downcase.include? 'платья'
      cat_type='545' if cat.downcase.include? 'мальч'

      cat_type='108791' if @carnaval
      cat_type='552' if cat.downcase.include? 'нарядные платья'

      col=addCollection(cat,cat_type)
      p = addProduct(col,product_id,name,999999999,desc,sizes,pics,nil,0,nil,source: url)
      unless p.weight
        p.weight=ChatGpt.new.get_product_weight(p.name)
        p.save
      end
      p.save


    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_cat_data(page)
      page.search('.products .product h3.wd-entities-title a').each {|a|
        process_page(a.attr('href').to_s)
      }

    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href+'?per_page=1024')
      process_cat_data(page)

      page.search('ul.page-numbers a.page-numbers').each {|a|
        page2=@agent.get(a.attr('href').to_s)
        process_cat_data(page2)
      }
    end

    def run

      if @carnaval
        url='https://xn----7sbaaj9bfgof0at.xn--p1ai/shop/'
      else
        url='http://kataleya-nsk.ru/shop/'
      end
      page=@agent.get url

      page.search('ul.product-categories > li > a').each {|a|
            process_cat(a.attr('href').to_s)
      }
    end


  end
end
