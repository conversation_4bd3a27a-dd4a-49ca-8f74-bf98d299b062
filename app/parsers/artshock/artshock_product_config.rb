#encoding:utf-8
require_relative '../framework/site/product_config'

module Artshock
  class ArtshockProductConfig < Framework::Site::ProductConfig

    def create_new_product(product_page, link=nil)
      brands = [
          'channel', 'gucci', 'adidas', 'nike', 'armani', '<PERSON>',
          '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Paul&Shark', 'Burberry',
          'D&G', 'Polo'
      ]
      product = parse_product(product_page, link)
      return if brands.any? {|b| product.description.downcase.include?(b.downcase) or product.name.downcase.include?(b.downcase)}

      colors = product_page.search('.card-color > option').map {|opt| prepare_string opt.text}

      raw_sizes = product_page.search('.section-razmer > .radio .val-name').map {|span| prepare_string span.text}

      group_sizes = {}
      raw_sizes.each do |s|
        m = s.match(/(?<size>\d+)\s*\(\+(?<price>\d+)\s*руб.\)/)
        if m.nil?
          group_sizes[0] = [] unless group_sizes.include? 0
          group_sizes[0] << s
        else
          key = m['price'].to_i
          group_sizes[key] = [] unless group_sizes.include? key
          group_sizes[key] << m['size']
        end
      end

      group_sizes.each do |key, sizes|
        clone_product = product.clone
        clone_product.sizes = sizes
        clone_product.price += key
        #if key > 0
         # clone_product.category = "#{clone_product.category} - Большие размеры"
        #end
        add_product(clone_product)
      end
    end
  end
end