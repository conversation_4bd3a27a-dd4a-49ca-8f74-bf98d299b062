require 'creek'

module SafekoCosm
  class SafekoCosmParser < Downloader
    attr_accessor :price_file

    def initialize
      @price_file=''
      @dlparams={'price_file'=>:file}

      super
    end


    def process_file(file)
      creek = Creek::Book.new @price_file
      sheet = creek.sheets[0]

      file_pics={}
      sheet.with_images.rows.each do |row|
        next if row.values[2].nil?
        barcode=row.values[2].to_s.gsub(/\.0$/,'').gsub(/\D/,'')
        puts barcode
        if row.values[1].is_a? Array
          file_pics[barcode]=row.values[1][0]
        end

      end

      cat_name=nil
      sheet.rows_with_meta_data.each do |row|
        next if row['cells'].values[2]=='Код'

        if row['cells'].values[12].nil?
          if row['cells'].values[1]!=nil and row['cells'].values[1].is_a?(String)
            cat_name=name=row['cells'].values[1]
            cat_name.strip!
          end
          next
        end

        code=row['cells'].values[2].to_s.gsub(/\.0$/,'').gsub(/\D/,'')
        art=row['cells'].values[4].to_s.gsub(/\.0$/,'')
        puts art

        name=row['cells'].values[5].gsub(%r!/\d+$!,'').strip
        name.gsub!(art,'')
        name.gsub!(/гр\.(\S)/,'гр. \1')
        name.gsub!(/\*(\d+) шт/ ,'*\1шт')

        name.strip!

        suff=name.split.last
        name.gsub!(suff,'') if suff.include? '*' and suff.include? 'шт'

        name.gsub!(/,$/,'')

        name.gsub!(/\s+/,' ')
        puts name


        desc=row['cells'].values[6].gsub(/s+/,' ')
        desc.gsub!("\u0000",'')
        puts desc
        price=(row['cells'].values[12].to_f*1.2).ceil

        name_d=name.downcase

        cat_type=107361
        cat_type=107366 if name_d.include? 'тела'
        cat_type=108813 if name_d.include? 'волос'
        cat_type=109580 if name_d.include? 'ног'
        cat_type=109580 if name_d.include? 'рук'
        cat_type=107353 if name_d.include? 'шампу'
        cat_type=108812	 if name_d.include? 'бальз'
        cat_type=108812	 if name_d.include? 'кондиц'
        cat_type=2427 if name_d.include? 'мужс'
        cat_type=107843 if name_d.include? 'гель для душа'
        cat_type=107843 if name_d.include? 'мыло'
        cat_type=107353 if name_d.include? 'шампу'

        code=art if code.blank?

        col=addCollection(cat_name,cat_type)
        prod=addProduct(col,code,name,price,desc,[],[])

        i=Picture.where(product_id: prod.id, image_type: :sample_pic).first
        unless i
          pic=savePic(file_pics[code],code,true,false,true,:sample_pic)
          prod.pictures<<pic if pic
          prod.save
        end

      end
    end

    def run
      if @agent==nil then before end

      process_file(@price_file)

    end

  end
end
