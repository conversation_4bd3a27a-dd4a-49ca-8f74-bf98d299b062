# encoding: utf-8

module Simaland
  # todo: auto apply category
  #
  class SimalandParser < Downloader
    include TableUtils
    include InvoiceSima

    attr_accessor :sids

    JWT_TOKEN = '3fa8497049787eb60a7351be5fa6d85175a2e57af3b69b55a9615db2efeac360e838e6bcaf54aa8ffae9370a509339d6d12d0344c25d9e35a23da5da63fe3808'

    def calculate_discount_price(price, price_min: 500.0, price_max: 5000.0, discount_min: 0.00, discount_max: 0.03, power: 2.0)
      if price < price_min
        discount_percent = 0.0
      elsif price >= price_max
        discount_percent = discount_max
      else
        t = (price - price_min) / (price_max - price_min)
        discount_percent = discount_min + (discount_max - discount_min) * (t ** power)
      end
      price.to_f*(1-discount_percent)
    end

    def initialize
      super

      @dlparams = { 'sids' => [:text, 'Список артикулов'],
                    'brands' => [:text, 'Список кодов брендов'],
                    'series' => [:text, 'Список серий'],
                    'cats' => [:text, 'Список категорий'],
                    'exclude_cats' => [:text, 'Исключить категории'],
                    'exclude_brands_local' => [:text, 'Исключить бренды SIMA из этой закупки'],
                    'top_brands' => [:text, 'Выделить бренды'],
                    'only_stm' => [:checkbox, 'Только СТМ'],
                    'include_adult' => [:checkbox, 'Добавлять товары для взрослых'],
                    'is_enough' => [:checkbox, 'Только большие остатки'],
                    'only_wholesale_cat' => [:text, 'Только товары этой оптовой категории'],
                    'skip_spup_purchase' => [:text, 'Исключить товары из этих закупок в spup'], }

      @added = {}

      # @brands = {}

      @categories = {}

      @default_brand = 'СИМА-ЛЕНД'

      @token = ''

      @exclude_in_winter = [944, 945, 948, 956, 957, 1192, 1193]

      @is_winter = (DateTime.now.month > 10 || DateTime.now.month < 4)
    end

    @@include_attrs = ['Цвет', 'Плотность, г/м²', 'Размер, см', 'Вес брутто', 'Состав', 'Длина', 'Материал',
                       'Цвет свечения', 'В наборе, шт.', 'Диаметр шара, см', 'Тип питания', 'Типоразмер батареек',
                       'Количество светодиодов', 'Ткань', 'Рост, см', 'Ширина', 'Высота',
                       'Количество игроков', 'Возраст', 'Количество колёс', 'Maксимальная нагрузка, кг',
                       'Maксимальная нагрузка, кг', 'Материал колёс', 'Материал деки', 'Формат',
                       'Особенности', 'Вес нетто', 'Добавки', 'Вид чая', 'Фасовка',
                       'Добавки чая', 'Диаметр, см', 'Форма', 'Вид тарелки', 'Можно мыть в посудомоечной машине',
                       'Можно использовать в СВЧ-печи', 'размер', 'вес', 'масса', 'Количество ламп',
                       'Режим работы гирлянды', 'Вид лампы', 'Количество режимов', 'Цвет нити гирлянды',
                       'Материал нити', 'Степень защиты', 'Вид светодиода', 'Вид насадки', 'Вид гирлянды',
                       'Объём, мл', 'Рисунок', 'Наличие губки/щётки', 'Назначение', 'Настенные', 'Плотность наполнителя',
                       'Материал наполнителя', 'Состав ткани', 'Сезон', 'Хлопковый чехол', 'Насечки', 'Вибрац', 'шаров',
                       'Диаметр', 'пульт', 'управле', 'Покрытие', 'двер', 'корзи', 'тип', 'разобр', 'устан', 'число', 'наличие', 'толщина', 'глубина',
                       'ящик', 'мощность', 'цоколь', 'Напряжение'
    ].map(&:downcase)

    def self.include_attrs
      @@include_attrs
    end

    def use_sizes(item)
      cats = ['3515', '16', '27504']
      modifiers = [91, 114, 157, 97]
      if cats.any? { |c| item['all_categories'].include? c } && modifiers.include?(item['modifier_id'])
        return true
      end
      false
    end
    def get_barcodes
      sids = Set.new
      Purchase.where(dlclass: 'Simaland::SimalandParser').each do |p|
        Product.where(purchase_id: p.id).where("updated_at>'2022-04-01'").each do |prod|
          if (prod.barcodes == nil) || (prod.barcodes.count == 0)
            sids.add prod.art
          end
        end
      end; nil

      sids.each_slice(50) do |s2|
        res = SimaApi.get "https://www.sima-land.ru/api/v3/item/",
                          { 'per-page': 100,
                            sid: s2.join(','),
                            expand: 'barcodes,all_categories' },
                          nil

        items = JSON.parse(res.body)
        items['items'].each do |i|
          puts i['sid']
          Product.where(art: i['sid']).each do |pr|
            size = '-'
            size = i['modifier_value'] if use_sizes(i)

            i['barcodes'].each do |code|
              Barcode.find_or_create_by(product_id: pr.id, code: code, size: size)
            end
          end
        end
      end
    end

    def get_new_category_type(cat)
      ['Продукты питания']
    end

    def process_pdf_page(img)
      img_list = Magick::ImageList.new
      img_list << img
      img_list.new_image(img_list.first.columns, img_list.first.rows) { |p| p.background_color = "white" }
      pic = img_list.reverse.flatten_images

      rows = pic.rows
      cols = pic.columns

      remove_rows = 0
      step = 0
      rows.times { |y|
        pixels = pic.get_pixels(0, y, cols, 1)
        avg = 0
        pixels.each_with_index { |p|
          avg += (p.red + p.green + p.blue)
        }
        avg /= cols * 3
        remove_rows = y
        step = 1 if (avg < 65535) && (step == 0)

        break if (step == 1) && (avg == 65535)
      }

      remove_rows = 0 if remove_rows < 0

      remove_rows *= 2 if remove_rows.to_f / rows.to_f < 0.04

      remove_rows_bottom = 0
      skip_bottom_rows = (rows * 0.03).to_i
      (rows - skip_bottom_rows).times { |y|
        pixels = pic.get_pixels(0, rows - y - skip_bottom_rows - 1, cols, 1)
        avg = 0
        pixels.each_with_index { |p|
          avg += (p.red + p.green + p.blue)
        }
        avg /= cols * 3
        remove_rows_bottom = y + skip_bottom_rows
        break if avg < 65535
      }
      remove_rows_bottom -= 50
      remove_rows_bottom = 0 if remove_rows_bottom < 0
      # puts remove_rows_bottom

      if (remove_rows >= 0) || (remove_rows_bottom >= 0)
        pic.crop!(0, remove_rows, cols, rows - remove_rows - remove_rows_bottom, true)
      end

      rows = pic.rows
      cols = pic.columns
      # puts rows, cols

      remove_left = 0
      cols.times { |x|
        pixels = pic.get_pixels(x, 0, 1, rows)
        avg = 0
        pixels.each_with_index { |p|
          avg += (p.red + p.green + p.blue)
        }
        avg /= rows * 3
        remove_left = x
        break if avg < 65535
      }
      remove_left -= 5
      remove_left = 0 if remove_left < 0
      # puts remove_left

      remove_right = 0
      cols.times { |x|
        pixels = pic.get_pixels(cols - x - 1, 0, 1, rows)
        avg = 0
        pixels.each_with_index { |p|
          avg += (p.red + p.green + p.blue)
        }
        avg /= rows * 3
        remove_right = x
        break if avg < 65535
      }
      remove_right -= 5
      remove_right = 0 if remove_right < 0
      # puts remove_right

      if (remove_left >= 0) || (remove_right >= 0)
        pic.crop!(remove_left, 0, cols - remove_right - remove_left, rows, true)
      end
      pic
    end

    def process_pdf(file)
      img_list = Magick::ImageList.new
      img_list.read(file) { |p| p.density = "150" }

      res = Magick::ImageList.new
      img_list.each do |img|
        img = process_pdf_page(img)
        res << img
      end

      res_fn = "#{File.dirname(file)}/#{File.basename(file, '.*')}.png"
      res.append(true).write(res_fn)
      res_fn
    end


    def get_cat_tree(cat_id)
      #@all_cats[cat_id]['path'].map { |c_id| @all_cats[c_id]['name'] }
      c = SimaCategory.find(cat_id)
      c.path.split('.').map { |c_id| SimaCategory.find(c_id).name }
      rescue ActiveRecord::RecordNotFound
        []
    end

    def queue_product(item)
      @product_queue.push(item)
    end

    def sort_and_merge_values_by_attr_name(attrs)
      result = []
      attrs.group_by { |h| h["attr_name"] } # Группируем по "attr_name" (строка)
           .each_value do |group|
        sorted_values = group.map { |h| h['value'].to_s }.sort # Извлекаем и сортируем значения
        merged_value = sorted_values.join(', ')        # Объединяем значения через запятую
        result << { "attr_name" => group.first["attr_name"], 'value' => merged_value } # Добавляем в результат
      end
      result
    end
    def add_product(item, cat_prefix: nil, category_id: nil, use_group: nil)
      if item['modifier_id'] == 91 || item['modifier_id'] == 114 || item['modifier_id'] == 157 || item['modifier_id'] == 97
        do_add_product(item, cat_prefix: cat_prefix)
      else
        #@pool.post do
          do_add_product(item, cat_prefix: cat_prefix, category_id: category_id, use_group: use_group)
        #end

      end
    end

    def do_add_product(sima_product, cat_prefix: nil, category_id: nil, use_group: nil)
      item = sima_product.data
      @log.info item['sid']

      stores_stocks = item['stocks'].select { |s| [2,115].include?(s['stock_id']) }.map { |s| s['balance'] }.sum

      if item['is_remote_store'].to_i == 1 && stores_stocks==0
        @log.info "Skipping, remote store"
        @log.info item['stocks']
        return
      end

      return if item['balance'] && (item['balance'].to_s == '0')

      return if item['is_disabled'] && (item['is_disabled'].to_s == '1')

      return if !@include_adult && item['is_adult'].to_i != 0

      return if item['country_id'].to_i == 392 || item['country_id'].to_i == 410
      #save_product(item)

      if item['is_markdown'].to_i != 0
        @log.info "Skipping, markdown"
        return
      end

      if item['mean_rating'] && item['mean_rating'].to_f < 2 && item['mean_rating'].to_f > 0
        @log.info "Skipping, rating #{item['mean_rating']}"
        return
      end

      if @exclude_products.include? item['sid'].to_s
        @log.info "Skipping, in other purchase exclude list"
        return
      end

      if /\b(аквариум|террариум)\b/i =~ item['name'] || /grandorf/i =~ item['name']
        @log.info "Skip: #{item['name']}"
        return
      end

      @log.info @exclude_cats.intersection(item['all_categories'])
      if @exclude_cats.intersection(item['all_categories']).count > 0
        @log.info "Excluding cat"
        return
      end

      entry_time = Time.now
      return if @is_winter && @exclude_in_winter.include?(item['transport_condition_id'].to_i)
      return if @is_winter && item['name'].downcase.include?('гелевые часы')

      return if @is_winter && item['all_categories'].include?('32952')

      stm = false
      stm = true if item['is_exclusive'] && (item['is_exclusive'].to_i != 0)
      stm = true if item['trademark'] && (item['trademark']['is_exclusive'].to_i != 0)

      return if @only_stm && (stm == false)

      return if item['balance'] && item['balance'].to_i < 2

      #return if @is_enough && item['isEnough'].nil? && item['balance'].to_i < 2
      #return if @is_enough && item['isEnough'] == false && item['balance'].to_i < 2


      @log.info item['wholesale_text']

      unless @only_wholesale_cat.blank?
        if item['wholesale_text']
          return unless @only_wholesale_cat.split(',').any? { |w|
            w.strip.downcase == item['wholesale_text'].strip.downcase
          }
          # return if item['wholesale_text'].strip.downcase!=@only_wholesale_cat.strip.downcase
        end
      end

      brand = 'Сималенд'
      brand = item['trademark']['name'].strip if item['trademark']
      brand_id = nil
      brand_id = item['trademark']['id'].to_i if item['trademark']

      brand = 'Farmstay' if brand == 'Farm Stay'
      brand = 'Mizon' if brand == 'MIZON'

      @log.info "Brand: #{brand},#{brand_id}"
      if @skip_brands.any? { |b| b.gsub(' ', '') == brand.downcase.gsub(' ', '') } || @skip_brands.any? { |b|
        b.to_i == brand_id
      }
        @log.info "Skipping brand"
        return
      end

      return if item['price'].to_i > 5000 && item['price'].to_i == item['wholesale_price'].to_i

      r = get_prices(sima_product)
      price = r[:price]

      if price.to_f < 200
        price = price.to_f*1.1
        #elsif price.to_f < 1000
        #price = price.to_f*1.03
        #elsif price.to_f < 5000
        #price = price.to_f*1.02
        #else
        #price = price.to_f*1.01
      end

      price =calculate_discount_price(price)

      price = (price*1.02).ceil


      rrp = r[:rrp]
      buy_price = r[:buy_price]

      return if price > 500000


      size = '-'


      # orig_price=price
      product_id = item['id']
      art = item['sid']
      orig_art = art

      pack_size = nil


      # pack_size=item['minimum_order_quantity'].to_i if item['minimum_order_quantity'] and item['minimum_order_quantity'].to_i > 1
      pack_size = item['minQty'].to_i if item['minQty'].to_i > 1 # if item['isAddToCartMultiple']

      if item['parent_item_id']
        pack_size = SimaProduct.where("data ->> 'parent_item_id' = ?", item['parent_item_id'].to_s).map {|p| p['data']['minQty'].to_i}.max
        pack_size = nil if pack_size == 1
      end


      @log.info item['isAddToCartMultiple']
      @log.info item['minQty'].to_i
      @log.info pack_size
      per_package = 1
      per_package = item['per_package'].to_i if item['per_package'] && (item['per_package'].to_i > 1)

      if pack_size && pack_size > 5 && price.to_i * pack_size > 500
        @log.info "Skipping, too expensive pack"
        return
      end

      name = item['name'].gsub('×', 'x')

      return if name.downcase.include? 'первой помощи'

      box_weight = item['weight'].to_i if item['weight'].to_i > 0
      box_depth = item['box_depth'].to_f * 10 if item['box_depth'].to_f > 0
      box_width = item['box_width'].to_f * 10 if item['box_width'].to_f > 0
      box_height = item['box_height'].to_f * 10 if item['box_height'].to_f > 0

      box_depth = item['depth'].to_f * 10 unless box_depth
      box_width = item['width'].to_f * 10 unless box_width
      box_height = item['height'].to_f * 10 unless box_height

      box_height = 1 if box_height.to_i == 0

      if name.downcase.include?('мешок') || name.downcase.include?('пакет')
        min_value = [box_depth, box_width, box_height].min
        case min_value
        when box_depth
          box_depth *= 2
        when box_width
          box_width *= 2
        when box_height
          box_height *= 2
        end
      end

      depth = item['depth'].to_f * 10 if item['depth'].to_f > 0
      width = item['width'].to_f * 10 if item['width'].to_f > 0
      height = item['height'].to_f * 10 if item['height'].to_f > 0

      if pack_size
        size_desc = "В упаковке #{pack_size} шт. - цена за 1 шт."

        if price.to_f <= 2 && pack_size > 200
          @log.info "Skipping, too small price, too big pack"
          return
        end

        if (price.to_f <= 10) || (price * pack_size <= 500)
          price *= pack_size
          rrp *= pack_size if rrp
          size = "Уп. #{pack_size} шт"
          size_desc = "Цена за упаковку из #{pack_size} шт"
          art = "#{art}-#{pack_size}"
          box_weight *= pack_size if box_weight

          vol_mul = Math.sqrt(pack_size)
          if item['all_categories'].include? '26236'
            vol_mul = Math.sqrt(pack_size/1.8)
            box_weight *= 2 if box_weight
          end

          #vol_mul = vol_mul

          dims = {w: box_width, d: box_depth, h: box_height}

          min_dim = dims.sort_by { |_, v| v }.take(2).map { |entry| entry[0] }

          min_dim.each do |dim|
            case dim
            when :w
              box_width *= vol_mul
            when :h
              box_height *= vol_mul
            when :d
              box_depth *= vol_mul
            end
          end

          box_width = box_width.ceil if box_width
          box_height = box_height.ceil if box_height
          box_depth = box_depth.ceil if box_depth

          name.gsub!(/\(цена за \d+ штук.?\)/i,'')
          name.gsub!(/, цена за \d+ штук.?/i,'')
        else
          pack_size = nil
        end
      end

      weight = nil
      if item['weight'] && (item['weight'].to_i > 0)
        w = item['weight'].to_i
        if w < 1000
          weight = "#{w} г."
        else
          weight = "#{(w.to_f / 1000).round(1)} кг."
        end
      end

      rrp = (price * 1.35).ceil(-1) if rrp.to_f.ceil <= price * 1.34 #|| rrp.to_f.ceil >= price * 2
      rrp += 3 if rrp - price * 1.35 <= 2

      price = price.to_f.ceil
      rrp = rrp.to_f.ceil if rrp

      item_size = "Размер: #{item['size']}. " if item['size'] && (item['size'].to_s.strip != '') && (item['size'].to_s != 'false')

      if item['materials'] && (item['materials'].count > 0)
        materials = item['materials'].map { |m| m['name'] }.join ', '
      end

      if item['box_width'] && item['box_height'] && item['box_depth']
        box_size = "#{item['box_depth']} x #{item['box_width']} x #{item['box_height']} см"
      end

      price = price.ceil if price > 5

      #return if @is_winter && /\bвода\b/i =~ name
      #return if /туалетная вода/i =~ name

      if use_sizes(item)
        modifier = item['modifier_value']
        item_size = nil
        item['attrs'].delete_if { |a| a['attr_name'] == 'Размер' }
        item['attrs'].delete_if { |a| a['attr_name'] == 'Рост, см' }
        item['attrs'].delete_if { |a| a['attr_name'] == 'Размер обуви' }
        item['attrs'].delete_if { |a| a['attr_name'] == 'Диоптрия' }
        if item['modifier_id'] == 157 || item['modifier_id'] == 114
          name.gsub!(/р\. *[+0-9,]+/,'')
          name.gsub!(/рост \d.*/,'')
          name.gsub!(/длина стопы  *[+0-9,]+ *см,?/,'')
          name.gsub!(/, [+0-9,]+$/, '')
          name.gsub!(/диоптрия: [0-9+-,.]+;* */, '')
          name.gsub!(/ +/, ' ')
          name.strip!
        end

      end

      if item['notices_text_all']
        notices = item['notices_text_all']
        notices = notices.values if notices.is_a? Hash

        notice = notices
                   .reject { |n|
                     n.downcase.include?('акц') ||
                       n.downcase.include?('плат') ||
                       n.downcase.include?('vk') ||
                       n.downcase.include?('face') ||
                       n.downcase.include?('inst') ||
                       n.downcase.include?('выгрузка') ||
                       n.downcase.include?('скид') ||
                       n.downcase.include?('покупат') ||
                       n.downcase.include?('выгруз') ||
                       n.downcase.include?('проек') ||
                       n.downcase.include?('сборк')
                   }
                   .map { |n| n.to_s.strip.gsub(/\.+$/, '') }.join('. ')
      end

      desc = item['description']
      desc = TextUtils.format_html(desc)
      desc.gsub!('Вы можете оформить заказ на изготовление соответсвующих товаров по заявленным Вами размерам, для этого свяжитесь с Вашим персональным менеджером в личном кабинете сайта.','')
      desc.gsub!('Обратите внимание на дополнение в блоке «С этим товаром покупают».', '')

      ext_desc = '. ' + TextUtils.format_html(item['ext_description'])

      attrs = item['attrs'].select { |a|
        @@include_attrs.any? { |ia|
          ia.downcase.include? a['attr_name'].downcase or a['attr_name'].downcase.include? ia.downcase
        }
      }.select { |a| a['value'].to_s != 'false' }

      attrs = sort_and_merge_values_by_attr_name(attrs)
      attr_text = attrs.map { |a| "#{a['attr_name'].downcase}: #{a['value']}" }.join(', ').capitalize

      desc = "#{size_desc}. #{desc}" if size.to_s != ''
      desc = "#{item_size}. #{desc}" if item_size
      desc = "#{desc}.\n\n#{ext_desc}" unless ext_desc.to_s.strip.blank?
      desc = "#{desc}.\n\n#{attr_text}" unless attr_text.to_s.strip.blank?

      desc = "#{desc}.\n\nВес: #{weight}" if weight
      desc = "#{desc}.\n\nСостав: #{materials}" if materials
      desc = "#{desc}.\n\nРазмер упаковки: #{box_size}" if box_size
      desc = "#{desc}.\n\n #{notice}. " if notice

      desc.strip!
      desc.gsub!(/( \.)+/, '. ')
      desc.gsub!(/\.+/, '.')
      # desc.gsub!(/\s+/, ' ')
      desc.gsub!(/^[. ]+/, '')

      return if desc.downcase.include?('парацетамол') ||
        desc.downcase.include?('аспирин') ||
        desc.downcase.include?('ацетилсал') ||
        desc.downcase.include?('цитрамон')


      # desc.gsub(/\.\S/, '. ')

      desc.gsub!(/Luazon Lighting - сертифицированная.*?;Не требует технически сложных инструментов для установки\./,
                 '')
      desc.gsub!('×', 'x')
      desc.gsub!(/^-\. /, '')
      desc = desc.gsub("\r", "").gsub(/\n{3,}/, "\n\n").strip

      @log.info "#{Time.now - entry_time} at start get cat"
      cat = get_category(item['category_id'])
      @log.info "#{Time.now - entry_time} after get cat"

      cat = item['series']['name'].gsub('Лас Играс ', '') if cat.nil? || (cat == '')

      cat_id = 0

      @log.info "#{Time.now - entry_time} at start get cat tree"
      begin
        cat_tree = get_cat_tree(item['category_id'])
      rescue
        @log.info "Error getting cat tree"
        return
      end
      @log.info "#{Time.now - entry_time} after get cat tree"

      if item['trademark'] && @top_brands
        br = @top_brands.find { |b| b.include? item['trademark']['name'].strip.downcase }
        if br
          tm = item['trademark']['name'].strip
          if br.include? '::'
            tm = br.split('::').last.strip
          end
          cat = "#{tm} - #{cat}"
          cat_tree = cat_tree.map { |c| "#{item['trademark']['name'].strip} - #{c}" }
        end
      end

      decor_type = item['attrs'].select { |i| i['attr_id'].to_i == 3938 }.first
      if decor_type
        cat = "#{cat} - #{decor_type['value']}"
      end

      color_type = item['attrs'].select { |i| i['attr_id'].to_i == 5927 }.first
      if color_type && (cat == 'Пищевые красители')
        cat = "#{cat} - #{color_type['value']}"
      end


      group = nil

      # Тарелки, блюда::Тарелки, блюда::Разные тарелки::attr::Цвет
      if (@purchase.id == 303) || (@purchase.id == 308)
        # return if cat=='Тарелки, блюда' and item['is_hit'].to_i==0

        if item['name'].downcase.include? 'дракон'
          group = 'Драконы'
          cat = "#{cat} - драконы"
        elsif item['series'] &&
          (
            (item['series']['name'].downcase.include?('нов') && item['series']['name'].downcase.include?('год')) ||
              item['series']['name'].include?('2024')
          )

          cat = "Новый год - #{cat}"
          group = 'Новый год'

          # skip new year products
          #return
        end

        new_year_theme = item['attrs'].select { |i|
          i['attr_id'].to_i == 432 and i['value'].downcase.include? 'новый год'
        }.first

        if new_year_theme && (group != 'Новый год') && !item['name'].downcase.include?('кролик')
          cat = "Новый год - #{cat}"
          group = 'Новый год'

          # skip new year products
          #return
        end

      end

      # rule:
      # cat::group::default cat name::rename type::rename param[::prefix]
      if @purchase.cat_rules
        rules = @purchase.cat_rules.split("\n").map(&:strip)
        rules.each do |rule|
          r = rule.split('::')
          if r[0].downcase == cat.downcase
            @log.info "Using rule #{r}"
            old_cat = cat
            group = r[1] unless r[1].blank?
            case r[3].downcase
            when 'stuff'
              cat = item['stuff'] if item['stuff']
            when 'serie'
              cat = item['series']['name'] if item['series'] && item['series']['name']
            when 'attr'
              a = item['attrs'].find { |a| a['attr_name'].downcase == r[4].downcase }
              cat = a['value'] if a
            end
            cat.strip!
            cat = r[2] if (cat == old_cat) || cat.blank?

            if r[5]
              cat = "#{r[5]} - #{cat}"
            end

            @log.info "New cat name #{old_cat} => #{cat}"
          end
        end

      end

      cat = 'Корригирующие очки' if cat == 'Очки при близорукости' || cat == 'Очки при дальнозоркости'

      male = false
      if @purchase.id == 283
        male = name.downcase.include?('муж')
        a = item['attrs'].find { |a| a['attr_name'].downcase == "для кого" }
        male = true if a && (a['value'] == 'Для мужчины')
        if male
          cat = "Мужчинам - #{cat}"
          group = "Мужская одежда"
        end
      end

      if category_id.to_i > 700000000
        promo_cat = get_cat_for_topcat(category_id, item)
        cat = promo_cat if promo_cat
      end

      group = use_group if use_group

      if cat_prefix
        cat = "#{cat_prefix} #{cat}"
      end

      #      if @purchase.id==303
      #        return if cat=='Разные кружки' and group=='Кружки' and item['is_hit'].to_i==0
      # end

      @log.info "#{Time.now - entry_time} at start add col"
      col = addCollection(cat, cat_id, nil, group: group)
      @log.info "#{Time.now - entry_time} after add col"

      @log.info "#{art} #{product_id} #{name} #{price} #{modifier}"

      barcodes = item['barcodes']

      source = ''
      existing_product = nil
      unless modifier.blank?
        size = modifier.gsub(',', '.')
        source = art

        raise "#{art} parent blank" if item['parent_item_id'].blank?

        art = "s#{item['parent_item_id']}"

        if male
          #          p=Product.where(art: art,purchase_id:@purchase.id).first
          # if p
          # p.collections=[]
          # p.save
          # end
        end

        Cache.set_value('sima', "#{art}~#{size}", source)

        name.gsub!(size, '')
        name.gsub!(/, размер/, '')
        name.gsub!(/, р-р/, '')
        name.gsub!(/р\./, '')
        name.gsub!(/[ ,]{2,}/, ', ')

        if item['modifier_id'] != 157
          desc += ". Параметры товара указаны для размера #{size}."
        end

        desc.gsub!(/\.+/, '.')

        existing_product = Product.where(art: art, disabled: false, purchase_id: @purchase_id).first
      end

      @log.info "#{Time.now - entry_time} at start add product"

      size_with_stock = size

      balance = item['balance']
      balance = (balance / pack_size).to_i if pack_size

      size_with_stock = "#{size}@#{balance}" if item['balance']

      @log.info "#{balance} #{size} #{item['balance']} #{pack_size} #{size_with_stock}"

      if existing_product && (existing_product.downloaded_at > @purchase.download_started_at)
        existing_product.add_size(price, size_with_stock, sort: true, barcodes: barcodes, rrp: rrp)
        e = JSON.parse(existing_product.extra)
        e['size_data'] = {} unless e['size_data']
        e['size_data'].delete_if {|s,art| art.to_i==orig_art.to_i}
        e['size_data'][size] = orig_art
        existing_product.extra = e.to_json
        existing_product.save
        return
      else
        existing_product = Product.where(art: art, purchase_id: @purchase_id).first

        if existing_product && existing_product.extra
          e = JSON.parse(existing_product.extra)
          size_data = e['size_data'] if e.is_a?(Hash)
        end

        size_data = {} unless size_data
        size_data.delete_if {|s,a| a.to_i==art.to_i}
        size_data[size] = art

        pics = []
        pos = 1000000000
        if item['mean_rating'] && (item['mean_rating'] > 3.8)
          pos = (5.0 - item['mean_rating'].to_f) * 10
        end
        prod = addProduct(col, art, name, price, desc, [size_with_stock], pics,
                          { 'cat_tree' => cat_tree, 'size_data' => size_data }, -1, nil, rrp: rrp,
                          brand_name: brand, source: source, barcodes: barcodes, buy_price: buy_price, pos: pos,
                          sima_cat: item['category_id'], weight: box_weight, box_height: box_height, box_width: box_width,
                          box_depth: box_depth, height: height, width: width, depth: depth
        )

        if item['all_categories'].include? '71310'
          if prod.edited_desc.nil? && (prod.desc.downcase.include?('лечен') || prod.desc.downcase.include?('лечеб'))
            prod.edited_desc = ChatGpt.new.request("Переделай описание товара. Не упоминай лечение или лечебные свойства. НЕ ИСПОЛЬЗУЙ СЛОВО ЛЕЧЕНИЕ!!! Немного сократи и сделай более понятным: #{prod.desc}")
            prod.save
          end

          if prod.edited_desc&.downcase&.include?('лечен') || prod.edited_desc&.downcase&.include?('лечеб')
            prod.edited_desc = ChatGpt.new.request("Переделай описание товара. Не упоминай лечение или лечебные свойства. НЕ ИСПОЛЬЗУЙ СЛОВО ЛЕЧЕНИЕ!!! Немного сократи и сделай более понятным: #{prod.desc}")
            prod.save
          end
        end

        if modifier
          begin
            e = JSON.parse(prod.extra)
          rescue
            e = {}
          end

          e['size_data'] = {} unless e['size_data']
          e['size_data'].delete_if {|s,a| a.to_i==orig_art.to_i}
          e['size_data'][size] = orig_art
          prod.extra = e.to_json
          prod.save
        end
      end

      #@all_cats[item['category_id']]['products'] << prod.id

      @log.info "#{Time.now - entry_time} at start pics dl"

      prod.pictures = []
      pics = []
      item['photos'].each_with_index do |photo, i|
        pic_url = "#{photo['url_part']}700-nw.jpg?v=#{photo['version']}"
        pic = Picture.where(source: pic_url).order(:updated_at).last

        if pic && (pic.version == photo['version'].to_s) && File.exist?(pic.path)
          pics << pic
        else
          prod.need_reupload_pics = true
          prod.save
          pic = savePic(pic_url, "#{art}~#{i}", true, false, false, :active, prod, show_order: i, force_overwrite: true)
          if pic
            pic.version = photo['version']
            pic.save
            pics << pic
          end
        end
      end

      @log.info pics
      pics.each do |p|
        prod.pictures << p if p && !prod.pictures.include?(p)
      end

      @log.info "#{Time.now - entry_time} at after pics dl"

      item['files'].each_with_index do |item, i|
        next unless item['type_id'] == 5

        url = item['url']
        ver = item['md5_hash']
        pic = Picture.where(source: url).order(:updated_at).last
        unless pic && (pic.version == item['md5_hash'].to_s)
          ext = url.split('.').last.downcase
          pic = nil
          if ext.end_with?('jpg') || ext.end_with?('jpeg') || ext.end_with?('png')
            begin
              pic = savePic(url, "#{art}~file_#{i}", true, false, false, :active, prod)
              pic.version = ver
              pic.save
            rescue Exception => e
            end
          elsif ext.end_with?('pdf') && (not Rails.env.development?)
            @agent.download(url, "/tmp/file_#{art}_#{i}.pdf")
            processed_pic = process_pdf("/tmp/file_#{art}_#{i}.pdf")
            pic = savePic(processed_pic, "#{art}~file_#{i}", true, false, true, :active, prod, source: url)
            pic.version = ver
            pic.save
          end
        end
        unless prod.pictures.include?(pic) || pic.nil?
          prod.pictures << pic
        end
      end

      prod.save

      @added[orig_art] = prod.id
      @log.info "#{Time.now - entry_time} at leave"
    end

    def process_cat_page(data)
      data.each do |cat|
        cat['path'] = cat['path'].split('.').map(&:to_i) if cat['path']
        cat['products'] = []
        #@all_cats[cat['id']] = cat
      end
    end


    def get_category(category_id)
      return @categories[category_id] if @categories[category_id]

      c = SimaCategory.where(id: category_id).first
      if c
        @categories[category_id] = c.name
        return c.name
      end

      res = SimaApi.get "https://www.sima-land.ru/api/v3/category/#{category_id}/"
      cat = JSON.parse(res)

      @categories[category_id] = cat['name']

      cat['name']
    end

    def load_brands
      page = 1
      loop do
        res = SimaApi.get "https://www.sima-land.ru/api/v5/trademark?p=#{page}"
        brands = JSON.parse(res)
        page += 1
        brands.each do |b|
          @brands[b['slug']] = b
        end
        break if brands.count < 100
      end
    end

    def load_categories
      res = SimaApi.get "https://www.sima-land.ru/api/v5/category", {}, nil, { 'x-api-key' => @token }
      @categories = JSON.parse(res)
    end

    def get_cat_for_topcat(topcat_id, item)
      @catcache = {} unless @catcache

      cat = Rails.cache.fetch("cat_#{topcat_id}", expires: 1.hour) do
        cat = JSON.parse(SimaApi.get("https://www.sima-land.ru/api/v3/category/#{topcat_id}/?expand=active_sub_categories"))
        cat
      end

      sub_cat = cat['active_sub_categories'].find { |c| item['all_categories'].include? c['id'].to_s }

      if sub_cat
        sub_cat['name']
      else
        nil
      end
    end

    #def load_goods_by_category(category_id, only_hits: false, cat_prefix: nil, use_group: nil)
    #products = SimaProduct.where("data -> 'all_categories' ? :category", category: category_id).where(disabled:false)
    #SimaProduct.where("data->'all_categories' @> ?", JSON.generate(['24182'])).where.not("data->'all_categories' @> ?", JSON.generate(['12218'])).where(disabled:false)
    #    end

    def load_goods_by_category(category_id, only_hits: false, cat_prefix: nil, use_group: nil)
      #.where.not("data->'all_categories' @> ?", JSON.generate(['12218']))
      products =SimaProduct.where("data->'all_categories' @> ?", JSON.generate([category_id.to_s])).where(disabled:false)
      products.each do |item|
        add_product(item, cat_prefix: cat_prefix, category_id: category_id, use_group: use_group)
      end
    end

    def load_goods_by_arts(arts)
      items = SimaProduct.where(sid: arts)
      items.each do |item|
        #@log.info item
        add_product(item)
      end
    end

    def load_goods(brand_id)
      if /^\d+$/=~brand_id
        products =SimaProduct.where("data ->> 'trademark_id' = ?", brand_id.to_s).where(disabled:false)
      else
        products =SimaProduct.where("data ->> 'trademark' ->> 'slug'  = ?", brand_id.to_s).where(disabled:false)
      end
      products.each do |item|
        add_product(item)
      end

    end

    def get_serie_ids(series)
      inc_series = series.select { |s| not s.start_with? '-' }
      exc_series = series.select { |s| s.start_with? '-' }.map { |s| s.gsub(/^-/, '') }

      ret = []
      #by_id = {}
      inc_series.each do |s|
        SimaSerie.where("name ilike :n",n:s.downcase).each do |ss|
          next if exc_series.any? { |s| ss.name.downcase.include?(s.downcase) }
          ret << ss.sima_id
          #by_id[ss.sima_id] = ss.name
        end
      end

      ret
    end

    def load_goods_by_series(serie_ids)
      products =SimaProduct.where("data ->> 'series_id' IN (?)", serie_ids.map(&:to_s)).where(disabled:false)
      products.each do |item|
        add_product(item)
      end
    end

    def get_prices(sima_product)
      price = sima_product.buy_price
      buy_price = sima_product.buy_price
      price = sima_product.wholesale_price if sima_product.wholesale_price > 0

      rrp = nil
      if sima_product.data['retail_price']
        rrp = sima_product.data['retail_price'].to_f
      elsif sima_product.data['price_max']
        rrp = sima_product.data['price_max'].to_f
      else
        rrp = sima_product.data['price'].to_f if price.to_f * 1.24 < sima_product.data['price'].to_f
      end

      if (sima_product.shipping_cost.to_f > 1) && (sima_product.shipping_cost.to_f > (price.to_f * 0.03).ceil)
        new_price = (price.to_f + sima_product.shipping_cost.to_f * 0.83 - price.to_f * 0.03).ceil
        buy_price = sima_product.buy_price + sima_product.shipping_cost.to_f

        if new_price > price.to_f
          price = new_price
          rrp = (rrp.to_f + sima_product.shipping_cost.to_f).ceil if rrp.to_i > 0
        end
      end

      {price: price, rrp: rrp, buy_price: buy_price}
    end

    def load_skip_products(pid)
      @exclude_products += Product.where(purchase_id: pid, disabled: false).all.map { |pr| pr.art.to_s }
    end

    def run
      if @agent == nil then
        before
      end

      @global_skip_brands = ['TORNADICA', 'INNAMORE', 'INCANTO', 'Omsa', 'MALEMI', 'GLAMOUR', 'Aravia Professional', 'Aravia Organic']

      @log = Logger.new("log/dl-#{self.class.name.gsub('::', '_')}_#{@purchase.id}.txt")

      #@agent.set_proxy("**************", 8080) unless Rails.env.development?
      # @agent.set_proxy("*************", 8080) unless Rails.env.development?

      @product_queue = Queue.new

      @token = JWT_TOKEN

      #load_cat_tree_cached # unless Rails.env.development?

      @skip_brands = Brand.where(skip: true).map(&:brand_name)

      #@skip_brands = Purchase.where(dlclass: @purchase.dlclass).map { |p|
      #        p.skip_brands.to_s.gsub(' ', '').split("\n")
      #}.flatten

      # @skip_brands += @skip_brands.to_s.split("\n")

      if @global_skip_brands
        @skip_brands += @global_skip_brands
      end

      if @exclude_brands_local
        @skip_brands += @exclude_brands_local.split(',')
      end

      @skip_brands = @skip_brands.map { |s| s.strip.downcase }.select { |s| not s.blank? }.uniq

      @log.info "Skip brands: #{@skip_brands}"

      if @exclude_cats
        @exclude_cats = @exclude_cats.split(',').map { |c| c.gsub(/\D/, '') } if !@exclude_cats.is_a?(Array)
      else
        @exclude_cats = []
      end

      if @top_brands
        @top_brands = @top_brands.split(',').map { |c| c.strip.downcase }
      end

      @exclude_products = []

      # if @skip_spup_purchase
      #        @skip_spup_purchase.split(',').each {|p| load_skip_products(p)}
      # end

      if @purchase.is_pp
        Purchase.where(dlclass: 'Simaland::SimalandParser', is_pp: true).where('priority > ?', @purchase.priority).each do |p|
          next if p.id == @purchase.id
          load_skip_products(p.id)
        end
      else
        if @purchase.id != 267
          Purchase.where(dlclass: 'Simaland::SimalandParser', is_pp: true).where('priority >= ?', @purchase.priority).each do |p|
            next if p.id == @purchase.id
            load_skip_products(p.id)
          end
        end
      end
      # @agent.add_auth 'https://www.sima-land.ru', '<EMAIL>', 'EQvILqcQ'

      # begin
      # res = SimaApi.get 'https://www.sima-land.ru/api/v3/auth/'
      # rescue Mechanize::ResponseCodeError => e
      #        puts e
      # end
      # jwt=JSON.parse(res.body)['jwt']

      # load_brands

      # load_goods_by_arts('3572940')
      # apply_shipping_costs
      # return

      @pool = Concurrent::FixedThreadPool.new(1)

      # goods=load_goods(@brands['las-igras']['id'])
      if @sids && (not @sids.strip == '')
        load_goods_by_arts(@sids)
      end

      if @series && (not @series.strip.blank?)
        if @series.split(',').all? { |s| s.to_i > 0 }
          ids = @series.split(',')
        else
          ids = get_serie_ids(@series.split(','))
        end

        unless ids.empty?
          load_goods_by_series(ids)
        end

      end

      if @cats
        if @cats.is_a?(Array)
          @cats.each do |cat|
            load_goods_by_category(cat.to_i)
          end
        elsif !@cats.strip.blank?
          @cats.split(',').each do |cat|
            # puts cat
            next if cat.strip.start_with? '-'

            hits = false
            prefix = nil
            if cat.include? ':'
              (cat, prefix) = cat.split(':')
              if prefix == 'hits'
                hits = true
                prefix = nil
              end

              if prefix&.start_with? 'grp'
                use_group = prefix.gsub(/^grp/, '')
                prefix = nil
              end
            end
            load_goods_by_category(cat.strip.to_i, only_hits: hits, cat_prefix: prefix, use_group: use_group)
          end
        end
      end

      if @brands && (not @brands.strip.blank?)
        # 1957,18626
        @brands.split(',').each do |brand|
          load_goods(brand)
        end
      end

      @pool.shutdown
      @pool.wait_for_termination

      #merge_small_collections(9, true)

      #apply_shipping_costs
    end
  end
end
