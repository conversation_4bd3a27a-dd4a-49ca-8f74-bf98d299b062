require 'mechanize'
require 'rmagick'

class SpUploadBase
  attr_accessor :sp_login
  attr_accessor :sp_password
  attr_accessor :skip_uploaded
  attr_accessor :pid
  attr_accessor :sort_by_price
  attr_reader :dlparams

  def initialize
    @sp_login=''
    @sp_password=''
    @pid=''
    @col_ids=Hash.new
    @pic_added_to_col=[]
    @skip_uploaded=''

    @dlparams={:pid=>:text}

  end

  def login
    p2=@agent.get('http://spup.primavon.ru/api/getsess')
    puts p2.body
    need_reauth=true

    if p2.body!='no data'
      (phpsessid,auth,au)=p2.body.split ';'
      cookie = Mechanize::Cookie.new :domain => 'www.100sp.ru', :name => 'PHPSESSID', :value => phpsessid, :path => '/', :expires => (Date.today + 5000).to_s
      @agent.cookie_jar << cookie
      #cookie = Mechanize::Cookie.new :domain => 'www.100sp.ru', :name => 'auth', :value => auth, :path => '/', :expires => (Date.today + 5000).to_s
      #@agent.cookie_jar << cookie
      cookie = Mechanize::Cookie.new :domain => 'www.100sp.ru', :name => 'au', :value => au, :path => '/', :expires => (Date.today + 5000).to_s
      @agent.cookie_jar << cookie

      p=@agent.get('https://www.100sp.ru')
      if p.search('.profile-menu-wrapper').count>0
        need_reauth=false
      end
    end

    puts need_reauth

    if need_reauth
      @agent.cookie_jar.clear!
      begin
        page = @agent.get('https://www.100sp.ru/login')
      rescue Mechanize::ResponseCodeError => e
        page = e.page
      end

      login_form=page.form_with(:id=>'login-form')
      login_form['LoginForm[loginString]']='<EMAIL>'
      login_form['LoginForm[password]']='Tushkan65'
      page = @agent.submit(login_form, login_form.buttons.first)

      if page.search('.profile-menu-wrapper').count>0
        puts @agent.cookies
        phpsessid=@agent.cookies.find {|c| c.name=='PHPSESSID'}.value
        au=@agent.cookies.find {|c| c.name=='au'}.value if @agent.cookies.find {|c| c.name=='au'}

        @agent.get("http://spup.primavon.ru/api/savesess/#{phpsessid}/#{au}/#{au}")
      else
        sleep(30)
        throw 'Wrong 100sp login or password' if page.search('input#LoginForm_sms_code').count==0 and page.search('span.user a').first.nil?

        p2=@agent.get('http://spup.primavon.ru/api/getcode')

        raise 'Can''t get code' if p2.body=='no code'

        login_form=page.form_with(:id=>'login-form')
        login_form['LoginForm[sms_code]']=p2.body
        page = @agent.submit(login_form, login_form.buttons.first)

        if page.search('.profile-menu-wrapper').count==0
          puts "Can't authorize"
          exit
        end
      end
    end

  end

  def after(job)
    #@purchase.reload
    # @purchase.status=0
    #@purchase.save
  end

  def error(job, exception)
    @purchase.reload
    @purchase.message="Ошибка загрузки на 100sp: #{exception.to_s}"
    puts exception.backtrace
    #@log.info exception.backtrace
    @purchase.save
  end

  def success(job)
    @purchase.reload
    @purchase.message="Данные загружены на 100sp успешно"
    @purchase.save
  end

end
