# frozen_string_literal: true

class ArtiInvoiceAdapter < SkuInvoiceAdapter
  def initialize
    super
    @name_col_name = 'Товары (работы, услуги)'
    @code_col_name = 'Код'
    @q_col_name = 'Кол-во'
    @price_col_name = 'Цена'
  end

  def get_invoice_name(sheet)
    sheet.each do |row|
      next unless row
      if row[0].to_s.include? 'Счет на оплату'
        return row[0].to_s
      end
    end
    ''
  end

end
