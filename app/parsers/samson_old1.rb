#encoding: utf-8
#encoding: utf-8

require 'downloader'

class SamsonOld1 < Downloader
  include InvoiceSimple

  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password,'stand_out_brands'=>:text,'price_file'=>:file,'csv_file'=>:file}
    @real_price={}
    @retail_price={}
    @base_price={}
    @barcode={}
    @purchase_tags=['Лайма','Брауберг канц','Хатбер','Рюкзаки']
    super
  end

  def sale_collection_name
    'РАСПРОДАЖА'
  end

  def get_sku(product)
    "SAMS#{product.art}"
  end


  def read_sale(book)
    sheet = book['Снижение цен']

    sheet.sheet_data.rows[3..-1].each do |row|
      price=row[4].value
      rate=row[5].value.to_i
      next if rate.nil?
      art=row[0].value.to_s
      #puts art
      next if rate<25

      q=1.15
      q=1.25 if rate>40

      @price[art]=(price*q).ceil
    end
  end

  def read_json url
    @log.info url
    page=@agent.get url

    js=JSON.parse(page.body)
    #js=JSON.parse(File.open(file).read)

    js['data'].each {|prod|
      art=prod['sku'].to_s

      @barcode[art]=prod['barcode']

      brand=prod['brand']
      @used_brands<<brand unless @used_brands.include? brand

      @brands[art]=brand

      #puts art
      desc=prod['description']
      desc+=' '+prod['characteristic_list'].reject { |el| el.include? 'Количество в наборе' }.map {|el| el.gsub(/[. ]+$/,'')}.join('. ') unless prod['characteristic_list']==''
      #puts desc


      pic_urls=prod['photo_list']
      #puts pic_urls

      @pics[art]=pic_urls

      package_list=prod['package_list'].map{|el| [el['type'],el['value']]}.to_h
      price_list=prod['price_list'].map{|el| [el['type'],el['value']]}.to_h
      #puts package_list


      #puts package_list['min_opt'].to_i
      @min_opt[art]=package_list['min_opt'].to_i

      desc+=". Упаковка #{package_list['min_opt']} шт"
      desc.gsub(/\.+/,'.')

      @descs[art]=desc

      @base_price[art]=price_list['contract'].to_f.ceil
    }

    #puts js['meta']['pagination']['next']
    read_json js['meta']['pagination']['next'] if js['meta']['pagination']['next']
  end

  def use_local_pics
    path='/home/<USER>/samson_pics/x'

    pics=Hash.new
    Dir.glob(path+'/**/*.jpg').each do |f|

      t=f.gsub(path+'/','')
      #puts t
      s=t.split '_'
      art=s[0]
      #puts art

      if pics.has_key? art
        pics[art]<<f
        pics[art].sort!
      else
        pics[art]=[f]
      end
    end

    #puts pics

    @pics[art]=files
  end

  def process_row(row,cat)
    art=row[0].value
    return if art.nil?
    art=art.to_s.gsub('.0','')
    #puts art
    @log.info art

    brand=''
    brand=row[3].value unless row[3].value.nil?

    name=row[1].value

    price=@base_price[art]
    return if price.nil?

    #real_price=row[13].value.to_s.to_f.ceil

    return if @skipBrands.any? {|br| name.upcase.include? br}


    price=price.ceil
    @log.info "Price #{price}"
    stock=row[9].value

    @log.info "Stock #{stock}"

    return if stock<1

    minorder=@min_opt[art]

    minorder=row[6].value.split('/')[0].to_i if minorder==0

    @real_price[art]=price if @real_price[art].nil?

    @log.info "Min order #{minorder}"

    if @skipCats.include? cat
      @log.info 'Skip cat'
      return
    end

    cat_id='101022'

    cat_id=@cat_ids[cat] if @cat_ids.include? cat
    cat=@cat_map[cat] if @cat_map.include? cat


    sale=false

    if @price.has_key? art
      sale=true
      price=@price[art]
      @log.info "Price sale #{price}"
    end

    @log.info "Cat name #{cat}"

    size=''
    size="Упаковка #{minorder} шт. Цена за 1" if minorder>1

    desc=''

    pics=[]
    desc=@descs[art].to_s

    pic_urls=@pics[art]

    if pic_urls
      pic_urls.each_with_index { |url,i|
          pic=savePic(url,"#{art}~#{i}",true,false,false, :active, nil, show_order: i) unless Rails.env.development?
          pics<<pic
      }
    end


    if @out_brands[brand]
      col=addCollection(cat,cat_id,@out_brands[brand])
    else
      col=addCollection(cat,cat_id)
    end

    if minorder>1
      add_price=(price/20.0).ceil
      add_price=2 if add_price>2

      price=price*1.14
      price+=add_price
    end

    @log.info "Calculated price #{price}"
    @log.info "Real price #{@real_price[art]}"

    #      if price.to_i==@real_price[art].to_i
    #        price=price*1.1
    #end

    @log.info "minorder #{minorder.inspect}"
    @log.info "price #{price.inspect}"
    @log.info "@real_price[art] #{@real_price[art].inspect}"
    @log.info "(@real_price[art]*1.1).ceil #{(@real_price[art]*1.1).ceil}"
    @log.info " minorder==1 and price>400 #{ minorder==1 and price>400}"

    if minorder==1
      price=(@real_price[art]*1.14).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder==1 and price>400
      price=(@real_price[art]*1.12).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder==1 and price>1000
      price=(@real_price[art]*1.07).ceil
      @log.info "Recalculated price #{price}"
    end

    if minorder==1 and price>2000
      price=(@real_price[art]*1.02).ceil
    end

    if minorder==1 and price>3000
      price=@real_price[art].ceil
    end

    orig_art=art

    if @real_price[art]<6 and minorder>1
      price=(@real_price[art]*1.15*10).ceil
      art="#{art}-10"
      size="Цена за 10 шт" if minorder>1
      @real_price[art]=@real_price[orig_art].to_f*10
      #@buy_price[art]=@buy_price[orig_art].to_f*10
    elsif @real_price[art]<10 and minorder>1
      price=(@real_price[art]*1.14*5).ceil
      art="#{art}-5"
      @real_price[art]=@real_price[orig_art].to_f*5
      #@buy_price[art]=@buy_price[orig_art].to_f*5
      size="Цена за 5 шт" if minorder>1
    end

    desc=ActionView::Base.full_sanitizer.sanitize(desc)
    desc.gsub!(/[\r\n]+/,'. ')
    desc.gsub!(/ +/,' ')
    desc.gsub!(/\.+/,'.')
    desc.strip!
    desc.gsub!(/[.,]+$/,'.')

    #desc="#{size}. #{desc}" if size!=''

    rrp=@retail_price[art]
    rrp=(price*1.4).round if rrp.nil? or rrp<price*1.3

    p=addProduct(col,art,name,price,desc,[size],pics,nil,cat_id,nil,barcode: @barcode[orig_art],buy_price: @real_price[art],rrp:rrp,sale:sale,brand_name: @brands[orig_art])

  end

  def read_file(book)
    sheet = book['Прайс-заказ']

    start_row=0
    sheet.sheet_data.rows.each_with_index do |row,i|
      if row[0] and row[0].value=='Код'
        start_row=i+3
        break
      end
    end

    #puts start_row

    cat=''
    sheet.sheet_data.rows[start_row..-1].each do |row|
      if row[13].nil? or row[13].value.nil? or row[13].value.to_s==''
        cat=row[0].value
        next
      end

      @pool.post do
        process_row(row,cat)
      end
    end

  end

  def read_cat_map
    return  unless File.exist? '/home/<USER>/samson_map.xls'

    book = Spreadsheet.open '/home/<USER>/samson_map.xls'

    sheet = book.worksheet 0
    sheet.rows.each do |row|
      cat=row[0]
      cat2=row[1]
      cat_id=row[2]

      @nil_ = cat_id.nil?
      cat_id='100000' if @nil_
      cat2=cat if cat2.nil?

      if cat2=='УБРАТЬ'
        @skipCats<<cat
        next
      end

      @cat_map[cat]=cat2
      @cat_ids[cat]=cat_id
    end

  end


  def dl_price(href,dest_file)
    @log.info href

    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest'}
    if href.include? 'pricelist'
      res=@agent.post href, {FILE: 'PRICE_LIST_ORDER'}, ajax_headers
    else
      res=@agent.post href,{FILE: 'ASSORTMENT_CSV'},ajax_headers
    end

    r=JSON.parse(res.body)
    @log.info res.body

    url=r['success']
    ext='zip'
    ext='7z' if url.end_with? '7z'
    @agent.download(url,"/tmp/samson.#{ext}")

    File.unlink dest_file if File.exist? dest_file

    if ext=='7z'
      system("7z e -o/tmp/samson/ /tmp/samson.7z")
      system("mv /tmp/samson/*.xlsx #{dest_file}")
    else
      Zip::File.open("/tmp/samson.zip") do |zipfile|
        zipfile.each do |entry|
          entry.extract(dest_file)
          break
        end
      end
    end

  end

  def read_csv(file)
    doc=InventoryDocument.create(
      user_id:1,
      doc_type:InventoryDocument.doc_types[:revalue],
      name: "Обновление цен Samson",
      posted:false
    )

    CSV.foreach(file,:col_sep=>';', :headers => true,:encoding=>'Windows-1251',:quote_char => "}") do |fields|
      @real_price[fields['Код']]=fields['Ваша цена'].gsub(',','.').to_f.ceil

      @retail_price[fields['Код']]=fields['Рекомендованная розничная цена'].to_f


      p=InventoryProduct.find_by_sku("SAMS#{fields['Код']}")
      if p
        p.stock.each do |st|
          size=st['size']
          buy_price=fields['Ваша цена'].gsub(',','.').to_f
          next if st['buy_price'].to_f==buy_price

          retail_price=st['retail_price'].to_f
          sp_price=st['sp_price'].to_f
          rrp=st['rrp'].to_f

          new_rrp=@retail_price[fields['Код']]
          new_rrp=(buy_price*2).round if new_rrp.nil? or new_rrp<buy_price*1.7
          rrp=new_rrp if new_rrp>rrp

          new_sp_price=buy_price*1.57
          new_sp_price=new_sp_price.ceil if new_sp_price>5
          sp_price=new_sp_price if new_sp_price>sp_price

          new_retail_price=buy_price*1.6
          new_retail_price=new_retail_price.ceil if new_retail_price>5
          retail_price=new_retail_price if new_retail_price>retail_price

          rrp=sp_price*1.1 if rrp<sp_price*1.1

          InventoryDocumentLine.create(
            inventory_product_id:p.id,
            size:size,
            amount_change:0,
            buy_price:buy_price,
            retail_price:retail_price,
            sp_price:sp_price,
            rrp:rrp,
            comment:"Переоценка",
            inventory_document: doc,
            line_type: :relative)
        end
      end

      @retail_price[fields['Код']]=fields['Рекомендованная розничная цена'].to_f
    end
  end

  def run
    if @agent==nil then before end


    @price=Hash.new
    @cat_map=Hash.new
    @cat_ids=Hash.new
    @min_opt=Hash.new(0)

    @pool = Concurrent::FixedThreadPool.new(5)

    @skipCats=[]


    #read_cat_map

    @skipBrands=['PASABAHCE', 'PATERRA','ROUBLOFF','VICTORINOX','GILLETTE','ФЕЯ','ALWAYS','NATURELLA','DISCREET','TAMPAX','ORAL-B','CALGON','IDEA',
                 'УТЕНОК','МИФ','АРИЕЛЬ','УШАСТЫЙ','НЕВСКАЯ ','TIDE','ARIEL','LENOR','МИФ','FINISH','GLADE ','AIRWICK','PERSIL','LOSK','ЛАСКА','DETTOL',
                 'DURACELL','PACLAN','AURA','ЧИСТАЯ','АЛЬТ','FABULA','BEFLER','FAIRY','MR. PROPER','GRIZZLY','GRASS'
    ]

    h=JSON.parse(@stand_out_brands)
    @out_brands={}
    h.each {|k,v| v.each {|v2| @out_brands[v2]=k}}

    @used_brands=[]

    @agent.set_proxy("**************", 8080) unless Rails.env.development?
    #
    #use_local_pics
    #read_xml(@xml_file) unless @xml_file.nil?

    @pics={}
    @descs={}
    @brands={}

    read_json 'https://api.samsonopt.ru/v1/assortment/?api_key=b41407614a3372cb4584dbffeadd9aae'

    if @price_file.nil?
      page=@agent.get 'https://www.samsonopt.ru/zakaz/services/prices/'

      login_form=page.forms[1]
      login_form.USER_LOGIN=@login
      login_form.USER_PASSWORD=@password
      page = @agent.submit(login_form, login_form.buttons.first)

      if Rails.env.development?
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_assortment.php','e:/samson.csv')
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_pricelist.php','e:/samson.xlsx')
        read_csv('e:/samson.csv')
        book = RubyXL::Parser.parse('e:/samson.xlsx')
      else
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_assortment.php','/tmp/samson.csv')
        dl_price('https://www.samsonopt.ru/ajax/public/service/get_pricelist.php','/tmp/samson.xlsx')
        book = RubyXL::Parser.parse('/tmp/samson.xlsx')
        read_csv('/tmp/samson.csv')
      end
    else
      book = RubyXL::Parser.parse(@price_file)
      read_csv(@csv_file)
    end

    read_sale book
    @log.info @price

    read_file book

    @pool.shutdown
    @pool.wait_for_termination

    File.open('/tmp/samson_brands', 'w') { |file| file.write(@used_brands.join("\n"))}
  end

end
