require 'creek'

module Altai
  class AltaiParser < Downloader
    attr_accessor :price_file

    def initialize
      @price_file=''
      @dlparams={'price_file'=>:file}

      super
    end

    def get_cat_id(cat,name)
      c=cat.downcase
      n=name.downcase

      return '108515' if cat.include? 'ча' or cat.include? 'кофе'

      cat_type = '107861'
      cat_type = '107353' if n.include? 'шампу'
      cat_type = '108812' if (n.include? 'бальза' or name.include? 'кондиц') and (c.include? 'волос' or c.include? 'кож')
      cat_type = '107847' if c.include? 'рта'
      cat_type = '109858' if c.include? 'трав' or n.include? 'трав'

      cat_type = '107361' if c.include? 'лиц'
      cat_type = '108411' if c.include? 'лиц' and n.include? 'увл'
      cat_type
    end

    def process_page(url,name,price,line_no,extra,cat)
      page=@agent.get url

      pic_urls=[page.search('.images a').first.attr('href').to_s]

      text_data={}
      page.search('h6.title-holder').each do |h|
        k=h.search('span').first.text.strip
        v=h.next_element.text.strip
        text_data[k]=v
      end

      desc=text_data.delete('Описание')

      pics=[]
      pic_urls.each_with_index do |pic_url, i|
        pics<<savePic(pic_url,"#{line_no}~#{i}",true)

      end

      cat_type=get_cat_id(cat,name)

      col=addCollection(cat,cat_type)
      addProduct(col,line_no,name,price,desc,[],pics,extra)
    end

    def process_page_sibireco(url,name,price,line_no,extra,cat,desc,pic_url)
      page=@agent.get url

      pics=[savePic(pic_url,"#{line_no}",true)]

      n=name.downcase

      cat_type=get_cat_id(cat,name)

      col=addCollection(cat,cat_type)
      addProduct(col,line_no,name,price,desc,[],pics,extra)
    end

    def process_page_farm(url,name,price,line_no,extra,cat)
      page=@agent.get url

      pic_urls=[page.search('.main-image a').first.attr('href').to_s]

      desc=page.search('.product-short-description').text.strip

      pics=[]
      pic_urls.each_with_index do |pic_url, i|
        pics<<savePic(pic_url,"#{line_no}~#{i}",true)
      end

      n=name.downcase

      cat_type=get_cat_id(cat,name)

      col=addCollection(cat,cat_type)
      addProduct(col,line_no,name,price,desc,[],pics,extra)
    end

    def process_page_altaiflora(url,name,price,line_no,extra,cat)
      page=@agent.get url

      pic_urls=[page.search('img#productZoom').first.attr('src').to_s]

      page.search('.product_desc ul').remove
      page.search('.product_desc h1').remove

      desc=page.search('.product_desc').first.text.strip

      pics=[]
      pic_urls.each_with_index do |pic_url, i|
        pics<<savePic(pic_url,"#{line_no}~#{i}",true)
      end

      n=name.downcase

      cat_type=get_cat_id(cat,name)

      col=addCollection(cat,cat_type)
      addProduct(col,line_no,name,price,desc,[],pics,extra)
    end

    def process_page_shop(url,name,price,line_no,extra,cat)
      page=@agent.get url

      if page.search('.image_carousel a.cloud-zoom-gallery').count>0
        pic_urls=page.search('.image_carousel a.cloud-zoom-gallery').map {|a| a.attr('href').to_s}.uniq
      else
        pic_urls=[page.search('a#zoom1').first.attr('href').to_s]
      end

      page.search('#tab-description .tags').remove

      desc=page.search('#tab-description').first.text

      pics=[]
      pic_urls.each_with_index do |pic_url, i|
        pics<<savePic(pic_url,"#{line_no}~#{i}",true)
      end

      n=name.downcase

      cat_type=get_cat_id(cat,name)

      col=addCollection(cat,cat_type)
      addProduct(col,line_no,name,price,desc,[],pics,extra)
    end

    def process_file(file)
      creek = Creek::Book.new @price_file
      sheet = creek.sheets[0]

      cat=''
      sheet.simple_rows.each do |row|
        line_no=row['A']
        if row['B'].nil? or row['B'].empty?
          cat=line_no.to_s.gsub(/НОВИНКА!*/i,'').strip
          cat.gsub!(/\s+/,' ')
          cat.gsub!(' на основе нафталановой нефти "NAFTA dermalis"','')
          cat.gsub!(', 60 Ф/П','')
          cat='Чаговый чай' if cat=='ЧАГОЧАЙ'
        end


        href=row['K']
        next if href.nil?
        price=row['G'].to_f.ceil
        name=row['B']
        name.gsub!(/НОВИНКА!*/i,'')
        name.gsub!('«Зеленый Алтай»','')
        name.gsub!('(НОВИНКА!)','')
        name.gsub!(/Акция.*/,'')
        name.gsub!("РАСПРОДАЖА!!!",'')
        name.strip!


        cat2=cat
        if /(\([^(]*?ф\/п\)$)/=~cat2
          t=$1
          name="#{name} #{t}"
          cat2.gsub!(t,'')
          cat2.strip!
        end


        begin
          extra=Digest::MD5.hexdigest(row['B'])
          if href.include? 'shop.green-altai'
            process_page_shop(href,name,price,line_no,extra,cat2)
          elsif href.include? 'green-altai'
            process_page(href,name,price,line_no,extra,cat2)
          elsif href.include? 'sibereco'
            desc=row['L']
            pic_url=row['M']
            process_page_sibireco(href,name,price,line_no,extra,cat2,desc,pic_url)
          elsif href.include? 'farm-product'
            process_page_farm(href,name,price,line_no,extra,cat2)
          elsif href.include? 'altaiflora'
            process_page_altaiflora(href,name,price,line_no,extra,cat2)
          end
        rescue Mechanize::ResponseCodeError => e
          next if e.response_code == '404'
          raise e
        end


      end
    end

    def run
      if @agent==nil then before end

      process_file(@price_file)

    end

  end
end
