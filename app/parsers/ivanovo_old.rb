#encoding: utf-8

require 'downloader'

class IvanovoOld < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password,'previous_purchase'=>:text}
    @cats=Hash.new
    @pics=Hash.new
    @descs=Hash.new

    super
  end


  ######### СОРОЧКИ!

  def processPage(url,cat)
    tries ||= 2

    cat.gsub!('Постельное белье-Комплекты постельного белья-','')
    cat.gsub!('Постельное белье-','')

    @log.info(url)
    page = @agent.get(url)
    puts url

    name=page.search('span#item_title').text.strip
    puts name

    titles=page.search('.item_info_section dt').map {|t| t.text.strip}
    vals=page.search('.item_info_section dd').map {|t| t.text.strip}

    props=Hash[titles.zip(vals)]

    art=props['Артикул']
    sz=props['Размер']

    if not cat.include? '-' and props.has_key? 'Категория'
      cat=cat+'-'+props['Категория']
      props.delete('Категория')
    end

    #if not cat.include? 'Подушки' and not cat.include? 'Покрывала'
    #cat+='-'+sz if not sz.nil?
     # props.delete('Размер')
    #end



    props.delete('Артикул')

    puts cat

    pic_urls=[]
    pid=0

    sizes=[]
    variants=Hash.new

    product_with_options=false
    product_with_sizes=false

    if /JCCatalogElement\((.*?)\);/=~page.body
      js_data=$1
      #js_data=js_data.force_encoding('utf-8').encode
      js_data.gsub!("'",'"')
      d=JSON.parse js_data
      price=nil

      return if d['PRODUCT'].has_key? 'PRICE' and d['PRODUCT']['PRICE']==false

      price=nil

      price=d['PRODUCT']['PRICE']['VALUE_NOVAT'] if d['PRODUCT'].has_key? 'PRICE'

      if d.has_key? 'OFFERS' and d['OFFERS'].length>0
        d['OFFERS'].each do |o|
          next if o['PRICE']==false
          price=o['PRICE']['VALUE']
          break
        end
      end



      @log.info price
      puts price

      return if price.nil?

      if d.has_key? 'OFFERS'

        d['OFFERS'].each do |o|

          product_with_options=true if o['TREE'].is_a?(Hash) and o['TREE'].has_key? 'PROP_28'
          product_with_sizes=true if o['TREE'].is_a?(Hash) and o['TREE'].has_key? 'PROP_29'

          pic_urls=[o['DETAIL_PICTURE']['SRC']]

          next unless o['CAN_BUY']

          if o['TREE'].is_a?(Hash) and o['TREE'].has_key? 'PROP_29'
            v=o['TREE']['PROP_29']
            s=page.search("li[data-treevalue='29_#{v}'] span").text.strip
            sizes<<s unless sizes.include? s
          end

          if o['TREE'].is_a?(Hash) and o['TREE'].has_key? 'PROP_28'
            n=o['NAME']
            n.gsub!(/[0-9]+ размер,? ?/,'')
            n.gsub!('()','')
            variants[n]=o['DETAIL_PICTURE']['SRC']
          end
        end
      else
        pic_urls=d['PRODUCT']['SLIDER'].map { |s| s['SRC'].to_s } unless d['PRODUCT']['SLIDER'].nil?
      end

      pid=d['PRODUCT']['ID']
    end

    desc1=props.map{|k,v| "#{k}: #{v}"}.join ', '


    pics=[]

    pic_urls.each_with_index do |url,i|
      pics<<savePic(url,"#{pid}~#{i}",true)

      break
    end

      desc=page.search('.bx_item_description p').text.strip

    if @used_desc[cat].nil?
      @used_desc[cat]=[desc]
    elsif @used_desc[cat].include? desc
      desc=''
    else
      @used_desc[cat]<<desc
    end

    desc=desc1+' '+desc
    desc.strip!

    puts desc

    @log.info variants

    return if product_with_options and variants.length==0
    return if product_with_sizes and sizes.length==0

    catType='1004'
    if cat.include? 'Одежда'
      catType='850'
      catType='870' if cat.include? 'Муж'
      catType='2426' if cat.include? 'Дет'
    end

    if cat.include? 'детскую кроватку'
      pics=[pics[0]] if pics.length>0
    end

    col=addCollection(cat,catType)

    desc.gsub!('Внимание! Если на складе числится чётное количество наволочек, заказывать их можно только парами.','')

    price=(price.to_f*1.05).ceil.to_s

    if variants.length>0
      variants.each_with_index do |(k,v),i|
        savePic(v,"#{pid}~var#{i}",true)
        addProduct(col,art,'',price,k+'. '+desc,sizes,["#{pid}~var#{i}"])
        desc=''
      end
    else
      addProduct(col,art,name,price,name+'. '+desc,sizes,pics)
    end

  rescue Mechanize::Error => e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(url,cats,proc_pages=false)
    @log.info(url)
    puts url
    page = @agent.get(url)


    page.search(".bx_catalog_item_container .bx_catalog_item_title a").each do |a|
        processPage(a.attr('href'),cats.join('-'))
    end


    if proc_pages
      seen=[]
      page.search('a').each {|a|
          href=a.attr('href').to_s
          next if seen.include? href
          if /PAGEN_/=~href
            seen<<href
            processCat(href,cats)
          end

      }
    end

  end


  def run

    #process_previous

    @used_desc=Hash.new
    @agent.agent.http.verify_mode = OpenSSL::SSL::VERIFY_NONE

    page = @agent.get('http://ivanovotextil.ru/personal/?login=yes')
    login_form=page.form_with(:name => 'form_auth')
    login_form.USER_LOGIN=@login
    login_form.USER_PASSWORD=@password

    page = @agent.submit(login_form, login_form.buttons.first)

    page = @agent.get('http://ivanovotextil.ru/catalog/')

    page.search('ul.cd-dropdown-content > li:not(.go-back)').each {|li1|
      a1=li1.search('>a')[0]
      cat1=a1.text.strip

      li1.search('ul.cd-secondary-dropdown > li:not(.go-back)').each {|li2|
        a2=li2.search('>a')[0]
        cat2=a2.text.strip

        has_third_cat=false

        li2.search('>ul > li:not(.go-back)').each {|li3|
          a3=li3.search('>a')[0]
          cat3=a3.text.strip
          processCat(a3.attr('href').to_s,[cat1,cat2,cat3],true)
          has_third_cat=true
        }

        processCat(a2.attr('href').to_s,[cat1,cat2],true) unless has_third_cat

      }
    }
  end


#handle_asynchronously :run

end