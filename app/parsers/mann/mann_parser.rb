#encoding: utf-8

require 'downloader'
require 'spreadsheet'

module Mann
  class MannParser < Downloader
    attr_accessor :stock_file

    def initialize
      @stock_file=''
      @dlparams={}
      @descs=Hash.new ''
      @pics=Hash.new
      @cats=Hash.new
      super
    end

    def search_url(name)
      tries ||= 3

      page = @agent.get("https://www.mann-ivanov-ferber.ru/book/search.ajax?limit=1&q=#{name}")
      sleep(0.3+rand(0.2))
      r = JSON.parse(page.body)
      return r['books'][0]['url'] if r['books'] && r['books'][0]

    rescue Mechanize::ResponseCodeError => ex

      return nil if ex.response_code == '404'

      sleep((4-tries)*5)
      tries-=1
      retry unless tries==0
      nil
    end

    def process_page_new(page,force_art=nil)

      if not force_art.nil?
        art=force_art
      else
        if /(978[0-9-]{11,})/=~page.to_s
          art=$1.gsub('-','')
        elsif /(463[0-9-]{11,})/=~page.to_s
            art=$1.gsub('-','')
        else
          return
        end
      end

      pic=[]
      pic=page.search('section.l-book-card img.img')
      pic=page.search('section.l-book-card img.img-cover') if pic.empty?
      pic=pic[0].attr('src') unless pic[0].nil?

      pic.gsub!('1.00x-thumb','2.00x-thumb')

      desc=page.search('section.l-about-book .description').text.strip

      @log.info art
      if art==nil then return end

      pics=[savePic('http://www.mann-ivanov-ferber.ru/'+pic,art,true)]

      page.search('section.l-dualpages .slider-wrap img.cover')[0..2].each_with_index do |pic,i|
        src=pic.attr('hires-src')
        src=pic.attr('lowres-src') if src.nil?
        next if src.nil?

        src.gsub!('1.00x-thumb','2.00x-thumb')

        pics<<savePic(src,"#{art}~#{i+1}",true)
      end


      @pics[art]=pics
      @descs[art]=desc


    rescue Mechanize::Error
      return
    rescue ArgumentError
      return
    end


    def processPage(url,force_art=nil)
      @log.info(url)
      puts url
      page = @agent.get(url)

      body=page.body.gsub('<a href="http://www.toms.com/">TOMS</a>','TOMS')
      page = Nokogiri::HTML(body)

      if page.search('body')[0].attr('data-ng-controller')=='Popup'
        process_page_new(page,force_art)
        return
      end

      if page.search('.selbook form#mc-embedded-subscribe-form').count==1 then return end

      if not force_art.nil?
        art=force_art
      else
        if /(978[0-9-]{11,})/=~body
          art=$1.gsub('-','')
        else
          return
        end
      end

      if /'catName': '(.*?)'/=~body
        cat=$1
        @cats[art]=cat
      elsif page.search('div.mm li a.sel').length>0
        cat=page.search('div.mm li a.sel')[0].text
        @cats[art]=cat
      end

      return if page.search('td#cover img').length==0

      pic=page.search('td#cover img')[0].attr('src')

      desc=page.search('div.description').text.gsub('О чем эта книга','').strip

      @log.info art
      if art==nil then return end

      pics=[savePic('http://www.mann-ivanov-ferber.ru/'+pic,art,true)]


      i=1
      page.search('div.description img').each do |pic|
        sleep(rand(0.3)+0.3)
        pics<<savePic('http://www.mann-ivanov-ferber.ru/'+pic.attr('src'),"#{art}~#{i}",true)

        i+=1
      end


      @descs[art]=desc
      @pics[art]=pics


    rescue Mechanize::Error
    end

    def process_file(f)
      book = RubyXL::Parser.parse(f)

      sheet=book[0]
      sheet.sheet_data.rows.each_with_index do |row, k|
        next if k==0

        isbn=row[6].value
        cat=row[11].value


        name=row[4].value
        code=row[0].value.gsub(/^0+/,'')
        price=(row[2].value.to_f*0.65).ceil
        rrp=row[2].value.to_f
        weight = (row[15].value.to_f*1000*1.1).ceil

        #next if isbn.nil?
        if isbn
          isbn=isbn.to_s.gsub('.0','').gsub(/[^0-9]/,'')
        else
          isbn=''
        end

        @log.info "file isbn #{isbn}"
        @log.info row[14].value
        @log.info @pics[isbn]

        url = row[14].value

        url = nil if url&.include?('google.com') || url&.include?('query')

        unless url&.start_with? 'http'
          url = search_url(name)
        end

        if url
          begin
            processPage(url,code)
          rescue
            next
          end
        else
          next
        end


        cat=@cats[code] if @cats.has_key? code

        pics=[]
        pics=@pics[code] if @pics.has_key? code

        cat='Вне серии' if cat.nil?

        col=addCollection(cat,'4695')
        addProduct(col,code+' '+isbn,name,price,@descs[code],[],pics,nil,0,nil,rrp:rrp, weight: weight)
      end
    end


    def run
      if @agent==nil then before end

      @file_path=Spup2::Application.config.data_path+self.class.name.gsub('::', '_')
      FileUtils.mkpath(@file_path) unless File.exist? @file_path

      if Rails.env.development?
        file = "c:\\users\\<USER>\\downloads\\price111.xlsx"
      else
        file=@agent.get('https://www.mann-ivanov-ferber.ru/b2b/resell/price.xlsx').save(@file_path+'/mann.xlsx')
      end
      Cache.set_value('mann','price_file',file)

      process_file(file)

    rescue  Exception => e
        puts e.backtrace
        raise
    end

  end
end
