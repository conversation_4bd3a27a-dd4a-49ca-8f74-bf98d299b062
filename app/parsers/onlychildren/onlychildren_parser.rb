module Onlychildren
  class OnlychildrenParser < Downloader
    def initialize
      @dlparams={}

      super
    end

    def find_link(art)
      href=Cache.get_value()
    end

    def process_page(url,arts)
      checkStop

      @log.info url
      @log.info arts
      puts url

      begin
        page = @agent.get(url)
      rescue Mechanize::ResponseCodeError => exception
        return
      end

      description = page.search('div[data-section="description"] div.object-additional__column-large').text
      description=ActionView::Base.full_sanitizer.sanitize(description)

      if specs = page.at('div[data-section="characteristics"] div.object-additional__column-large')
        specs = page.search('div[data-section="characteristics"] div.object-additional__column-large').text
        specs=ActionView::Base.full_sanitizer.sanitize(specs)
      end

      desc="#{desc}\n#{specs}" if specs

      name=page.search('h1[itemprop=name]').text.to_s.strip

      col_name=page.search('ul.content__breadcrumbs a')[1..2].map {|a| a.text.to_s.strip}.join ' - '

      pic_pos={}

      pic_urls=page.search('a[data-fancybox=gallery]').map {|a| a.attr('href').to_s}

      return if pic_urls.empty?

      page.search('.object-photo__additional-photo div[data-vendor]').each_with_index do |div,i|
        art2=div.attr('data-vendor').to_s
        pic_pos[art2]=[] if pic_pos[art2].nil?

        pic_pos[art2]<<pic_urls[i]
      end

      arts.each do |art,product_data|
        if page.at("li[data-object-vendor=#{art}]")
          variant=page.at("li[data-object-vendor=#{art}] img").attr('alt').to_s.downcase
          pics2=pic_pos[art]
          pics=[]

          other_color=''
          if pics2.nil? or pics2.empty?
            pics2=[pic_urls.first]
            other_color='На картинке может быть другой цвет. '
          end

          pics2.each_with_index do |pic_url, i|
            pics<<savePic(pic_url,"#{art}~#{i}",true)


          end
          col=addCollection(col_name,get_cat_id(col_name,name))

          n=name
          if variant.downcase!=name.downcase and not name.downcase.include? variant.downcase
            n="#{name}, #{variant}"
          end

          addProduct(col,art,n,product_data[:price],"#{other_color}#{description}",product_data[:sizes],pics)

        end
      end

    end


    def get_cat_id(cat1,name1)
      cat=cat1.downcase
      name=name1.downcase

      r=1267
      r=1202 if cat.include? 'скейт' or cat.include? 'круиз'
      r=109827 if cat.include? 'самокат'
      r=101046 if cat.include? 'вело запч'
      r=109863 if cat.include? 'единобор'
      r=826 if cat.include? 'футбол'
      r=109865 if cat.include? 'волейб'
      r=827 if cat.include? 'баскетб'
      r=830 if cat.include? 'хокке'
      r=1418 if cat.include? 'теннис' or cat.include? 'бадм'
      r=109864 if cat.include? 'дартс' or cat.include? 'билья'
      r=2842 if cat.include? 'художеств'
      r=828 if cat.include? 'плаван'
      r=109842 if cat.include? 'коньки'

      r=109600 if cat.include? 'тюби'
      r=1571 if cat.include? 'сноуб' or cat.include? 'лыжи'

      r=100395 if cat.include? 'туризм'
      r=100393 if cat.include? 'палатк'
      r=100392 if cat.include? 'рюкзак'
      r=100391 if cat.include? 'коврик' or cat.include? 'спальны'

      r
    end

    def process_file(f)
      reader = RubyXL::Parser.parse(f)
      sheet=reader.worksheets.first

      @products={}

      sheet.sheet_data.rows.each_with_index {|row, index|
        next if row.nil? or index == 0

        next if row[0].nil? or row[5].nil? or row[7].nil?

        name = row[8].value.to_s.strip

        next if name.start_with? '*'

        vendor_code = row[7].value.to_s
        link = row[5].value.to_s

        category = row[1].value.to_s
        subcategory = row[2].value.to_s
        category += ' - '+subcategory

        price = row[12].value
        retail_price = row[11].value

        #next if price*1.2>retail_price
        if price>=retail_price
          price=retail_price*0.8
        end

        size=name.split.last

        @products[link]={} if @products[link].nil?
        if @products[link][vendor_code].nil?
          @products[link][vendor_code]={name:name,price:price,sizes:[size]}
        else
          @products[link][vendor_code][:sizes]<<size
        end
      }
    end

    def sign_in
      request_headers = {
        'Content-Type' => 'application/x-www-form-urlencoded'
      }

      request_params = {
        backurl: "/auth/",
        AUTH_FORM: "Y",
        TYPE: "AUTH",
        USER_LOGIN: @login,
        USER_PASSWORD: @password
      }

      @agent.post(@login_url, request_params, request_headers)
    end

    def run
      page = @agent.get("http://opt.onlychildren.ru/catalog/")

      category = ""
      page.search("div.item_block.col-md-6.col-sm-6 td.section_info > ul > li").each_with_index {|li, index|
        category = li.search("a > span").text.strip if li.attr("class").to_s == "name"
        subcategory = category + "~" + li.search("a").text.gsub(/<span.*?>[0-9+]<\/span>/, '') if li.attr("class").to_s == "sect"

        process_goods_list(li.search("a").attr('href').to_s, subcategory)
      }
    end

  end
end
