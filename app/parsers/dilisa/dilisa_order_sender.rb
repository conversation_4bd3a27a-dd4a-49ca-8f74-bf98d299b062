#encoding: utf-8
module Di<PERSON>a
  class DilisaOrderSender< Framework::OrderSender

    def clean
      basket_page = @agent.get('https://lalastyle.ru/personal/cart/')
      hrefs= basket_page.search('a').map{|a|a.attr('href').to_s}.select {|a| a.include? '?basketAction=delete'}
      puts hrefs.length
      hrefs.each {|a| @agent.get(a)}
    end

    def order(articul, colors)
      debug_func "#{articul}, #{colors}"
      (code,art) = articul.scan(/(\S+) (.*)/)[0]

      page_url=Cache.find_by_key("lala_page_#{code}")
      debug_func page_url.data

      if page_url.nil?
        add_error_csv_data('Cannot find page url', articul)
        return
      end

      debug_func page_url.data

      page=nil

      begin
        page=@agent.get(page_url.data)
      rescue Mechanize::ResponseCodeError => e
        if e.response_code == '404'
          add_error_csv_data('Page url open error', articul)
          return
        else
          raise
        end

      end


      script = page.body.match(/JCCatalogElement\((.+)\);/).captures[0].to_s
                   .gsub('\'', '"').gsub(' ', ' ') #.gsub(/\s/, '')
      # puts product_page.body
      data = JSON.parse(script)

      products=Hash.new
      if data.nil? or data['OFFERS'].nil?
        add_error_csv_data('Cannot find offers', articul)
        return
      end

      data['OFFERS'].each {|o|

        color_code=o['TREE']['PROP_245']
        size_code=o['TREE']['PROP_117']

        color=data['TREE_PROPS'].find{|p| p['ID']=='245'}['VALUES'][color_code]['NAME']
        size=data['TREE_PROPS'].find{|p| p['ID']=='117'}['VALUES'][size_code]['NAME']


        products[color]=Hash.new unless products[color]
        products[color][size]=o['ID']
      }

      #https://lalastyle.ru/ajax/addbasket.php?m_products%5B111812%5D=1&m_products%5B111813%5D=1

      req_data=[]
      colors.each { |color, sizes|
        sizes.each {|size,q|
          if products[color].nil?
            add_error_csv_data('Cannot find color', articul,color)
            next
          end
          if products[color][size].nil?
            add_error_csv_data('Cannot find size', articul,color,size)
            next
          end
          req_data<<["m_products[#{products[color][size]}]",q]
        }
      }

      p=@agent.get('https://lalastyle.ru/ajax/addbasket.php',req_data)
      sleep(0.5+rand(0.7))
      #debug_func "ADDED DATA: length #{ids.length}, total cost length #{cost.length}"
    end

    def authorize
      page = @agent.get('https://lalastyle.ru/auth/')

      login_form=page.form_with(:name=>'form_auth')
      login_form['USER_LOGIN']='<EMAIL>'
      login_form['USER_PASSWORD']='270279'
      page = @agent.submit(login_form, login_form.buttons[0])

    end

    def add_csv_data(fields)
      articul=fields['Артикул']
      color = fields['Наименование'].scan(/ цвет: (.*)/)[0]
      color=color[0].strip if color
      size = fields['Размер'].to_s
      debug_func fields['Артикул']
      debug_func fields['Участник']

      @csv_data[articul] = {} unless @csv_data.include? articul
      @csv_data[articul][color] = {} unless @csv_data[articul].include? color

      @csv_data[articul][color][size]+=1 if @csv_data[articul][color].include? size
      @csv_data[articul][color][size]=1 unless @csv_data[articul][color].include? size
    end
  end
end
