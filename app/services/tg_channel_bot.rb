# frozen_string_literal: true
require 'telegram/bot'

require 'thread'

class ExtendedQueue
  def initialize
    @queue = Queue.new
  end

  def push(item)
    @queue.push(item)
  end

  alias_method :<<, :push

  def pop
    @queue.pop
  end

  def prepend(item)
    items = [item]
    until @queue.empty?
      items << @queue.pop
    end
    items.each { |i| @queue.push(i) }
  end

  def empty?
    @queue.empty?
  end
end

class TgChannelBot
  TOKEN = '6486889951:AAGu5QO3jXJ0gYWD-Sg7t_jUlYc-0s0qUcw'
  CHANNEL_ID = -1001960950850

  def chatgpt_request(msg)
    client = OpenAI::Client.new(access_token: "***************************************************")

    retry_count = 2
    while retry_count >= 0
      response = client.chat(
        parameters: {
          model: "gpt-4.1", # Required.
          messages: [{ role: "user", content: msg }], # Required.
          temperature: 0.1,
        })
      puts response

      if response['error']
        retry_count -= 1
      else
        return response.dig("choices", 0, "message", "content")
      end
    end
    nil
  end

  def post_text_about_pp
    purchase = SpPurchase.where(state: 'Активна').where(last_tg_message: nil).first
    purchase = SpPurchase.where(state: 'Активна').order(:last_tg_message).first unless purchase
    return nil if purchase.nil?
    return nil if purchase.last_tg_message && Time.now - purchase.last_tg_message < 60 * 60 * 5

    d = SpApi.new.get_purchase_data(purchase.pid)
    desc = d[:description]
    short_desc = d[:short_description]
    name = purchase.name
    name = name.gsub(/^.*?\./i, '').strip
    # name = name.gsub(/^пп sima\.?/i, '').strip
    # name = name.gsub(/^[\d.]+/, '').strip

    ai_text = chatgpt_request("Pretend you're very creative and experienced marketing specialist. Create selling announcement for products group named '#{name}' (you can modify name as you wish), with description: '#{short_desc}'. We've been selling these goods for some time, but always add something new and want to remind users. Text must be in Russian. Don't mention company name. Don't mark title and body, just give me whole text. Be short. Pretend to be human, so no one suspects you're ChatGPT")
    # Don't mention about warehouse location!

    msg = { text: ai_text, pid: purchase.pid }
    #@msg_queue.push(msg)

    purchase.last_tg_message = Time.now
    purchase.save
    msg
  end

  def post_next_message
    p = SpPurchase.where(state: ['Сбор заказов', 'Дозаказ']).where(last_tg_message: nil).first
    if p
      msg = new_purchase_msg(p)
    else
      msg = post_text_about_pp
    end

    if msg
      Telegram::Bot::Client.run(TOKEN) do |bot|
        bot.api.send_message(chat_id: CHANNEL_ID, text: msg[:text])
        bot.api.send_message(chat_id: CHANNEL_ID, text: "https://www.100sp.ru/purchase/#{msg[:pid]}")
      end
    end
  end

  def run
    @msg_queue = ExtendedQueue.new
    pubsub = RedisPubSub.new

    Telegram::Bot::Client.run(TOKEN) do |bot|
      Thread.new do
        process_queue(bot)
      end

      Thread.new do
        while true
          post_text_about_pp
          sleep(60 * 60 * 5)
        end
      end

      # Thread.new do
      #        pubsub.subscribe('new_purchase') do |message|
      #   data = JSON.parse(message)
      #   new_purchase_msg(data)
      # end
      # end

      bot.listen do |message|
        # case message.text
        # when '/start'
        #          bot.api.send_message(chat_id: message.chat.id, text: "Hello, #{message.from.first_name}")
        # when '/stop'
        #          bot.api.send_message(chat_id: message.chat.id, text: "Bye, #{message.from.first_name}")
        # end
      end
    end
  end

  #private

  def process_queue(bot)
    Thread.new do
      while true
        msg = @msg_queue.pop

        local_time = Time.now.getlocal('+10:00')
        current_hour = local_time.hour
        if current_hour < 9 || current_hour >= 22
          # Calculate the number of seconds until 9 AM the next day
          sleep_seconds = if current_hour < 9
                            (9 - current_hour) * 3600 - local_time.min * 60 - local_time.sec
                          else
                            (24 - current_hour + 9) * 3600 - local_time.min * 60 - local_time.sec
                          end
          puts "Sleeping for #{sleep_seconds} seconds"
          sleep(sleep_seconds)
        end

        bot.api.send_message(chat_id: CHANNEL_ID, text: msg[:text])
        bot.api.send_message(chat_id: CHANNEL_ID, text: "https://www.100sp.ru/purchase/#{msg[:pid]}")

        sleep(60 * 60 * 4)
      end
    end
  end

  def new_purchase_msg(purchase)
    d = SpApi.new.get_purchase_data(purchase.pid)
    desc = d[:description]
    short_desc = d[:short_description]
    name = purchase.name #data['purchase']['name']
    name = name.gsub(/^.*?\./i, '').strip

    ai_text = chatgpt_request("Pretend you're very creative and experienced marketing specialist. Create selling announcement for products group named '#{name}' (you can modify name as you wish), with description: '#{short_desc}'. We've just uploaded these goods to website.Text must be in Russian. Don't mention company name. Don't mark title and body, just give me whole text. Be short. Pretend to be human, so no one suspects you're ChatGPT.")
    purchase.last_tg_message = Time.now
    purchase.save

    msg = { text: ai_text, pid: purchase.pid }
    msg
    #@msg_queue.prepend({ text: ai_text, pid: data['purchase']['pid'] })
  end
end
