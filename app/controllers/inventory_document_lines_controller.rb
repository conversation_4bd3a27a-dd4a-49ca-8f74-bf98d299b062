class InventoryDocumentLinesController < ApplicationController
  layout 'inventory'
  skip_before_action :verify_authenticity_token
  load_and_authorize_resource

  def destroy
    @doc=InventoryDocument.find(params[:id])
    raise 'Нельзя удалить проведенный документ' if @doc.posted

    @doc.inventory_document_lines.each {|l| l.destroy}
    @doc.destroy
    render json: {status: :ok}
  end

  # PATCH/PUT /collections/1
  # PATCH/PUT /collections/1.json
  def update
    #current_user=User.find 1

    respond_to do |format|
      line=InventoryDocumentLine.find(params[:id])
      raise 'Wrong user' unless line.inventory_document.user_id == current_user.id
      if params[:new_amount] and params[:check]
        line.amount_change = params[:new_amount].to_i - line.old_amount
        line.checked = params[:check]
        line.save
        format.json { render json: {result: 'ok'}}
      end
    end
  end

  def create
    @doc = InventoryDocument.find params[:doc_id]

    prod=params['product']

    line_type=InventoryDocumentLine.line_types[:relative]

    line=InventoryDocumentLine.create(inventory_product_id:prod['id'],size:'-',amount_change:0,old_amount:0,inventory_document:@doc,line_type:line_type)
    l=line.as_json(include: {product: {only: [:id, :sku, :name, :barcode]}})
    l[:new_amount]=0

    p=InventoryProduct.find prod['id']

    if p.inventory_pictures.first
      l['small_pic']=p.inventory_pictures.first.path.gsub('/mnt/spup2/images/','/img/100/')
      l['pic']=inventory_pictures.first.path.gsub('/mnt/spup2/images/','/img/640/')
      if  Rails.env.development?
        l['small_pic']='http://spup.primavon.ru'+l['small_pic']
        l['pic']='http://spup.primavon.ru'+l['pic']
      end
    end


    respond_to do |format|
      if @doc.save
        format.json { render json: {result:'ok', line:l}, status: :created }
      else
        format.json { render json: @doc.errors, status: :unprocessable_entity }
      end
    end
  end

  def fill_adjust
    @doc=InventoryDocument.find params[:id]
    @doc.populate_adjust(params[:purchase_id]) if @doc.doc_type=='adjust' and params[:purchase_id]
    render json: {result: 'ok'}
  end

  def fill_file
    @doc=InventoryDocument.find params[:id]
    @doc.fill_file(params[:file])
    render json: {result: 'ok'}
  end

  def inventory_document_params
    params.permit(:name,:doc_type,:purchase_id)
  end
end
