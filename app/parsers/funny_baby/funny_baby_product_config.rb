#encoding:utf-8
require_relative '../framework/site/product_config'

module FunnyBaby
  class FunnyBabyProductConfig < Framework::Site::ProductConfig
    def initialize
      super
      with_product_selector('.product_preview-preview > a')
          .with_articul(->(product_page) { prepare_string product_page.search('.product-sku_field').first.text })
          .with_name(->(product_page) { prepare_string product_page.search('h1').first.text })
          .with_price(->(product_page) {
            return 0
            # На случай упаковки
            price = prepare_string product_page.search('.prices-current').first.text
            price.to_f
          })
          .with_sizes(->(product_page) {
            product_page.search('#variant-select > option').map { |el| prepare_string el.text }
          })
          .with_description(->(product_page) {
            description = product_page.search('.product-description').first
            unless description.nil?
              description = description.search('p').map { |p| prepare_string HTMLEntities.new.decode(p.inner_html) }.join ' '
            end
            description = '' if description.nil?
            quantity = ''
            characteristics = product_page.search('.product-properties tr').map { |tr|
              title = prepare_string tr.search('.property-title').first.text
              values = prepare_string tr.search('.property-values').first.text
              lower = title.downcase.to_s
              quantity = values if lower.include? 'заказ' and values.to_i > 1
              (lower.include? 'заказ' or values == '-') ? '' : title + ' ' + values
            }
            characteristics.delete('')
            characteristics = characteristics.join '. '

            characteristics = "Количество в упаковке: #{quantity}. #{characteristics}" unless quantity.blank?

            if description.blank?
              prepare_string characteristics
            else
              prepare_string "#{characteristics}. #{description.titleize.to_s}."
            end
          })
          .with_images(->(product_page) {
            json = get_json_from_page(product_page)
            json['images'].map { |img| img['large_url'] }
          })
          .with_image_config(->(ic) { ic.with_default_url('http://www.funny-baby.ru/') })
          .with_category_type('2426')
          .with_category_type('573', 'новорожденным', 'комбинезоны')
          .with_category_type('572', 'новорожденным', 'боди')
          .with_category_type('574', 'новорожденным', 'ползунки')
          .with_category_type('571', 'новорожденным', 'распашонки')
          .with_category_type('599', 'новорожденным', 'кофточки')
          .with_category_type('1296', 'новорожденным', 'чепчики')
          .with_category_type('978', 'новорожденным', 'рукавички')
          .with_category_type('865', 'новорожденным', 'пинетки')
          .with_category_type('964', 'белье', 'майки')
          .with_category_type('2426', 'белье', 'трусы')
          .with_category_type('2426', 'белье', 'комплекты')
          .with_category_type('551', 'белье', 'пижамы')
          .with_category_type('552', 'девочек', 'платья')
          .with_category_type('546', 'девочек', 'туники')
          .with_category_type('556', 'девочек', 'футболки')
          .with_category_type('550', 'девочек', 'кофточки')
          .with_category_type('4680', 'девочек', 'брюки')
          .with_category_type('2951', 'девочек', 'костюмы')
          .with_category_type('558', 'девочек', 'юбки')
          .with_category_type('557', 'девочек', 'шорты')
          .with_category_type('568', 'мальчиков', 'футболки')
          .with_category_type('565', 'мальчиков', 'джемпера')
          .with_category_type('561', 'мальчиков', 'брюки')
          .with_category_type('2951', 'мальчиков', 'костюмы')
          .with_category_type('569', 'мальчиков', 'шорты')
    end

    def get_json_from_page(product_page)
      raw_json = product_page.search('section.section--content script').first.text.match(/product:\s+({.*})/).captures[0]
      result = JSON.parse raw_json.force_encoding("cp1251").encode("utf-8", undef: :replace)
      result['variants'].each{|v| v['sku'] = v['sku'].split[0].split('-')[0]}
      result
    end

    def create_new_product(product_page, link=nil)
      product = parse_product(product_page)
      product_json = get_json_from_page(product_page)

      product_json['variants'].group_by{|v|v['sku']}.each{|articul, var|
        json = {'pics'=>product.images, 'sizes'=>{}}
        product.articul = articul
        var.group_by{|v|v['price']}.each{|price, variants|
          sizes = variants.map{|v|v['option_values'].map{|o|o['title'] == '-' ? '' : o['title']}.join(' ').gsub('р. ', '').gsub('р.', '')}
          #sizes.each{|option| json['sizes'][option] = price}
          product.price = price.to_f
          product.sizes = sizes
          add_product(product)
          json['sizes'] = product.get_json_sizes
        }
        add_product_new(product, {articul=>json})
      }
    end
  end
end
