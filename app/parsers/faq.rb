#encoding: utf-8

require 'downloader'
require 'csv'
#require 'logger'
#require 'unicode_utils/downcase'

class Faq < Downloader

  def initialize
    @dlparams={}
    @allpics=Hash.new
    super
  end

  #http://faq-fashion.ru/index.php?route=product/quickview&product_id=7187

  def processPage(url,art)
    puts url

    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest'}
    page = @agent.get(url,ajax_headers)

    pics=page.search('.MagicToolboxSelectorsContainer a').map {|a| a.attr('href').to_s}

    if pics.size==0
      pics<< page.search('a.MagicZoomPlus')[0].attr('href').to_s
    end
    puts pics
    @allpics[art]=pics
  end

  def processCat(href)
    puts href

    done=false
   page = @agent.get(href)
    page.search('.products-block .product-block').each do |div|
      a=div.search('a.product-zoom')[0]
      return if a.nil?

      art=div.search('b')[0].text.gsub('Артикул: ','').downcase

      fname="#{@picpath}#{art}~0.jpg"
      next if File.exist? fname


      processPage(a.attr('onclick').to_s.gsub("ajaxDialog('",'').gsub("','quickBuy'); return false;",''),art)
    end
  end

  def process_csv
    page=@agent.get 'http://faq-fashion.ru/download/Bokova.csv'

    text=page.body.gsub('"',"'")
    conv=Encoding::Converter.new('Windows-1251','UTF-8')
    text=conv.convert(text)

    CSV.parse(text,:headers => true,:col_sep=>';') do |ar|
      art=ar['Артикул']
      puts art
      n=ar['Наименование']
      sizes=[]
      stocks=[]
      sizes=ar['Сетка'].split('*') if not ar['Сетка'].nil?
      stock=ar['Наличие'].split('*')  if not ar['Наличие'].nil?
      s=Hash[sizes.zip stock]
      s2=s.select {|k,v| v!='0'}.keys
      cat=ar['ГруппаНоменклатуры']
      price=(ar['ЦенаОптФакт'].to_f).ceil #*1.05
      next if s2.size==0

      pics=[]
      if @allpics.has_key? art.downcase
        @allpics[art.downcase].each_with_index { |url,i |
          pics<<savePic(url,"#{art}~#{i}",false)
        }

      end

      desc=''

      ar.to_h.keys[11..20].each_with_index {|k,i|
        #puts k
        if not ar[i+11].nil?
          if k=='Примечание'
            desc+=ar[i+11]
          else
            desc+="#{k}: #{ar[i+11]}. "
          end
        end
      }

      if pics.size>0
        catid='850'
        catid='19' if cat.include? 'Юбки'
        catid='20' if cat.include? 'Платья'
        catid='13' if cat.include? 'Водолазки'
        catid='24' if cat.include? 'Домашняя'
        catid='16' if cat.include? 'Жакеты'

        col=addCollection(cat,catid)
        addProduct(col,art,n,price,desc,s2,pics)
      end


      #puts art,n,s,s2,cat,price
    end

  end

  def run

    page = @agent.get('http://faq-fashion.ru/')

    page.search('ul#accordion li.haschild ul a').each do |a|
      processCat(a.attr('href').to_s+'?limit=240')
    end
    page.search('ul#accordion li:not(.haschild) a').each do |a|
      processCat(a.attr('href').to_s+'?limit=240')
    end

    process_csv

  end
end