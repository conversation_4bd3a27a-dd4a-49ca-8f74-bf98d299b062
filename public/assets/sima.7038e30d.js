import { q as inject, v as emptyRenderFn, f as computed, p as getCurrentInstance, z as layoutKey, g as hSlot, h, e as createComponent, x as onBeforeUnmount, aq as Transition, a1 as useDarkProps, a3 as useDark, i as ref, aK as onBeforeUpdate, w as watch, a9 as nextTick, aO as shouldI<PERSON>re<PERSON><PERSON>, bj as <PERSON><PERSON><PERSON><PERSON>, Q as QIcon, aW as QCheckbox, a6 as stopAndPrevent, U as withDirectives, bs as vShow, aX as injectProp } from "./index.f4b41209.js";
const usePageStickyProps = {
  position: {
    type: String,
    default: "bottom-right",
    validator: (v) => [
      "top-right",
      "top-left",
      "bottom-right",
      "bottom-left",
      "top",
      "right",
      "bottom",
      "left"
    ].includes(v)
  },
  offset: {
    type: Array,
    validator: (v) => v.length === 2
  },
  expand: Boolean
};
function usePageSticky() {
  const { props, proxy: { $q } } = getCurrentInstance();
  const $layout = inject(layoutKey, emptyRenderFn);
  if ($layout === emptyRenderFn) {
    console.error("QPageSticky needs to be child of QLayout");
    return emptyRenderFn;
  }
  const attach = computed(() => {
    const pos = props.position;
    return {
      top: pos.indexOf("top") !== -1,
      right: pos.indexOf("right") !== -1,
      bottom: pos.indexOf("bottom") !== -1,
      left: pos.indexOf("left") !== -1,
      vertical: pos === "top" || pos === "bottom",
      horizontal: pos === "left" || pos === "right"
    };
  });
  const top = computed(() => $layout.header.offset);
  const right = computed(() => $layout.right.offset);
  const bottom = computed(() => $layout.footer.offset);
  const left = computed(() => $layout.left.offset);
  const style = computed(() => {
    let posX = 0, posY = 0;
    const side = attach.value;
    const dir = $q.lang.rtl === true ? -1 : 1;
    if (side.top === true && top.value !== 0) {
      posY = `${top.value}px`;
    } else if (side.bottom === true && bottom.value !== 0) {
      posY = `${-bottom.value}px`;
    }
    if (side.left === true && left.value !== 0) {
      posX = `${dir * left.value}px`;
    } else if (side.right === true && right.value !== 0) {
      posX = `${-dir * right.value}px`;
    }
    const css = { transform: `translate(${posX}, ${posY})` };
    if (props.offset) {
      css.margin = `${props.offset[1]}px ${props.offset[0]}px`;
    }
    if (side.vertical === true) {
      if (left.value !== 0) {
        css[$q.lang.rtl === true ? "right" : "left"] = `${left.value}px`;
      }
      if (right.value !== 0) {
        css[$q.lang.rtl === true ? "left" : "right"] = `${right.value}px`;
      }
    } else if (side.horizontal === true) {
      if (top.value !== 0) {
        css.top = `${top.value}px`;
      }
      if (bottom.value !== 0) {
        css.bottom = `${bottom.value}px`;
      }
    }
    return css;
  });
  const classes = computed(
    () => `q-page-sticky row flex-center fixed-${props.position} q-page-sticky--${props.expand === true ? "expand" : "shrink"}`
  );
  function getStickyContent(slots) {
    const content = hSlot(slots.default);
    return h(
      "div",
      {
        class: classes.value,
        style: style.value
      },
      props.expand === true ? content : [h("div", content)]
    );
  }
  return {
    $layout,
    getStickyContent
  };
}
var QPageSticky = createComponent({
  name: "QPageSticky",
  props: usePageStickyProps,
  setup(_, { slots }) {
    const { getStickyContent } = usePageSticky();
    return () => getStickyContent(slots);
  }
});
var QSlideTransition = createComponent({
  name: "QSlideTransition",
  props: {
    appear: Boolean,
    duration: {
      type: Number,
      default: 300
    }
  },
  emits: ["show", "hide"],
  setup(props, { slots, emit }) {
    let animating = false, doneFn, element;
    let timer = null, timerFallback = null, animListener, lastEvent;
    function cleanup() {
      doneFn && doneFn();
      doneFn = null;
      animating = false;
      if (timer !== null) {
        clearTimeout(timer);
        timer = null;
      }
      if (timerFallback !== null) {
        clearTimeout(timerFallback);
        timerFallback = null;
      }
      element !== void 0 && element.removeEventListener("transitionend", animListener);
      animListener = null;
    }
    function begin(el, height, done) {
      if (height !== void 0) {
        el.style.height = `${height}px`;
      }
      el.style.transition = `height ${props.duration}ms cubic-bezier(.25, .8, .50, 1)`;
      animating = true;
      doneFn = done;
    }
    function end(el, event) {
      el.style.overflowY = null;
      el.style.height = null;
      el.style.transition = null;
      cleanup();
      event !== lastEvent && emit(event);
    }
    function onEnter(el, done) {
      let pos = 0;
      element = el;
      if (animating === true) {
        cleanup();
        pos = el.offsetHeight === el.scrollHeight ? 0 : void 0;
      } else {
        lastEvent = "hide";
        el.style.overflowY = "hidden";
      }
      begin(el, pos, done);
      timer = setTimeout(() => {
        timer = null;
        el.style.height = `${el.scrollHeight}px`;
        animListener = (evt) => {
          timerFallback = null;
          if (Object(evt) !== evt || evt.target === el) {
            end(el, "show");
          }
        };
        el.addEventListener("transitionend", animListener);
        timerFallback = setTimeout(animListener, props.duration * 1.1);
      }, 100);
    }
    function onLeave(el, done) {
      let pos;
      element = el;
      if (animating === true) {
        cleanup();
      } else {
        lastEvent = "show";
        el.style.overflowY = "hidden";
        pos = el.scrollHeight;
      }
      begin(el, pos, done);
      timer = setTimeout(() => {
        timer = null;
        el.style.height = 0;
        animListener = (evt) => {
          timerFallback = null;
          if (Object(evt) !== evt || evt.target === el) {
            end(el, "hide");
          }
        };
        el.addEventListener("transitionend", animListener);
        timerFallback = setTimeout(animListener, props.duration * 1.1);
      }, 100);
    }
    onBeforeUnmount(() => {
      animating === true && cleanup();
    });
    return () => h(Transition, {
      css: false,
      appear: props.appear,
      onEnter,
      onLeave
    }, slots.default);
  }
});
const tickStrategyOptions = ["none", "strict", "leaf", "leaf-filtered"];
var QTree = createComponent({
  name: "QTree",
  props: {
    ...useDarkProps,
    nodes: {
      type: Array,
      required: true
    },
    nodeKey: {
      type: String,
      required: true
    },
    labelKey: {
      type: String,
      default: "label"
    },
    childrenKey: {
      type: String,
      default: "children"
    },
    dense: Boolean,
    color: String,
    controlColor: String,
    textColor: String,
    selectedColor: String,
    icon: String,
    tickStrategy: {
      type: String,
      default: "none",
      validator: (v) => tickStrategyOptions.includes(v)
    },
    ticked: Array,
    expanded: Array,
    selected: {},
    noSelectionUnset: Boolean,
    defaultExpandAll: Boolean,
    accordion: Boolean,
    filter: String,
    filterMethod: Function,
    duration: {},
    noConnectors: Boolean,
    noTransition: Boolean,
    noNodesLabel: String,
    noResultsLabel: String
  },
  emits: [
    "update:expanded",
    "update:ticked",
    "update:selected",
    "lazyLoad",
    "afterShow",
    "afterHide"
  ],
  setup(props, { slots, emit }) {
    const { proxy } = getCurrentInstance();
    const { $q } = proxy;
    const isDark = useDark(props, $q);
    const lazy = ref({});
    const innerTicked = ref(props.ticked || []);
    const innerExpanded = ref(props.expanded || []);
    let blurTargets = {};
    onBeforeUpdate(() => {
      blurTargets = {};
    });
    const classes = computed(
      () => `q-tree q-tree--${props.dense === true ? "dense" : "standard"}` + (props.noConnectors === true ? " q-tree--no-connectors" : "") + (isDark.value === true ? " q-tree--dark" : "") + (props.color !== void 0 ? ` text-${props.color}` : "")
    );
    const hasSelection = computed(() => props.selected !== void 0);
    const computedIcon = computed(() => props.icon || $q.iconSet.tree.icon);
    const computedControlColor = computed(() => props.controlColor || props.color);
    const textColorClass = computed(() => props.textColor !== void 0 ? ` text-${props.textColor}` : "");
    const selectedColorClass = computed(() => {
      const color = props.selectedColor || props.color;
      return color ? ` text-${color}` : "";
    });
    const computedFilterMethod = computed(() => props.filterMethod !== void 0 ? props.filterMethod : (node, filter) => {
      const filt = filter.toLowerCase();
      return node[props.labelKey] && node[props.labelKey].toLowerCase().indexOf(filt) !== -1;
    });
    const meta = computed(() => {
      const meta2 = {};
      const travel = (node, parent) => {
        const tickStrategy = node.tickStrategy || (parent ? parent.tickStrategy : props.tickStrategy);
        const key = node[props.nodeKey], isParent = node[props.childrenKey] && Array.isArray(node[props.childrenKey]) && node[props.childrenKey].length !== 0, selectable = node.disabled !== true && hasSelection.value === true && node.selectable !== false, expandable = node.disabled !== true && node.expandable !== false, hasTicking = tickStrategy !== "none", strictTicking = tickStrategy === "strict", leafFilteredTicking = tickStrategy === "leaf-filtered", leafTicking = tickStrategy === "leaf" || tickStrategy === "leaf-filtered";
        let tickable = node.disabled !== true && node.tickable !== false;
        if (leafTicking === true && tickable === true && parent && parent.tickable !== true) {
          tickable = false;
        }
        let localLazy = node.lazy;
        if (localLazy === true && lazy.value[key] !== void 0 && Array.isArray(node[props.childrenKey]) === true) {
          localLazy = lazy.value[key];
        }
        const m = {
          key,
          parent,
          isParent,
          lazy: localLazy,
          disabled: node.disabled,
          link: node.disabled !== true && (selectable === true || expandable === true && (isParent === true || localLazy === true)),
          children: [],
          matchesFilter: props.filter ? computedFilterMethod.value(node, props.filter) : true,
          selected: key === props.selected && selectable === true,
          selectable,
          expanded: isParent === true ? innerExpanded.value.includes(key) : false,
          expandable,
          noTick: node.noTick === true || strictTicking !== true && localLazy && localLazy !== "loaded",
          tickable,
          tickStrategy,
          hasTicking,
          strictTicking,
          leafFilteredTicking,
          leafTicking,
          ticked: strictTicking === true ? innerTicked.value.includes(key) : isParent === true ? false : innerTicked.value.includes(key)
        };
        meta2[key] = m;
        if (isParent === true) {
          m.children = node[props.childrenKey].map((n) => travel(n, m));
          if (props.filter) {
            if (m.matchesFilter !== true) {
              m.matchesFilter = m.children.some((n) => n.matchesFilter);
            } else if (m.noTick !== true && m.disabled !== true && m.tickable === true && leafFilteredTicking === true && m.children.every((n) => n.matchesFilter !== true || n.noTick === true || n.tickable !== true) === true) {
              m.tickable = false;
            }
          }
          if (m.matchesFilter === true) {
            if (m.noTick !== true && strictTicking !== true && m.children.every((n) => n.noTick) === true) {
              m.noTick = true;
            }
            if (leafTicking) {
              m.ticked = false;
              m.indeterminate = m.children.some((node2) => node2.indeterminate === true);
              m.tickable = m.tickable === true && m.children.some((node2) => node2.tickable);
              if (m.indeterminate !== true) {
                const sel = m.children.reduce((acc, meta3) => meta3.ticked === true ? acc + 1 : acc, 0);
                if (sel === m.children.length) {
                  m.ticked = true;
                } else if (sel > 0) {
                  m.indeterminate = true;
                }
              }
              if (m.indeterminate === true) {
                m.indeterminateNextState = m.children.every((meta3) => meta3.tickable !== true || meta3.ticked !== true);
              }
            }
          }
        }
        return m;
      };
      props.nodes.forEach((node) => travel(node, null));
      return meta2;
    });
    watch(() => props.ticked, (val) => {
      innerTicked.value = val;
    });
    watch(() => props.expanded, (val) => {
      innerExpanded.value = val;
    });
    function getNodeByKey(key) {
      const reduce = [].reduce;
      const find = (result, node) => {
        if (result || !node) {
          return result;
        }
        if (Array.isArray(node) === true) {
          return reduce.call(Object(node), find, result);
        }
        if (node[props.nodeKey] === key) {
          return node;
        }
        if (node[props.childrenKey]) {
          return find(null, node[props.childrenKey]);
        }
      };
      return find(null, props.nodes);
    }
    function getTickedNodes() {
      return innerTicked.value.map((key) => getNodeByKey(key));
    }
    function getExpandedNodes() {
      return innerExpanded.value.map((key) => getNodeByKey(key));
    }
    function isExpanded(key) {
      return key && meta.value[key] ? meta.value[key].expanded : false;
    }
    function collapseAll() {
      if (props.expanded !== void 0) {
        emit("update:expanded", []);
      } else {
        innerExpanded.value = [];
      }
    }
    function expandAll() {
      const expanded = [];
      const travel = (node) => {
        if (node[props.childrenKey] && node[props.childrenKey].length !== 0) {
          if (node.expandable !== false && node.disabled !== true) {
            expanded.push(node[props.nodeKey]);
            node[props.childrenKey].forEach(travel);
          }
        }
      };
      props.nodes.forEach(travel);
      if (props.expanded !== void 0) {
        emit("update:expanded", expanded);
      } else {
        innerExpanded.value = expanded;
      }
    }
    function setExpanded(key, state, node = getNodeByKey(key), m = meta.value[key]) {
      if (m.lazy && m.lazy !== "loaded") {
        if (m.lazy === "loading") {
          return;
        }
        lazy.value[key] = "loading";
        if (Array.isArray(node[props.childrenKey]) !== true) {
          node[props.childrenKey] = [];
        }
        emit("lazyLoad", {
          node,
          key,
          done: (children) => {
            lazy.value[key] = "loaded";
            node[props.childrenKey] = Array.isArray(children) === true ? children : [];
            nextTick(() => {
              const localMeta = meta.value[key];
              if (localMeta && localMeta.isParent === true) {
                localSetExpanded(key, true);
              }
            });
          },
          fail: () => {
            delete lazy.value[key];
            if (node[props.childrenKey].length === 0) {
              delete node[props.childrenKey];
            }
          }
        });
      } else if (m.isParent === true && m.expandable === true) {
        localSetExpanded(key, state);
      }
    }
    function localSetExpanded(key, state) {
      let target = innerExpanded.value;
      const shouldEmit = props.expanded !== void 0;
      if (shouldEmit === true) {
        target = target.slice();
      }
      if (state) {
        if (props.accordion) {
          if (meta.value[key]) {
            const collapse = [];
            if (meta.value[key].parent) {
              meta.value[key].parent.children.forEach((m) => {
                if (m.key !== key && m.expandable === true) {
                  collapse.push(m.key);
                }
              });
            } else {
              props.nodes.forEach((node) => {
                const k = node[props.nodeKey];
                if (k !== key) {
                  collapse.push(k);
                }
              });
            }
            if (collapse.length !== 0) {
              target = target.filter((k) => collapse.includes(k) === false);
            }
          }
        }
        target = target.concat([key]).filter((key2, index, self) => self.indexOf(key2) === index);
      } else {
        target = target.filter((k) => k !== key);
      }
      if (shouldEmit === true) {
        emit("update:expanded", target);
      } else {
        innerExpanded.value = target;
      }
    }
    function isTicked(key) {
      return key && meta.value[key] ? meta.value[key].ticked : false;
    }
    function setTicked(keys, state) {
      let target = innerTicked.value;
      const shouldEmit = props.ticked !== void 0;
      if (shouldEmit === true) {
        target = target.slice();
      }
      if (state) {
        target = target.concat(keys).filter((key, index, self) => self.indexOf(key) === index);
      } else {
        target = target.filter((k) => keys.includes(k) === false);
      }
      if (shouldEmit === true) {
        emit("update:ticked", target);
      }
    }
    function getSlotScope(node, meta2, key) {
      const scope = { tree: proxy, node, key, color: props.color, dark: isDark.value };
      injectProp(
        scope,
        "expanded",
        () => {
          return meta2.expanded;
        },
        (val) => {
          val !== meta2.expanded && setExpanded(key, val);
        }
      );
      injectProp(
        scope,
        "ticked",
        () => {
          return meta2.ticked;
        },
        (val) => {
          val !== meta2.ticked && setTicked([key], val);
        }
      );
      return scope;
    }
    function getChildren(nodes) {
      return (props.filter ? nodes.filter((n) => meta.value[n[props.nodeKey]].matchesFilter) : nodes).map((child) => getNode(child));
    }
    function getNodeMedia(node) {
      if (node.icon !== void 0) {
        return h(QIcon, {
          class: "q-tree__icon q-mr-sm",
          name: node.icon,
          color: node.iconColor
        });
      }
      const src = node.img || node.avatar;
      if (src) {
        return h("img", {
          class: `q-tree__${node.img ? "img" : "avatar"} q-mr-sm`,
          src
        });
      }
    }
    function onShow() {
      emit("afterShow");
    }
    function onHide() {
      emit("afterHide");
    }
    function getNode(node) {
      const key = node[props.nodeKey], m = meta.value[key], header = node.header ? slots[`header-${node.header}`] || slots["default-header"] : slots["default-header"];
      const children = m.isParent === true ? getChildren(node[props.childrenKey]) : [];
      const isParent = children.length !== 0 || m.lazy && m.lazy !== "loaded";
      let body = node.body ? slots[`body-${node.body}`] || slots["default-body"] : slots["default-body"];
      const slotScope = header !== void 0 || body !== void 0 ? getSlotScope(node, m, key) : null;
      if (body !== void 0) {
        body = h("div", { class: "q-tree__node-body relative-position" }, [
          h("div", { class: textColorClass.value }, [
            body(slotScope)
          ])
        ]);
      }
      return h("div", {
        key,
        class: `q-tree__node relative-position q-tree__node--${isParent === true ? "parent" : "child"}`
      }, [
        h("div", {
          class: "q-tree__node-header relative-position row no-wrap items-center" + (m.link === true ? " q-tree__node--link q-hoverable q-focusable" : "") + (m.selected === true ? " q-tree__node--selected" : "") + (m.disabled === true ? " q-tree__node--disabled" : ""),
          tabindex: m.link === true ? 0 : -1,
          ariaExpanded: children.length > 0 ? m.expanded : null,
          role: "treeitem",
          onClick: (e) => {
            onClick(node, m, e);
          },
          onKeypress(e) {
            if (shouldIgnoreKey(e) !== true) {
              if (e.keyCode === 13) {
                onClick(node, m, e, true);
              } else if (e.keyCode === 32) {
                onExpandClick(node, m, e, true);
              }
            }
          }
        }, [
          h("div", {
            class: "q-focus-helper",
            tabindex: -1,
            ref: (el) => {
              blurTargets[m.key] = el;
            }
          }),
          m.lazy === "loading" ? h(QSpinner, {
            class: "q-tree__spinner",
            color: computedControlColor.value
          }) : isParent === true ? h(QIcon, {
            class: "q-tree__arrow" + (m.expanded === true ? " q-tree__arrow--rotate" : ""),
            name: computedIcon.value,
            onClick(e) {
              onExpandClick(node, m, e);
            }
          }) : null,
          m.hasTicking === true && m.noTick !== true ? h(QCheckbox, {
            class: "q-tree__tickbox",
            modelValue: m.indeterminate === true ? null : m.ticked,
            color: computedControlColor.value,
            dark: isDark.value,
            dense: true,
            keepColor: true,
            disable: m.tickable !== true,
            onKeydown: stopAndPrevent,
            "onUpdate:modelValue": (v) => {
              onTickedClick(m, v);
            }
          }) : null,
          h("div", {
            class: "q-tree__node-header-content col row no-wrap items-center" + (m.selected === true ? selectedColorClass.value : textColorClass.value)
          }, [
            header ? header(slotScope) : [
              getNodeMedia(node),
              h("div", node[props.labelKey])
            ]
          ])
        ]),
        isParent === true ? props.noTransition === true ? m.expanded === true ? h("div", {
          class: "q-tree__node-collapsible" + textColorClass.value,
          key: `${key}__q`
        }, [
          body,
          h("div", {
            class: "q-tree__children" + (m.disabled === true ? " q-tree__node--disabled" : ""),
            role: "group"
          }, children)
        ]) : null : h(QSlideTransition, {
          duration: props.duration,
          onShow,
          onHide
        }, () => withDirectives(
          h("div", {
            class: "q-tree__node-collapsible" + textColorClass.value,
            key: `${key}__q`
          }, [
            body,
            h("div", {
              class: "q-tree__children" + (m.disabled === true ? " q-tree__node--disabled" : ""),
              role: "group"
            }, children)
          ]),
          [[vShow, m.expanded]]
        )) : body
      ]);
    }
    function blur(key) {
      const blurTarget = blurTargets[key];
      blurTarget && blurTarget.focus();
    }
    function onClick(node, meta2, e, keyboard) {
      keyboard !== true && meta2.selectable !== false && blur(meta2.key);
      if (hasSelection.value && meta2.selectable) {
        if (props.noSelectionUnset === false) {
          emit("update:selected", meta2.key !== props.selected ? meta2.key : null);
        } else if (meta2.key !== props.selected) {
          emit("update:selected", meta2.key === void 0 ? null : meta2.key);
        }
      } else {
        onExpandClick(node, meta2, e, keyboard);
      }
      if (typeof node.handler === "function") {
        node.handler(node);
      }
    }
    function onExpandClick(node, meta2, e, keyboard) {
      if (e !== void 0) {
        stopAndPrevent(e);
      }
      keyboard !== true && meta2.selectable !== false && blur(meta2.key);
      setExpanded(meta2.key, !meta2.expanded, node, meta2);
    }
    function onTickedClick(meta2, state) {
      if (meta2.indeterminate === true) {
        state = meta2.indeterminateNextState;
      }
      if (meta2.strictTicking) {
        setTicked([meta2.key], state);
      } else if (meta2.leafTicking) {
        const keys = [];
        const travel = (meta3) => {
          if (meta3.isParent) {
            if (state !== true && meta3.noTick !== true && meta3.tickable === true) {
              keys.push(meta3.key);
            }
            if (meta3.leafTicking === true) {
              meta3.children.forEach(travel);
            }
          } else if (meta3.noTick !== true && meta3.tickable === true && (meta3.leafFilteredTicking !== true || meta3.matchesFilter === true)) {
            keys.push(meta3.key);
          }
        };
        travel(meta2);
        setTicked(keys, state);
      }
    }
    props.defaultExpandAll === true && expandAll();
    Object.assign(proxy, {
      getNodeByKey,
      getTickedNodes,
      getExpandedNodes,
      isExpanded,
      collapseAll,
      expandAll,
      setExpanded,
      isTicked,
      setTicked
    });
    return () => {
      const children = getChildren(props.nodes);
      return h(
        "div",
        {
          class: classes.value,
          role: "tree"
        },
        children.length === 0 ? props.filter ? props.noResultsLabel || $q.lang.tree.noResults : props.noNodesLabel || $q.lang.tree.noNodes : children
      );
    };
  }
});
function transformCatArrayToTree(data) {
  let ret = [];
  let map = {};
  for (let i = 0; i < data.length; i += 1) {
    map[data[i].id] = i;
    data[i].children = [];
    data[i].label = data[i].name;
    data[i].product_count = data[i].item_count;
  }
  for (let i = 0; i < data.length; i += 1) {
    let node = data[i];
    if (node.parent_id) {
      if (data[map[node.parent_id]])
        data[map[node.parent_id]].children.push(node);
    } else {
      ret.push(node);
    }
  }
  return ret;
}
export { QPageSticky as Q, QTree as a, transformCatArrayToTree as t };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
