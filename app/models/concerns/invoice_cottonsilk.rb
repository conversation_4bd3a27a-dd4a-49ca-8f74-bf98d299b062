#encoding: utf-8

module InvoiceCottonsilk
  extend ActiveSupport::<PERSON>cern

  def unprocessed_invoice?
    true
  end

  def read_invoice(file)
    ret=[]
    book = RubyXL::Parser.parse(file)
    sheet = book.worksheets[0]


    code_num=1
    name_num=0
    size_num=2
    q_num=3
    price_num=4


    in_data=false
    #sheet.sheet_data.rows.each_with_index do |row, k|
    sheet.each do |row|

      next if row.nil?

      next if row[code_num]&.value.blank? #|| row[code_num].value.blank?


      if row[code_num].value=='Модель'
        in_data=true
        next
      end

      next unless in_data

      art=row[code_num].value.to_s
      name=row[name_num].value
      q=row[q_num].value.to_i
      size=row[size_num].value.to_i

      price=row[price_num].value.to_s if price_num

      ret<<{'Артикул'=>art,'Название'=>name,'Коли<PERSON>ество'=>q, 'Цена закуп'=>price, 'Размер'=>size}
    end
    ret
  end


  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)

    return false if fields['Примечания к заказам'].to_s!='' or line['Название'].nil? or line['Артикул'].nil?

    site_art=fields['Артикул'].gsub(/--\d+$/,'').gsub(/ БР$/,'').gsub(/^\d+ /,'').upcase.strip.gsub(/[() ]/,'')

    size=line['Размер'].to_s

    if /[^:]+:(.*)/ =~ size
      size=$1
    end
    size=size.gsub(',','.').strip

    name=line['Название'].strip.upcase.gsub(/^\d+ /,'').gsub(/[() ]/,'')
    art=line['Артикул'].upcase.strip.gsub(/[() ]/,'')

    art==site_art  and size==fields['Размер']
  end

end