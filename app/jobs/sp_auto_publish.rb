# frozen_string_literal: true

class SpAutoPublish
  include SpAuthorize

  def initialize
    @scheduled_publishes = {}
    @publish_times = {}

    @log = Logger.new('log/sp_auto_publish.log')
  end
  def load_publish_times
    page = @agent.get 'https://www.100sp.ru/org/purchase/index?mode=active'
    page.search('tr.purchases-table__row').each do |tr|
      state = tr.at('td.purchases-table-col--status').text.strip
      next unless state == 'Активна'
      pid = tr.attr('data-purchase-id').to_i
      publish_time = tr.at('span[data-datetime]').attr('data-datetime')
      d = DateTime.parse(publish_time)
      @publish_times[pid] = d
    end
  end

  def load_scheduled_publish(pid)
    page = @agent.get "https://www.100sp.ru/org/purchase/edit?pid=#{pid}"

    enabled = page.at('#PurchaseEditForm_autoPublish').attr('checked').present?
    return unless enabled

    date = page.at('#PurchaseEditForm_autoPublishDate').attr('value')
    time = page.at('#PurchaseEditForm_autoPublishTime').attr('value')

    dt = DateTime.parse("#{date}T#{time}+10")
    @scheduled_publishes[pid] = dt
  end

  def load_scheduled_publishes
    page = @agent.get 'https://www.100sp.ru/org/purchase/index?mode=presale'

    page.search('tr.purchases-table__row').each do |tr|
      pid = tr.attr('data-purchase-id').to_i
      load_scheduled_publish(pid)
      sleep(0.43)
    end
  end

  def have_purchase_in_new_list?
    page = @agent.get 'https://www.100sp.ru/vladivostok'
    orgs = page.search('#recently span.org-name').map { |span| span.text.strip }
    @log.info "orgs: #{orgs}"
    orgs.include?('Moral')
  end

  def republish(pid)
    @log.info "republish #{pid}"
    @agent.post "https://www.100sp.ru/org/purchase/republish/index?pid=#{pid}"
  end

  def run

    time = Time.now.getlocal('+10:00')
    h = time.hour
    return if h < 11 || h > 22

    authorize

    return if have_purchase_in_new_list?

    load_scheduled_publishes
    @log.info "scheduled publishes: #{@scheduled_publishes}"

    times = @scheduled_publishes.values.sort
    return if times[0] && times[0].to_time - DateTime.now <= 1.hour

    load_publish_times
    @log.info "publish times: #{@publish_times}"

    need_republish = @publish_times.select { |pid, dt| dt < DateTime.now - 4.days }
    @log.info "need republish: #{need_republish}"

    return unless need_republish.count > 0

    republish(need_republish.keys[-1])

  end
end
