class Api3::CollectionsController < Api3::BaseController
  before_action :set_collection, only: [:show, :edit, :update, :destroy, :removetag, :addtag, :move_collections, :move_products, :items]

  load_and_authorize_resource

  # GET /collections
  # GET /collections.json
  def index
  end

  # GET /collections/1
  # GET /collections/1.json
  def show
    @products = @collection.products.order('disabled,pos,updated_at')
    @products = @products.map { |p| p.transform_product_to_json }
    render json: { products: @products }
  end

  def items
    tpl = 'collections/show'
    tpl = 'collections/collection_image_chooser' if params[:tpl] == 'image_chooser'

    @collection = Collection.eager_load(:products).where(id: params[:id]).first
    @products = @collection.products

    if params[:no_pics]
      @products = @collection.products.select { |p| p.pictures.none? { |i| i.active? } }
    end
    respond_to do |format|
      format.html { render layout: false, template: tpl } # Add this line to you respond_to block
    end
  end

  # GET /collections/new
  def new
    @collection = Collection.new
  end

  # GET /collections/1/edit
  def edit
  end

  # POST /collections
  # POST /collections.json
  def create
    @collection = Collection.new(collection_params)

    respond_to do |format|
      if @collection.save
        format.html { redirect_to @collection, notice: 'Collection was successfully created.' }
        format.json { render action: 'show', status: :created, location: @collection }
      else
        format.html { render action: 'new' }
        format.json { render json: @collection.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /collections/1
  # PATCH/PUT /collections/1.json
  def update
    respond_to do |format|
      if @collection.update(collection_params)
        format.html { redirect_to @collection, notice: 'Collection was successfully updated.' }
        format.json { head :no_content }
      else
        format.html { render action: 'edit' }
        format.json { render json: @collection.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /collections/1
  # DELETE /collections/1.json
  def destroy
    CollectionRename.where(purchase_id: @collection.purchase_id, new_name: @collection.name).delete_all
    @collection.destroy
    respond_to do |format|
      format.html { redirect_to collections_url }
      format.json { head :no_content }
    end
  end

  def removetag
    respond_to do |format|
      if can? :read, @collection
        @collection.remove_tag(params[:tag])
        format.json { render json: { status: 'ok' } }
      else
        format.json { render json: { status: 'error' } }

      end

    end

  end

  def addtags
    params['col_ids'].each do |col_id|
      col = Collection.find col_id
      if can? :read, col
        col.add_tag(params[:tag])
      end
    end

    respond_to do |format|
      format.json { render json: { status: 'ok' } }
    end
  end
  def setgroup
    params['col_ids'].each do |col_id|
      col = Collection.find col_id
      if can? :write, col
        col.grp=params['group']
        col.save
      end
    end

    respond_to do |format|
      format.json { render json: { status: 'ok' } }
    end
  end

  def move_collections
    params[:collections].each do |cid|
      @purchase = Collection.find(cid).purchase unless @purchase

      CollectionsProduct.where(collection_id: cid).each { |cp|
        CollectionsProduct.find_or_create_by!(product_id: cp.product_id, collection_id: @collection.id)

      }
      CollectionsProduct.where(collection_id: cid).delete_all

      #Product.where(collection_id:cid).update_all(collection_id:@collection.id)
      #Product.where(collection_id:@collection.id,category: nil).update_all(category:@collection.coltype)
    end

    @collection_renames = {}
    @purchase.collection_renames.each do |r|
      @collection_renames[r.new_name] = [] if @collection_renames[r.new_name].nil?
      @collection_renames[r.new_name] << r.old_name
    end

    respond_to do |format|
      format.html {

        render(@purchase.ordered_collections)
      }
    end
  end

  def move_products
    cid=params[:fromCollection]
    @purchase = Collection.find(cid).purchase unless @purchase

    unless can? :read, @purchase
      raise "Access error"
    end

    params[:products].each do |pid|
      next if cid.to_i==@collection.id
      CollectionsProduct.where(collection_id: cid, product_id: pid).delete_all
      CollectionsProduct.find_or_create_by!(product_id: pid, collection_id: @collection.id)
    end
    @collection.reload

    col1=Collection.find(cid)
    col1=col1.as_json(include: :products).merge({ active_products: col1.active_products_count, total_products: col1.products.count })
    col2=@collection
    col2=col2.as_json(include: :products).merge({ active_products: col2.active_products_count, total_products: col2.products.count })

    respond_to do |format|
      format.json {

        render json: {ok: true, col1:col1,col2:col2}
      }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_collection
    @collection = Collection.find(params[:id])
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def collection_params
    params[:collection, :tag, :collections, :products, :old_cid]
  end
end
