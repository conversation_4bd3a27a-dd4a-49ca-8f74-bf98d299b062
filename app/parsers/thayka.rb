#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Thayka < Downloader

  def initialize
    @dlparams={}
    super
  end


  def processPage(url,cat)
    tries ||= 2

    @log.info('page '+url)

    puts url

    page = @agent.get(url)

    art=page.search('input[name=productID]').attr('value').to_s
    puts art

    price=page.search('input[name=productID]')[0].parent.search('input')[1].attr('value')

    name=page.search("h1.main-title").text.strip

    desc=page.search(".item_descr").text.strip

    pic=page.search('.item_pic a')[0].attr('href')

    savePic(pic,art,false)

    col=addCollection(cat,'4685')
    addProduct(col,art,name,price,desc,[],[art])


  rescue Mechanize::Error => e
    puts e
    @log.info e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(href)
    tries ||= 2
    puts href
    @log.info('cat '+href)

    page = @agent.get(href)

    catName=page.search('.sidebar li.thiscat a').text.strip

    page.search(".pr_brief_info a").each do |a|
      processPage(a.attr('href'),catName)
    end

  rescue Mechanize::Error => e
    @log.info e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def run
    page = @agent.get('http://www.thayka.ru/')

    page.search(".sidebar li a").each do |a|
      processCat(a['href']+'all/')
    end

  end
  #handle_asynchronously :run

end