#encoding: utf-8

module InvoiceAvelon
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    site_art = fields['Артикул'].split(' ')[1].gsub(/--\d+$/,'')
    site_size = fields['Размер']

    file_art = line['Название'].split(' ')[0]
    file_size = '-'

    if /\((.+)\)$/ =~ line['Название']
      file_size = $1
    end

    site_art == file_art && site_size == file_size
  end

end